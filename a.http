
# 设置 scid
@scid = sc047f71a5d4ef1134

# 设置 organizationId
@organizationId = 1711957904168636416

# 设置 Authorization
@Authorization = Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbmlkIjoiYzM5NzVmM2E3Yzc0NGJhYzkyOTMwZjA0MmQ3NjIzZDAiLCJ1c2VyX25hbWUiOiIxNzEyMjI3MzUyNzU4MDE4MDQ4IiwicGlkIjoiIiwiaXNCaW5kV3giOmZhbHNlLCJ0eXBlIjoxLCJhdXRob3JpdGllcyI6WyJzeXM6dXNlcjp1cGRhdGUiLCJzaG93Onp6Z2xkYXRhIiwic3lzOnVzZXI6aW5kZXgiLCJzeXM6dXNlcjpjcmVhdGUiXSwiY2xpZW50X2lkIjoic2Nsb3VkIiwicmVhbG5hbWUiOiLpu4QqKiIsInVpZCI6IjE3MTIyMjczNTMzNzg3NzUwNDAiLCJ1c2VyQXV0aFR5cGUiOiJzeXMiLCJwaG9uZSI6IjEzOCoqKio5NjM1Iiwic2NvcGUiOlsiYWxsIl0sImlkIjoiMTcxMjIyNzM1MzM3ODc3NTA0MCIsImV4cCI6MTc1MzI5MTc4OSwianRpIjoiYmUwMWU4NGItOTc4Yy00ODQ3LWEwZmYtZGMwNjRjNmI1ZjkxIiwic2NpZCI6InNjMDQ3ZjcxYTVkNGVmMTEzNCIsImpvYk51bWJlciI6IlNDVDE3NTI1NDk1MTgyMDgxMTciLCJ1c2VybmFtZSI6IjE3MTIyMjczNTI3NTgwMTgwNDgifQ.NtPooA3J5DOiZaGw3LvSDP2IirNndarI4oUQdPOqd1M

### 获取事件列表
POST /aiot/event/mobile/index HTTP/1.1
Host: 127.0.0.1:12245
Content-Type: application/json
scid: {{scid}}
organizationId: 1708599276551712768
Authorization: {{Authorization}}

{
    "size": 1,
    "page": 0,
    "regionPid": "80",
    "regionId": "107",
    "regionCid": "832789087816818692",
    "projectId": "1710841737840631808"
}

### 获取统计数据

POST /report/project/statistics HTTP/1.1
Host: 127.0.0.1:12245
Content-Type: application/json
Content-Length: 16
scid: {{scid}}
organizationId: {{organizationId}}
Authorization: {{Authorization}}
{}


### 获取区域统计数据
POST /sporadic/project/region/statistics HTTP/1.1
Host: 127.0.0.1:12245
Content-Type: application/json
scid: {{scid}}
organizationId: 1707848681287663616
Authorization: {{Authorization}}

{
    "regionPid": "83"
    # "regionId": "832789087816818897"
}