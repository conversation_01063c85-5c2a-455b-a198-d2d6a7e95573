### 邀请码功能测试

### 1. 生成邀请码
POST http://localhost:12245/sporadic/project/invite-code/generate
Content-Type: application/json

{
  "id": "your-project-id-here"
}

### 2. 通过邀请码获取工程信息
POST http://localhost:12245/sporadic/project/invite-code/project
Content-Type: application/json

{
  "inviteCode": "your-invite-code-here"
}

### 3. 查看工程详情（用于获取项目ID）
POST http://localhost:12245/sporadic/project/show
Content-Type: application/json

{
  "id": "your-project-id-here"
}

### 4. 查询工程列表（用于获取项目ID）
POST http://localhost:12245/sporadic/project/index
Content-Type: application/json

{
  "page": 0,
  "size": 10
}

### 5. 绑定邀请码
POST http://localhost:12245/sporadic/project/invite-code/bind
Content-Type: application/json

{
  "inviteCode": "your-invite-code-here"
}

### 6. 查询当前成员关联的项目列表
POST http://localhost:12245/engineering/members/project/page
Content-Type: application/json

{
  "id": "member-id-here",
  "page": 0,
  "size": 10
}
