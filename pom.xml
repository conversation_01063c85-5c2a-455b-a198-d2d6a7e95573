<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cn.shencom.scloud.server</groupId>
        <artifactId>scloud-server</artifactId>
        <version>2.0.1-SNAPSHOT</version>
    </parent>

    <groupId>cn.shencom</groupId>
    <artifactId>sporadic-project</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <name>sporadic project</name>
    <description>sporadic-project</description>

    <properties>
        <java.version>1.8</java.version>
        <elasticsearch.version>7.17.1</elasticsearch.version>
        <elasticsearch>7.17.1</elasticsearch>
    </properties>

    <dependencies>
        <dependency>
            <groupId>cn.shencom.scloud.common</groupId>
            <artifactId>scloud-es-util</artifactId>
            <version>1.0.3-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.36</version>
        </dependency>

        <dependency>
            <groupId>cn.shencom.scloud.common</groupId>
            <artifactId>scloud-security-service</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.shencom.scloud.common</groupId>
            <artifactId>redis-lock</artifactId>
            <version>2.1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>cn.shencom.scloud.api</groupId>
            <artifactId>scloud-api-uaa</artifactId>
            <version>2.0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>cn.shencom.scloud.api</groupId>
            <artifactId>scloud-api-file</artifactId>
            <version>2.0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.slyak</groupId>
            <artifactId>spring-data-jpa-extra</artifactId>
            <version>3.3.0.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
            <version>2.3.0</version>
        </dependency>

        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-client</artifactId>
            <version>4.9.3</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.shencom.scloud</groupId>
            <artifactId>scloud-log-starter</artifactId>
            <version>2.0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>cn.shencom.scloud.common</groupId>
            <artifactId>scloud-common-cipher</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-mock</artifactId>
            <version>2.0.8</version>
        </dependency>

        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.11.0</version>
        </dependency>

    </dependencies>
</project>

