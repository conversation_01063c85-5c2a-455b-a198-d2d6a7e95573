通过生成邀请码可以查看该工程的信息

> 数据表 `sql/db_tn_sc047f71a5d4ef1134.sql`

工程数据表: `sporadic_project`


## 生成邀请码
邀请码生成权限说明：该工程绑定的施工负责人，建设方（业主）有生成邀请码的权限

1. 邀请码：大小写字母+数字混合。随机10位数
2. 每个工程的邀请码是变动的，每点击一次邀请码，生成一个准一码
3. 该码的有效期限为1周。过期后扫码提示该邀请码无效。

邀请码实现简单逻辑
1. 使用 redis 存储，通过邀请码作为 key，工程 id 作为 value
2. 存储时间为一周，一周后过期


业务逻辑实现文件：`src/main/java/cn/shencom/server/service/impl/SporadicProjectServiceImpl.java`


## 绑定邀请码

邀请码使用权限说明：施工负责人 施工工人 建筑方（业主） 有绑定工程权限。 绑定后可在我的工程查看。

绑定前需要判断邀请码是否过期，是否有权限进行绑定

创建关联关系的业务实现文件：`src/main/java/cn/shencom/server/service/impl/EngineeringMembersServiceImpl.java`

数据表：

`engineering_members`
`engineering_members_project_relate`
`engineering_members_type`