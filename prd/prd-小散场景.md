# 小散场景
> 违规告警统计数据

## 原型图
![原型图](./小散场景.png)

url: `./prd/小散场景.png`

## 功能分析

根据产品原型图，此页面为“小散场景”下的“违规告警统计数据”展示页面。

### 页面元素
1.  **标题**: “小散场景 违规告警统计数据”
2.  **时间筛选**:
    -   提供一个日期范围选择器，用于筛选告警时间。
    -   默认展示最近一周的数据。
    -   用户可以筛选任意时间段。
3.  **操作按钮**:
    -   **下载按钮**: 推测用于导出当前筛选结果的统计报表（如Excel）。
    -   **设置按钮**: 功能未知，可能用于配置页面显示列或其它设置。
4.  **数据表格**:
    -   以列表形式展示统计数据。
    -   支持多选框（第一列），可能用于批量操作。
    -   **列定义**:
        -   `序号`: 数据的顺序标识。
        -   `违规类型`: 告警所属的大类，如“消防安全”、“规范作业”。
        -   `违规问题`: 具体的告警内容，如“作业现场吸烟”。
        -   `告警次数`: 在选定时间范围内，该违规问题发生的总次数。
        -   `操作`: 提供针对单条记录的操作，如图标所示为“告警详情”，点击后应跳转到该类告警的明细列表页面。

## 数据分析

此页面的数据是基于"告警事件"的聚合统计结果。根据现有系统架构，数据关系如下：

1.  **厂商场景管理 (aim_firm_scene_management)**: 存储各种违规场景的定义，包含场景编码和场景名称。
2.  **监控事件 (aiot_event)**: 每一次违规行为的实时记录，通过 `scene_code` 关联到具体的违规场景。
3.  **统计逻辑**: 对 `aiot_event` 表中的 `scene_code` 进行分组统计，获得各类违规问题的告警次数。

## 表结构设计

基于现有的 `aiot_event` 表和 `aim_firm_scene_management` 表，无需创建额外的表。

### 现有表说明

**1. 监控事件表 (`aiot_event`)** - 已存在
存储每一次告警事件的详细信息。

**2. 厂商场景管理表 (`aim_firm_scene_management`)** - 已存在
存储违规场景的定义信息。

### 数据关联关系
- `aiot_event.scene_code` 字段的值对应 `aim_firm_scene_management.code` 字段的值。
- 违规问题名称就是 `aim_firm_scene_management.scene_name`。

### 统计查询示例
当需要查询原型图中的统计数据时，可以通过以下SQL语句实现：

```sql
SELECT
    '消防安全' AS violation_type_name,  -- 违规类型（需要根据scene_name进行分类）
    sm.scene_name AS violation_issue_name, -- 违规问题
    COUNT(ae.id) AS alarm_count      -- 告警次数
FROM
    aiot_event ae
JOIN
    aim_firm_scene_management sm ON ae.scene_code = sm.code
WHERE
    ae.is_deleted = 0
    AND sm.is_deleted = 0
    AND ae.event_at BETWEEN '2025-10-01 00:00:00' AND '2025-10-07 23:59:59' -- 动态时间范围
    -- AND ae.project_id = ? -- 可根据项目ID筛选
GROUP BY
    sm.scene_name
ORDER BY
    sm.scene_name;
```

### 优化建议：场景分类统计表 (`aiot_event_statistics`)
如果需要对违规问题进行分类统计（如消防安全、规范作业等），建议创建一个分类配置表：

```sql
CREATE TABLE `aiot_event_statistics` (
  `id` bigint(20) NOT NULL,
  `scene_code` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '场景编码',
  `count` int(11) DEFAULT '0' COMMENT '告警次数',
  `is_deleted` tinyint(4) DEFAULT '0' COMMENT '是否删除:0-否,1-是',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  PRIMARY KEY (`scene_code`),
  UNIQUE KEY `uk_scene_code` (`scene_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='事件统计分类配置表';
```

### 带分类的统计查询示例
```sql
SELECT
    aes.type_name AS violation_type_name,  -- 违规类型
    sm.scene_name AS violation_issue_name, -- 违规问题
    COUNT(ae.id) AS alarm_count      -- 告警次数
FROM
    aiot_event ae
JOIN
    aim_firm_scene_management sm ON ae.scene_code = sm.code
JOIN
    aiot_event_statistics aes ON ae.scene_code = aes.scene_code
WHERE
    ae.is_deleted = 0
    AND sm.is_deleted = 0
    AND aes.is_deleted = 0
    AND ae.event_at BETWEEN '2025-10-01 00:00:00' AND '2025-10-07 23:59:59' -- 动态时间范围
    -- AND ae.project_id = ? -- 可根据项目ID筛选
GROUP BY
    aes.type_name,
    sm.scene_name
ORDER BY
    aes.sort_order,
    sm.scene_name;
```