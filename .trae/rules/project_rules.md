# 小散工程项目 (Sporadic Project) Chat 规则

本规则文件专为 `sporadic-project`（占道经营平台"烟火巷"）项目定制，仅在本项目内生效。

## 项目基本信息

### 技术栈与版本
- **Java版本**: 1.8
- **Spring Boot**: 继承自 `scloud-server:2.0.1-SNAPSHOT`
- **数据库**: 
  - 关系型数据库 (Spring Data JPA + spring-data-jpa-extra:3.3.0.RELEASE)
  - Elasticsearch: 7.17.1
  - Redis (缓存和分布式锁)
- **消息队列**: RocketMQ 4.9.3
- **定时任务**: Xxl-Job 2.3.0
- **工具库**: Lombok 1.18.36, Commons-IO 2.11.0
- **安全框架**: scloud-security-service (自定义)
- **容器化**: Docker + Swarm

### 核心依赖
```xml
<!-- 核心业务依赖 -->
<dependency>
    <groupId>cn.shencom.scloud.common</groupId>
    <artifactId>scloud-es-util</artifactId>
    <version>1.0.3-SNAPSHOT</version>
</dependency>
<dependency>
    <groupId>cn.shencom.scloud.common</groupId>
    <artifactId>redis-lock</artifactId>
    <version>2.1.0-SNAPSHOT</version>
</dependency>
<dependency>
    <groupId>com.slyak</groupId>
    <artifactId>spring-data-jpa-extra</artifactId>
    <version>3.3.0.RELEASE</version>
</dependency>
```

## 项目架构规范

### 包结构规范
```
cn.shencom.sporadic.project/
├── auth/           # 外部API认证鉴权
├── config/         # 配置类 (XxlJobConfig, EsIndexConfig等)
├── constant/       # 全局常量
├── enums/          # 业务枚举
├── feign/          # Feign客户端 (微服务调用)
├── filter/         # Servlet过滤器 (如MultiOrganizationFilter)
├── model/          # 数据模型层
│   ├── dto/        # 数据传输对象 (按create/update/query/resp分类)
│   └── *.java      # JPA实体类
├── rabbitmq/       # 消息队列消费者 (实际使用RocketMQ)
├── repos/          # Spring Data JPA Repositories
├── security/       # 安全配置
├── server/         # 核心业务逻辑
│   ├── controller/ # REST控制器
│   ├── service/    # 业务逻辑接口
│   │   └── impl/   # 业务逻辑实现
│   ├── manager/    # 高层业务封装 (如UaaManager)
│   └── timer/      # 定时任务
└── utils/          # 通用工具类
```

### 分层架构原则
1. **Controller层**: 接收HTTP请求，参数校验，DTO转换
2. **Service层**: 核心业务逻辑，通过Repository操作数据
3. **Manager层**: 外部服务调用封装，复杂业务组合
4. **Repository层**: 数据访问层

## 核心业务模块

### 主要业务领域
1. **小散工程管理** (`sporadic_project`): 核心业务，工程生命周期管理
2. **监管工单流程** (`monitor_*`): 工单流程、安装回收等
3. **人工智能管理** (`aim_*`): AI场景管理、厂商管理
4. **事件监控** (`event_*`, `aiot_*`): 违规告警、摄像头管理
5. **设备管理** (`device_*`): 摄像头配置、设备型号
6. **组织架构** (`xsgc_*`): 业务人员、客户信息
7. **系统管理** (`sys_*`): 用户权限、RBAC体系

### 关键数据表
- `sporadic_project`: 小散工程主表 (核心)
- `aiot_event`: 监控事件表 (告警数据)
- `aim_firm_scene_management`: 厂商场景管理 (违规场景定义)
- `sys_users`, `sys_roles`, `sys_permissions`: RBAC权限体系

## 编码规范

### 命名约定
- **Service接口**: 以 `I` 开头 (如 `ISporadicProjectService`)
- **Repository**: 以 `Repository` 结尾 (如 `SporadicProjectRepository`)
- **Controller**: 以 `Controller` 结尾 (如 `SporadicProjectController`)
- **实体类**: 驼峰命名，对应数据库表名
- **DTO类**: 按使用场景分类 (CreateDTO, UpdateDTO, QueryDTO, RespDTO)

### 代码质量原则
- **DRY**: 抽象通用逻辑到 utils 或 manager
- **KISS**: 优先选择简单直接的实现
- **单一职责**: Service和Controller聚焦单一业务领域
- **接口隔离**: DTO按使用场景细分
- **YAGNI**: 不过度设计，只实现当前需要的功能
- **大类拆分**: 超过500行的类应考虑拆分

### 数据库规范
- **统一字段**: `id` (bigint主键), `is_deleted` (软删除), `created_at`, `updated_at`, `deleted_at`
- **字符集**: utf8mb4
- **命名**: 表名使用下划线分隔，字段注释详细
- **关联关系**: 使用关联表处理多对多关系

## 业务特定规则

### 违规告警统计
- 基于 `aiot_event` 和 `aim_firm_scene_management` 表进行聚合统计
- 支持时间范围筛选、项目ID筛选
- 违规类型分类: 消防安全、规范作业等
- 关键查询: `scene_code` 关联场景定义

### 微服务通信
- 使用Feign Client与UAA、文件服务等通信
- 通过Manager层封装外部服务调用
- 统一错误处理和重试机制

### 安全认证
- 集成 `scloud-security-service` 框架
- 支持多组织架构 (`MultiOrganizationFilter`)
- RBAC权限控制基于 `sys_*` 表群

### 消息队列
- 虽然包名为rabbitmq，但实际使用RocketMQ
- 消费者统一放在 `rabbitmq` 包下
- 注意消息幂等性处理

### 定时任务
- 使用Xxl-Job框架
- 任务实现放在 `server.timer` 包下
- 配置类: `XxlJobConfig`

## 开发建议

### 新功能开发
1. 优先查看现有的Controller和Service实现模式
2. 遵循现有的DTO分类规范
3. 注意软删除字段的使用
4. 关联查询时注意性能优化
5. 重要业务节点记录日志

### 数据库操作
1. 使用Spring Data JPA + spring-data-jpa-extra
2. 复杂查询可使用 `.sftl` 模板文件 (位于 `resources/sqls/`)
3. 注意索引使用和分页处理
4. 维护数据一致性，特别是关联表

### 性能优化
1. 大表查询使用分页
2. 合理使用Redis缓存
3. Elasticsearch用于复杂搜索场景
4. 注意N+1查询问题

## 数据库架构理解指南

### 数据库概览
- **数据库名**: `db_tn_sc047f71a5d4ef1134`
- **字符集**: utf8mb4
- **引擎**: InnoDB
- **表总数**: 约80+张表
- **SQL文件位置**: `/sql/db_tn_sc047f71a5d4ef1134.sql`

### 表分类体系

#### 1. 核心业务表群 (sporadic_*)
- **sporadic_project**: 小散工程主表 (核心)
  - 包含工程基本信息、地理位置、建设/施工单位、状态等
  - 关键字段: `name`, `organization_id`, `status`, `monitor_flag`
- **sporadic_project_category**: 工程分类表
- **sporadic_project_memo**: 工程备注表

#### 2. AI智能管理表群 (aim_*)
- **aim_firm**: 厂商管理表
- **aim_firm_scene_management**: 厂商场景管理 (违规场景定义)
  - 关键字段: `code`, `scene_name`, `firm_id`
- **aim_scene_management**: AI场景管理
- **aim_scene_category_management**: 场景类别管理
- **aim_scene_firm_relationship**: 场景-厂商关联表

#### 3. 事件监控表群 (aiot_*)
- **aiot_event**: 监控事件表 (告警数据核心表)
  - 关键字段: `project_id`, `scene_code`, `event_at`, `device_code`
- **aiot_event_statistics**: 事件统计表
- **aiot_scene**: 违规类型场景表
- **aiot_category**: 场景分类表
- **aiot_scene_mgr**: 场景类别管理

#### 4. 监管工单表群 (monitor_*)
- **monitor_order**: 监管工单主表
- **monitor_install**: 安装工单
- **monitor_recycle**: 回收工单
- **monitor_flow**: 工单流程
- **monitor_access_info**: 接入信息
- **monitor_on_scene_inspection**: 现场检查

#### 5. 设备管理表群 (device_*)
- **device_camera_config**: 摄像头配置
- **device_camera_type**: 摄像头型号

#### 6. 组织架构表群 (xsgc_*)
- **xsgc_organization**: 组织机构
- **xsgc_business_members**: 业务人员
- **xsgc_customer_info**: 客户信息
- **xsgc_customer_service_record**: 客户服务记录

#### 7. 系统管理表群 (sys_*)
- **sys_users**: 用户表
- **sys_roles**: 角色表
- **sys_permissions**: 权限表
- **sys_menu**: 菜单表
- **sys_operate_log**: 操作日志

#### 8. 通用基础表群 (com_*)
- **com_region**: 地区表 (省市区街道社区)
- **com_org**: 组织机构表
- **com_dictionary**: 数据字典
- **com_upload_resource**: 文件资源表

### 核心业务关系

#### 主要关联关系
1. **工程 → 事件监控**:
   ```
   sporadic_project.id → aiot_event.project_id
   ```

2. **事件 → 场景定义**:
   ```
   aiot_event.scene_code → aim_firm_scene_management.code
   ```

3. **工程 → 地理位置**:
   ```
   sporadic_project.region_pid → com_region.id (区)
   sporadic_project.region_id → com_region.id (街道)
   sporadic_project.region_cid → com_region.id (社区)
   ```

4. **工程 → 组织架构**:
   ```
   sporadic_project.organization_id → xsgc_organization.id
   ```

### 统一字段规范

#### 标准字段
- **id**: `bigint(20)` 主键
- **is_deleted**: `tinyint(4)` 软删除标记 (0-否, 1-是)
- **created_at**: `datetime` 创建时间
- **updated_at**: `datetime` 更新时间
- **deleted_at**: `datetime` 删除时间 (部分表有)

#### 特殊字段模式
- **地理位置**: `lng`(经度), `lat`(纬度)
- **状态字段**: 通常用 `tinyint(4)` 表示不同状态
- **编码字段**: `code` 字段通常有唯一索引
- **关联字段**: 外键字段通常以 `_id` 结尾

### 查询优化建议

#### 重要索引
- `aiot_event`: 按 `project_id`, `scene_code`, `event_at` 查询
- `sporadic_project`: 按 `organization_id`, `status` 查询
- `aim_firm_scene_management`: 按 `code`, `firm_id` 查询

#### 常用查询模式
1. **违规统计查询**:
   ```sql
   SELECT scene_code, COUNT(*) 
   FROM aiot_event 
   WHERE project_id = ? AND event_at BETWEEN ? AND ?
   GROUP BY scene_code
   ```

2. **工程详情查询**:
   ```sql
   SELECT p.*, r1.title as region_name, r2.title as street_name
   FROM sporadic_project p
   LEFT JOIN com_region r1 ON p.region_pid = r1.id
   LEFT JOIN com_region r2 ON p.region_id = r2.id
   WHERE p.id = ?
   ```

### 数据一致性注意事项

1. **软删除**: 所有查询都需要加上 `is_deleted = 0` 条件
2. **级联删除**: 删除工程时需要同步处理相关的事件、工单等数据
3. **状态同步**: 工程状态变更时需要同步更新监管标记等字段
4. **编码唯一性**: `scene_code` 等编码字段需要保证唯一性

### 开发查询建议

1. **表结构查询**: 直接查看 `/sql/db_tn_sc047f71a5d4ef1134.sql` 文件
2. **字段含义**: 参考表注释和字段注释
3. **关联关系**: 通过字段名推断 (如 `project_id` 关联 `sporadic_project.id`)
4. **业务逻辑**: 结合代码中的 Repository 和 Service 实现理解

### 任务分解原则
- **要求**: 接收需求时优先分解为任务列表，再逐个实现
- **处理流程**:
  1. **需求分析**: 理解用户需求的完整意图
  2. **任务分解**: 将复杂需求拆分为具体的、可执行的任务
  3. **优先级排序**: 按照依赖关系和重要性排序
  4. **逐个实现**: 按顺序完成每个任务
  5. **进度反馈**: 完成每个任务后提供简要反馈

- **任务分解示例**:
  ```
  需求: 实现用户管理功能
  
  任务列表:
  Task 1: 创建用户实体类和DTO
  Task 2: 实现用户Repository接口
  Task 3: 编写用户Service业务逻辑
  Task 4: 开发用户Controller接口
  Task 5: 添加用户权限验证
  Task 6: 集成前端页面调用
  ```

---

**注意**: 本项目是"占道经营平台烟火巷"的核心服务，专注于小散工程的全生命周期管理和智能监管。开发时应充分理解业务场景，确保代码质量和系统稳定性。