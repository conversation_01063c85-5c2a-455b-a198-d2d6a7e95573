# 用户个人偏好规则

本文件定义了用户的个人开发偏好和要求，适用于所有项目开发过程中。

## 基本偏好设置

### 1. 对话语言
- **要求**: 保持对话语言为中文
- **说明**: 所有交流、解释、注释和文档都使用中文
- **适用范围**: 代码注释、错误信息、日志输出、用户界面文本

### 2. 系统环境
- **操作系统**: macOS
- **说明**: 所有命令、路径和配置都基于Mac系统
- **注意事项**: 
  - 使用Unix风格的路径分隔符 `/`
  - 考虑Mac特有的文件系统特性
  - 使用适合Mac的开发工具和命令

## 代码开发规范

### 3. 代码注释要求
- **要求**: 必须添加函数级注释
- **注释标准**:
  ```java
  /**
   * 方法功能描述
   * @param paramName 参数说明
   * @return 返回值说明
   * @throws ExceptionType 异常说明
   */
  public ReturnType methodName(ParamType paramName) {
      // 实现逻辑
  }
  ```
- **注释内容**:
  - 方法的主要功能和用途
  - 参数的含义和约束
  - 返回值的说明
  - 可能抛出的异常
  - 特殊业务逻辑的说明

### 4. 测试用例
- **要求**: 不需要编写测试用例
- **说明**: 专注于功能实现，跳过单元测试和集成测试的编写
- **例外**: 如果用户明确要求测试，则按需提供，

### 5. 文档编写
- **要求**: 不需要编写额外文档
- **说明**: 不生成README、API文档、用户手册等
- **例外**: 代码注释仍然必须完整，这是代码的一部分，如果用户明确要求生成文档，则按需提供

## 需求处理流程

### 6. 任务分解原则
- **要求**: 接收需求时优先分解为任务列表，再逐个实现
- **处理流程**:
  1. **需求分析**: 理解用户需求的完整意图
  2. **任务分解**: 将复杂需求拆分为具体的、可执行的任务
  3. **优先级排序**: 按照依赖关系和重要性排序
  4. **逐个实现**: 按顺序完成每个任务
  5. **进度反馈**: 完成每个任务后提供简要反馈

- **任务分解示例**:
  ```
  需求: 实现用户管理功能
  
  任务列表:
  Task 1: 创建用户实体类和DTO
  Task 2: 实现用户Repository接口
  Task 3: 编写用户Service业务逻辑
  Task 4: 开发用户Controller接口
  Task 5: 添加用户权限验证
  Task 6: 集成前端页面调用
  ```

## 代码质量要求

### 7. 编码风格
- 遵循项目现有的编码规范
- 保持代码简洁和可读性
- 使用有意义的变量和方法命名
- 适当的代码分层和模块化

### 8. 错误处理
- 提供清晰的中文错误信息
- 合理的异常处理机制
- 重要操作添加日志记录

### 9. 性能考虑
- 避免明显的性能问题
- 合理使用缓存和数据库查询
- 考虑并发安全性

## 沟通方式

### 10. 反馈机制
- 每完成一个任务提供简要进度更新
- 遇到问题时及时说明和询问
- 提供清晰的中文解释和建议

### 11. 确认机制
- 对于复杂需求，先确认理解是否正确
- 重要决策点征求用户意见
- 提供多种实现方案供选择

---

**注意**: 这些规则是用户的个人偏好，在所有项目开发中都应该遵循。如有特殊情况需要调整，请提前沟通确认。