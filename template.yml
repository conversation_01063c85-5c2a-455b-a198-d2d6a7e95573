version: '3.8'
services:
    {{name}}:
      image: {{image}}
      networks:
        - {{network}}
      environment:
        NACOS_HOST: nacos:8848
        NACOS_NAMESPACE: ''
        NACOS_GROUP: scloud-group
        SW_AGENT_COLLECTOR_BACKEND_SERVICES: oap:11800
        SW_AGENT_NAME: {{name}}
        SW_GRPC_LOG_SERVER_HOST: oap
        SW_LOGGING_LEVEL: 'OFF'
      extra_hosts:
        - "aiot-t.shencom.cn:*************"
      deploy:
        replicas: {{replicas}}
        update_config:
          parallelism: 2
          delay: 3s
          order: stop-first
        placement:
          constraints:
            - node.labels.role==services
        restart_policy:
          condition: on-failure
          max_attempts: 2
        resources:
          limits:
            memory: {{memory_limit}}
          reservations:
            memory: {{memory_rsv}}
networks:
    {{network}}:
      external: true
