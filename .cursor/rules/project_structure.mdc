---
description: 
globs: 
alwaysApply: false
---
# 项目架构与模块指南

本文档为 `sporadic-project` 提供架构概览、模块划分和编码风格指导。

## 1. 整体架构

本系统是一个基于 Java 8 和 Spring Boot 的微服务应用，是 `scloud` 技术体系的一部分。

- **父项目**: [pom.xml](mdc:pom.xml) 中定义了父项目 `cn.shencom.scloud.server:scloud-server`，项目继承了统一的依赖管理和插件配置。
- **微服务通信**: 系统通过 Feign Client 与其他微服务（如UAA、文件服务）进行HTTP通信。相关定义在 `cn.shencom.feign` 包中。
- **容器化**: 项目提供了 [Dockerfile](mdc:Dockerfile) 和 `swarm.sh`，支持 Docker 容器化部署。

## 2. 主要技术栈

- **核心框架**: Spring Boot
- **持久化**: Spring Data JPA (`spring-data-jpa-extra`)
- **数据库/存储**:
  - 关系型数据库 (通过JPA)
  - Elasticsearch (v7.17.1)
  - Redis (用于缓存和分布式锁)
- **消息队列**: RocketMQ
- **定时任务**: Xxl-Job
- **安全**: `scloud-security-service` (自定义安全框架)
- **工具**: Lombok, Commons-IO

## 3. 模块划分与组织结构

项目遵循按功能分层的结构，主要代码位于 `src/main/java/cn/shencom/sporadic/project/`。

- `auth`: 外部API的认证与鉴权逻辑。
- `config`: 应用的配置类，如 `XxlJobConfig.java`、`EsIndexConfig.java`。
- `constant`: 全局常量定义。
- `enums`: 业务相关的枚举类型。
- `feign`: Feign客户端接口，用于服务间调用。
- `filter`: Servlet过滤器，如 `MultiOrganizationFilter` 用于处理多组织逻辑。
- `model`: 数据模型层。
  - `dto`: 数据传输对象 (Data Transfer Objects)，按 `create`, `update`, `query`, `resp` 等场景细分。
  - `*.java`: JPA实体 (Entities)。
- `rabbitmq`: 消息队列的消费者。**注意：虽然包名为rabbitmq，但实际使用的是RocketMQ**。
- `repos`: Spring Data JPA Repositories，负责数据库访问。
- `security`: 安全配置，与 `scloud-security-service` 集成。
- `server`: 核心业务逻辑。
  - `controller`: Spring MVC 控制器，对外提供RESTful API。
  - `service`: 业务逻辑接口。
    - `impl`: 业务逻辑实现。
  - `manager`: 更高层次的业务封装或外部服务调用封装，如 `UaaManager`。
  - `timer`: 定时任务实现。
- `utils`: 通用工具类。

## 4. 模块间耦合关系

- **典型流程**: `Controller` -> `Service` -> `Repository`。
- `Controller` 负责接收HTTP请求，调用 `Service` 处理业务逻辑。参数校验和DTO转换应在 `Controller` 层完成。
- `Service` 层负责核心业务逻辑，通过 `Repository` 操作数据库，可能调用 `Manager` 或 Feign Client 与其他服务交互。
- `Manager` 层用于封装对外部服务（如UAA）的调用，或组合多个`Service`以完成复杂操作，旨在让`Service`层保持聚焦。
- 模块间应通过接口（如`Service`接口）通信，而不是直接依赖实现类，以降低耦合。

## 5. 代码风格与原则

- **DRY (Don't Repeat Yourself)**: 抽象通用逻辑到 `utils` 或 `manager` 中。
- **KISS (Keep It Simple, Stupid)**: 优先选择简单直接的实现。
- **SOLID**:
  - **单一职责**: `Service` 和 `Controller` 应聚焦于单一业务领域。过大的类（如超过500行）应考虑拆分。
  - **接口隔离**: `dto` 包按下使用场景（创建、查询、响应）细分是接口隔离原则的良好体现，应继续遵守。
- **YAGNI (You Ain't Gonna Need It)**: 不要过度设计，只实现当前需要的功能。
- **命名**:
  - `Service` 接口以 `I` 开头 (e.g., `ISporadicProjectService`)。
  - `Repository` 以 `Repository` 结尾 (e.g., `SporadicProjectRepository`)。
  - `Controller` 以 `Controller` 结尾 (e.g., `SporadicProjectController`)。
- **日志**: 使用 `scloud-log-starter` 提供的日志框架，在关键业务节点记录日志。

---
*此规则文件由AI根据项目结构和 `pom.xml` 生成。*
