package cn.shencom.utils;

import cn.shencom.constant.InviteCodeConstant;
import org.junit.jupiter.api.Test;

import java.util.HashSet;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 邀请码工具类测试
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
public class InviteCodeUtilTest {

    @Test
    public void testGenerateInviteCode() {
        // 测试生成邀请码
        String inviteCode = InviteCodeUtil.generateInviteCode();
        
        // 验证邀请码不为空
        assertNotNull(inviteCode);
        
        // 验证邀请码长度
        assertEquals(InviteCodeConstant.INVITE_CODE_LENGTH, inviteCode.length());
        
        // 验证邀请码只包含大小写字母和数字
        assertTrue(inviteCode.matches("[A-Za-z0-9]+"));
    }

    @Test
    public void testGenerateUniqueInviteCodes() {
        // 测试生成多个邀请码的唯一性
        Set<String> codes = new HashSet<>();
        int count = 1000;
        
        for (int i = 0; i < count; i++) {
            String code = InviteCodeUtil.generateInviteCode();
            codes.add(code);
        }
        
        // 验证生成的邀请码都是唯一的
        assertEquals(count, codes.size());
    }

    @Test
    public void testGetRedisKey() {
        String inviteCode = "ABC123def4";
        String expectedKey = InviteCodeConstant.INVITE_CODE_REDIS_PREFIX + inviteCode;
        String actualKey = InviteCodeUtil.getRedisKey(inviteCode);
        
        assertEquals(expectedKey, actualKey);
    }

    @Test
    public void testInviteCodeCharset() {
        // 测试生成的邀请码字符集
        String charset = InviteCodeConstant.INVITE_CODE_CHARSET;
        
        for (int i = 0; i < 100; i++) {
            String code = InviteCodeUtil.generateInviteCode();
            for (char c : code.toCharArray()) {
                assertTrue(charset.indexOf(c) >= 0, 
                    "邀请码包含不允许的字符: " + c);
            }
        }
    }
}
