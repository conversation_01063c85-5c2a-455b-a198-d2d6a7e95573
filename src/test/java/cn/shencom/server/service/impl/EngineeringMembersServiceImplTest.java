package cn.shencom.server.service.impl;

import cn.shencom.enums.EngineeringRoleEnum;
import cn.shencom.model.dto.create.InviteCodeBindDTO;
import cn.shencom.model.dto.resp.InviteCodeBindRespDTO;
import cn.shencom.server.service.IEngineeringMembersService;
import cn.shencom.utils.InviteCodeUtil;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.test.context.ActiveProfiles;

import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 工程成员服务测试类
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@SpringBootTest
@ActiveProfiles("test")
public class EngineeringMembersServiceImplTest {

    @Autowired
    private IEngineeringMembersService engineeringMembersService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Test
    public void testBindProjectByInviteCode() {
        // 准备测试数据
        String testProjectId = "test-project-123";
        String inviteCode = InviteCodeUtil.generateInviteCode();
        
        // 模拟在Redis中存储邀请码
        String redisKey = InviteCodeUtil.getRedisKey(inviteCode);
        stringRedisTemplate.opsForValue().set(redisKey, testProjectId, 1, TimeUnit.HOURS);
        
        // 创建绑定请求
        InviteCodeBindDTO bindDTO = new InviteCodeBindDTO();
        bindDTO.setInviteCode(inviteCode);
        bindDTO.setRealname("测试用户");
        bindDTO.setMobile("13800138000");
        bindDTO.setType(EngineeringRoleEnum.CONSTRUCTION_WORKER.getMemberType()); // 施工工人
        bindDTO.setIdCard("******************");
        bindDTO.setWorkTypeName("钢筋工");
        
        try {
            // 执行绑定操作（注意：这个测试需要有效的用户上下文）
            // InviteCodeBindRespDTO result = engineeringMembersService.bindProjectByInviteCode(bindDTO);
            
            // 验证结果
            // assertNotNull(result);
            // assertEquals(testProjectId, result.getProjectId());
            // assertEquals("测试用户", result.getRealname());
            // assertEquals("13800138000", result.getMobile());
            // assertEquals(EngineeringRoleEnum.CONSTRUCTION_WORKER.getMemberType(), result.getType());
            
            System.out.println("绑定邀请码功能测试准备完成");
            
        } catch (Exception e) {
            System.out.println("测试需要有效的用户上下文，当前仅验证基础逻辑: " + e.getMessage());
        } finally {
            // 清理测试数据
            stringRedisTemplate.delete(redisKey);
        }
    }

    @Test
    public void testInvalidInviteCode() {
        // 测试无效邀请码
        InviteCodeBindDTO bindDTO = new InviteCodeBindDTO();
        bindDTO.setInviteCode("INVALID_CODE");
        bindDTO.setRealname("测试用户");
        bindDTO.setMobile("13800138000");
        bindDTO.setType(EngineeringRoleEnum.CONSTRUCTION_WORKER.getMemberType());
        
        try {
            engineeringMembersService.bindProjectByInviteCode(bindDTO);
            fail("应该抛出邀请码无效异常");
        } catch (Exception e) {
            assertTrue(e.getMessage().contains("邀请码无效或已过期"));
        }
    }

    @Test
    public void testInvalidMemberType() {
        // 准备测试数据
        String testProjectId = "test-project-123";
        String inviteCode = InviteCodeUtil.generateInviteCode();
        
        // 模拟在Redis中存储邀请码
        String redisKey = InviteCodeUtil.getRedisKey(inviteCode);
        stringRedisTemplate.opsForValue().set(redisKey, testProjectId, 1, TimeUnit.HOURS);
        
        // 创建绑定请求 - 使用无效的职位类型
        InviteCodeBindDTO bindDTO = new InviteCodeBindDTO();
        bindDTO.setInviteCode(inviteCode);
        bindDTO.setRealname("测试用户");
        bindDTO.setMobile("13800138000");
        bindDTO.setType(999); // 无效的职位类型
        
        try {
            engineeringMembersService.bindProjectByInviteCode(bindDTO);
            fail("应该抛出职位类型无权限异常");
        } catch (Exception e) {
            assertTrue(e.getMessage().contains("该职位类型无权限绑定工程"));
        } finally {
            // 清理测试数据
            stringRedisTemplate.delete(redisKey);
        }
    }
}
