-- mobileIndex
SELECT
    device.*,
    project.name as project<PERSON><PERSON>,
    project.address as projectAddress,
    project.id as projectId
FROM
    event_camera_point_device device
    LEFT JOIN sporadic_project project ON device.project_id = project.id
WHERE
    device.is_deleted = 0
    and project.is_deleted = 0
    and project.id in (:projectIds)
    <#if keyword?? >
        and (project.name LIKE CONCAT('%',:keyword,'%') or project.address LIKE CONCAT('%',:keyword,'%'))
    </#if>
    <#if regionId??>
        and project.region_id = :regionId
    <#elseif regionCid??>
        and project.region_cid = :regionCid
    <#elseif regionPid??>
        and project.region_pid = :regionPid
    </#if>
ORDER BY device.created_at DESC

-- notRelevanceIndex
SELECT
id id,
serial_no serialNo
FROM event_camera_point_device
WHERE is_deleted = 0 AND project_id IS NULL
<#if serialNo?? >
    and  serial_no like CONCAT('%',:serialNo,'%')
</#if>
ORDER BY updated_at DESC