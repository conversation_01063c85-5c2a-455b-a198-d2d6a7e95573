-- findAllRelateByOrganizationId
SELECT
    r.type_id typeId, m.user_id userId
FROM fn_rmsv3_members_type_relate r
         JOIN fn_rmsv3_members m ON r.member_id = m.id
WHERE m.organization_id = :organizationId
  AND  m.deleted_at is null AND r.is_deleted = 0 AND r.active = 1


<#---- getAllOrganizationByUserId-->
<#--SELECT-->
<#--    o.id id,-->
<#--    o.NAME NAME-->
<#--FROM-->
<#--         fn_rmsv3_members m-->
<#--         JOIN fn_rmsv3_members_type_relate r ON r.member_id = m.id AND r.type_id = 1-->
<#--         JOIN xsgc_organization o ON m.organization_id = o.id-->
<#--         JOIN sys_role_user_organization sruo ON sruo.user_id = :userId AND  sruo.organization_id = m.organization_id-->
<#--WHERE m.deleted_at is null AND r.is_deleted = 0 AND r.active = 1-->
<#--AND o.is_deleted = 0 AND m.user_id = :userId AND sruo.role_id = :roleId-->


-- getAllOrganizationByUserId
SELECT DISTINCT
    o.id id,
    o.NAME NAME
FROM
    fn_rmsv3_members m
        JOIN xsgc_organization o ON m.organization_id = o.id
        JOIN fn_rmsv3_members_type_relate r ON m.id = r.member_id
WHERE m.deleted_at is null
  AND o.is_deleted = 0 AND m.user_id = :userId AND m.is_deleted = 0 AND r.is_deleted = 0 AND r.active = 1
<#if organizationId??>
    AND m.organization_id = :organizationId
</#if>

-- getStatics
SELECT
    (SELECT COUNT(id) FROM fn_rmsv3_members WHERE is_deleted = 0 AND organization_id = :organizationId ) totalCnt,
    IFNULL(SUM( CASE WHEN type_id  = 1 THEN 1 ELSE 0 END ) ,0)  administratorsCnt,
    IFNULL(SUM( CASE WHEN type_id  = 2 THEN 1 ELSE 0 END ),0) cityPersonalCnt,
    IFNULL(SUM( CASE WHEN type_id  = 3 THEN 1 ELSE 0 END ),0) districtPersonalCnt,
    IFNULL(SUM( CASE WHEN type_id  = 4 THEN 1 ELSE 0 END ),0) streetPersonalCnt,
    IFNULL(SUM( CASE WHEN type_id  = 5 THEN 1 ELSE 0 END ),0) communityPersonalCnt,
    IFNULL(SUM( CASE WHEN type_id  = 6 THEN 1 ELSE 0 END ),0) inspectorCnt
FROM
    fn_rmsv3_members_type_relate  r
        JOIN fn_rmsv3_members m ON r.member_id = m.id
WHERE r.is_deleted = 0 AND m.is_deleted = 0 AND m.organization_id = :organizationId

-- findAllUserIdByProjectId
SELECT
     m.user_id userId
FROM fn_rmsv3_members_type_relate r
         JOIN fn_rmsv3_members m ON r.member_id = m.id
         JOIN sporadic_project p ON m.organization_id = p.organization_id
WHERE
  p.id = :projectId AND r.level = 0
  AND  m.deleted_at is null AND r.is_deleted = 0 AND r.active = 1 AND r.type_id IN (2,3,4,5,6)
UNION

SELECT
    DISTINCT m.user_id userId
FROM fn_rmsv3_members_type_relate r
         JOIN fn_rmsv3_members m ON r.member_id = m.id
         JOIN sporadic_project p ON m.organization_id = p.organization_id
         JOIN fn_rmsv3_members_type_relate_binding b ON r.id = b.relate_id
WHERE
    p.id = :projectId AND r.level = 1 AND b.region_pid = p.region_pid
  AND  m.deleted_at is null AND r.is_deleted = 0 AND r.active = 1 AND r.type_id IN (2,3,4,5,6)
UNION
SELECT
    DISTINCT m.user_id userId
FROM fn_rmsv3_members_type_relate r
         JOIN fn_rmsv3_members m ON r.member_id = m.id
         JOIN sporadic_project p ON m.organization_id = p.organization_id
         JOIN fn_rmsv3_members_type_relate_binding b ON r.id = b.relate_id
WHERE
    p.id = :projectId AND r.level = 2 AND b.region_id = p.region_id
  AND  m.deleted_at is null AND r.is_deleted = 0 AND r.active = 1 AND r.type_id IN (2,3,4,5,6)

UNION
SELECT
    DISTINCT m.user_id userId
FROM fn_rmsv3_members_type_relate r
         JOIN fn_rmsv3_members m ON r.member_id = m.id
         JOIN sporadic_project p ON m.organization_id = p.organization_id
         JOIN fn_rmsv3_members_type_relate_binding b ON r.id = b.relate_id
WHERE
    p.id = :projectId AND r.level = 3 AND b.region_cid = p.region_cid
  AND  m.deleted_at is null AND r.is_deleted = 0 AND r.active = 1 AND r.type_id IN (2,3,4,5,6)