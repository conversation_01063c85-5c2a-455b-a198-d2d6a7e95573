-- getAllRoles

SELECT
    sr.name roleName, sr.display_name  displayName
FROM
    sys_roles sr
        JOIN sys_role_user sru ON sr.id = sru.role_id
WHERE sr.deleted_at is null AND sr.flag = 0 AND sru.user_id = :userId
<#if roleNameList??>
    AND sr.name in (:roleNameList)
</#if>



-- getAllOrganizationRoles
SELECT
    sr.name roleName, sr.display_name  displayName
FROM
    sys_roles sr
        JOIN sys_role_user_organization sruo ON sruo.organization_id = :organizationId AND sr.id = sruo.role_id
WHERE sr.deleted_at is null AND sr.flag = 1 AND sruo.user_id = :userId
<#if roleNameList??>
    AND sr.name in (:roleNameList)
</#if>


