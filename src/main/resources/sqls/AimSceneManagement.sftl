-- getSceneStatisticsData
select count(distinct asm.id) as sceneNum, count(distinct asm.scene_category_id) as sceneCategoryNum
from aim_scene_firm_relationship asfr
left join aim_scene_management asm on asfr.scene_id = asm.id
where asfr.firm_id = :firmId
<#if name?? && name?length gt 0>
   AND asm.name like :name
</#if>
<#if code?? && code?length gt 0>
   AND asm.code like :code
</#if>
<#if sceneCategoryId?? && sceneCategoryId?length gt 0>
   AND asm.scene_category_id = :sceneCategoryId
</#if>
<#if tags?? && tags?length gt 0>
   AND asm.tags like :tags
</#if>
<#if simpleUsed?? && simpleUsed?length gt 0>
    and ( asm.name LIKE :simpleUsed or asm.code LIKE :simpleUsed or asm.tags LIKE :simpleUsed )
</#if>

-- findCodesById
select s.code
from aim_scene_firm_relationship t
         left join aim_firm_scene_management s on t.firm_id = s.id and s.is_deleted = 0
where scene_id = :id

-- getAiSceneAll
select id, name
from aim_scene_management
where is_deleted = 0