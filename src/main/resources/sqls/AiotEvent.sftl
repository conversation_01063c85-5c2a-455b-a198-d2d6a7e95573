-- mobileIndex
SELECT
    `event`.*,
    project.name as projectName,
    project.address as projectAddress
FROM
    aiot_event `event`
    LEFT JOIN sporadic_project project ON `event`.project_id = project.id
WHERE
    `event`.is_deleted = 0
    and project.is_deleted = 0
    <#if organizationId??>
        and project.organization_id = :organizationId
    </#if>
    <#if projectIdSet??>
        and project.id in (:projectIdSet)
    </#if>
    <#if projectId??>
        and project.id = :projectId
    </#if>
    <#if keyword?? >
     and (project.name LIKE :keyword or project.address LIKE :keyword)
    </#if>
    <#if startTime?? && endTime?? >
     and `event`.event_at BETWEEN :startTime AND :endTime
    </#if>
    <#if sceneCode??>
        and `event`.scene_code = :sceneCode
    </#if>
    <#if regionCidSet??>
        and project.region_cid in (:regionCidSet)
    <#elseif regionIdSet??>
        and project.region_id in (:regionIdSet)
    <#elseif regionPidSet??>
        and project.region_pid in (:regionPidSet)
    </#if>
    <#if regionCid??>
        and project.region_cid = :regionCid
    <#elseif regionId??>
        and project.region_id = :regionId
    <#elseif regionPid??>
        and project.region_pid = :regionPid
    </#if>
    <#if eventAtSort?? && eventAtSort == "desc">
        order by `event`.event_at desc
    <#elseif eventAtSort?? && eventAtSort == "asc">
        order by `event`.event_at asc
    <#else>
        order by `event`.event_at desc
    </#if>

-- queryEvent
SELECT ev.* FROM aiot_event ev
    LEFT JOIN sporadic_project project ON ev.project_id = project.id
WHERE ev.is_deleted = 0
    AND project.is_deleted = 0
<#if sceneCode??>
    AND ev.scene_code = :sceneCode
</#if>
<#if projectId??>
    AND project.id in (:projectId)
</#if>
<#if startDate?? && endDate??>
    AND ev.event_at between :startDate and :endDate
</#if>
