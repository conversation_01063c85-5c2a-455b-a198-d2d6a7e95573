-- relevanceIndex
SELECT m.id  memberId , m.realname realname , m.mobile mobile , m.type type
FROM xsgc_business_members m
JOIN xsgc_business_members_relate r ON m.id = r.member_id
WHERE r.organization_id = :organizationId
AND m.is_deleted = 0 and r.is_deleted = 0 AND m.status = 1
AND m.type = :type

-- notRelevanceIndex
SELECT
    m.id AS memberId,
    m.realname AS realname,
    m.mobile AS mobile,
    m.type type
FROM
    xsgc_business_members m
    LEFT JOIN
    xsgc_business_members_relate r ON m.id = r.member_id AND r.organization_id = :organizationId AND r.is_deleted = 0
WHERE
    m.is_deleted = 0 AND m.status = 1
  AND r.member_id IS NULL
<#if type??>
      AND m.type = :type
</#if>


-- findByOrganizationIdAndType
SELECT
    r.*
FROM
    xsgc_business_members m
    JOIN
    xsgc_business_members_relate r ON m.id = r.member_id AND r.organization_id = :organizationId AND r.is_deleted = 0
WHERE
    m.is_deleted = 0
  AND m.type = :type