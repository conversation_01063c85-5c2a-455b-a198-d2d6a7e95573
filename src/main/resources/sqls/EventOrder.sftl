-- getEventData
select
    count(id) as eventNum,
    count(if(status = 1, id, null)) as handledEventNum,
    count(if(status = 0, id, null)) as pendingEventNum,
    count(if(status = 2, id, null)) as timeOutEventNum,
    count(if(dispose_desc = '事件识别准确', id, null)) as eventPreciseNum,
    count(if(dispose_desc = '事件识别不准确', id, null)) as eventInaccurateNum,
    count(if(dispose_desc = '识别有误，智能模型再学习', id, null)) as eventWrongNum
from event_order
where is_deleted = 0
  and type_code in (:typeCodeList)