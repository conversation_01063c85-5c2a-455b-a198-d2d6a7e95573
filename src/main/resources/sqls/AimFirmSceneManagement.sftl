-- getNameAndCodeById
select scene_name name,code code from aim_firm_scene_management where is_deleted = 0 and id = :firmId

-- getSceneStatistics
select count(distinct afsm.firm_id) as firmNum, count(distinct afsm.code) as firmSceneNum
from aim_scene_firm_relationship asfr
left join aim_firm_scene_management afsm on asfr.firm_id = afsm.id
left join aim_firm af on asfr.firm_id = af.id
where asfr.scene_id = :sceneId
<#if firmId?? && firmId?length gt 0>
   AND afsm.firm_id = :firmId
</#if>
<#if code?? && code?length gt 0>
   AND afsm.code like :code
</#if>
<#if sceneName?? && sceneName?length gt 0>
   AND afsm.scene_name like :sceneName
</#if>
<#if simpleUsed?? && simpleUsed?length gt 0>
    and ( af.firm_name LIKE :simpleUsed or afsm.code LIKE :simpleUsed or afsm.scene_name LIKE :simpleUsed )
</#if>