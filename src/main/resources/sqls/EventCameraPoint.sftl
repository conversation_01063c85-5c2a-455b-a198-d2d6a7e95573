-- getCameraType
select `code`, `name`
from event_camera_point_type
where is_deleted = 0
<#if typeTag??>
    <#if typeTag == 1>
        and is_point = 1
    </#if>
    <#if typeTag == 2>
        and is_station = 1
    </#if>
    <#if typeTag == 3>
        and is_vehicle = 1
    </#if>
    <#if typeTag == 4>
        and is_temp_point = 1
    </#if>
    <#if typeTag == 5>
        and is_open = 1
    </#if>
    <#if typeTag == 6>
        and is_rebirth = 1
    </#if>
</#if>

-- getBuildTreePointData
select t1.*, district.sort districtSort
from
    (
    select ecp.id id, project.region_pid regionPid, ifnull(project.region_id, 0) regionId, ifnull(project.region_cid, 0) regionCid, ecp.project_id projectId,
           ecp.model_no modelNo, ecp.`type` `type`, ecp.channel channel, ecp.flv_address flvAddress, ecp.hd_flv_address hdFlvAddress, ecp.monitor_no monitorNo,
           ecp.open_live openLive, ecp.status status, ecp.serial_no serialNo, ecp.is_lock isLock, project.name projectName
    from event_camera_point ecp
    join sporadic_project project on ecp.project_id = project.id
    where ecp.is_deleted = 0 and project.is_deleted = 0
    <#if regionPid?? && regionPid?length gt 0>
        and project.region_pid = :regionPid
    </#if>
    <#if regionId?? && regionId?length gt 0>
        and project.region_id = :regionId
    </#if>
        <#if regionCid?? && regionCid?length gt 0>
        and project.region_cid = :regionCid
        </#if>
    <#if projectId??>
        and ecp.project_id in (:projectId)
    </#if>
    ) t1
        left join com_region community on t1.regionCid = community.id and community.deleted_at is null
        left join com_region street on t1.regionId = street.id and street.deleted_at is null
        left join com_region district on t1.regionPid = district.id and district.deleted_at is null
order by district.sort, street.sort, community.sort desc

-- findSliceSerialNoChannel
select event_camera_point.type, event_camera_point_device.sip_user_id, event_camera_point.serial_no, event_camera_point.channel
from event_camera_point
         left join event_camera_point_device on event_camera_point_device.id = event_camera_point.device_id
where event_camera_point.is_deleted = 0
<#if serialNo?? && serialNo?length gt 0>
        and event_camera_point.serial_no = :serialNo
</#if>

-- getLiveData
select ecp.flv_address, ecp.hd_flv_address, ecp.`status`
from event_camera_point ecp
where ecp.is_deleted = 0 and ecp.monitor_no = :monitorNo
group by ecp.monitor_no

-- getAllDeviceByType
select t1.id, t1.device_id deviceId, ifnull(t2.sip_user_id, t1.serial_no) deviceCode, t1.channel peripheryCode, 1 type
from event_camera_point t1
         left join event_camera_point_device t2 on t1.device_id = t2.id and t2.is_deleted = 0
where t1.is_deleted = 0 and t1.type= :type