-- findSimpleRegions
select id, title, p_id as pId, sort
from com_region
where deleted_at is null
  and p_id != 0
order by sort desc

-- roleList
SELECT district.* FROM com_region district
LEFT JOIN com_region street on street.p_id = district.id
LEFT JOIN com_region community on street.id = community.p_id
WHERE district.type = 'district' AND district.deleted_at IS NULL
<#if regionPids??>
    AND district.id in (:regionPids)
</#if>
<#if regionIds??>
    AND street.id in (:regionIds)
</#if>
<#if regionCids??>
    AND community.id in (:regionCids)
</#if>
    UNION
SELECT street.* from com_region street
LEFT JOIN com_region community on street.id = community.p_id
WHERE street.type = 'subdistrict' AND street.deleted_at IS NULL
<#if regionPids??>
    AND street.p_id in (:regionPids)
</#if>
<#if regionIds??>
    AND street.id in (:regionIds)
</#if>
<#if regionCids??>
    AND community.id in (:regionCids)
</#if>
    UNION
SELECT community.* FROM com_region community
LEFT JOIN com_region street on street.id = community.p_id
WHERE community.type = 'community' AND community.deleted_at IS NULL
<#if regionPids??>
    AND street.p_id in (:regionPids)
</#if>
<#if regionIds??>
    AND community.p_id in (:regionIds)
</#if>
<#if regionCids??>
    AND community.id in (:regionCids)
</#if>