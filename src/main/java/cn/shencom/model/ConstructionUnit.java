package cn.shencom.model;

import cn.shencom.scloud.common.base.snowflake.SnowflakeGenerator;
import lombok.*;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Where;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 施工单位表
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Entity
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@DynamicUpdate
@DynamicInsert
@Table(name = "construction_unit")
@Where(clause = "is_deleted = 0")
@EntityListeners(AuditingEntityListener.class)
public class ConstructionUnit implements Serializable {
    // ===========================数据库字段================================
    /**
     * 主键ID
     */
    @Id
    @Column(name = "id")
    @GenericGenerator(name = "snowflake", strategy = SnowflakeGenerator.TYPE)
    @GeneratedValue(generator = "snowflake")
    private String id;

    /**
     * 施工单位名称
     */
    @Column(name = "name")
    private String name;

    /**
     * 管理员用户ID，关联移动端认证的管理员
     */
    @Column(name = "admin_user_id")
    private String adminUserId;

    /**
     * 管理员姓名
     */
    @Column(name = "admin_name")
    private String adminName;

    /**
     * 管理员联系方式
     */
    @Column(name = "admin_mobile")
    private String adminMobile;

    /**
     * 状态：0-无效，1-有效
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 创建时间
     */
    @Column(name = "created_at")
    @CreatedDate
    private java.util.Date createdAt;

    /**
     * 更新时间
     */
    @Column(name = "updated_at")
    @LastModifiedDate
    private java.util.Date updatedAt;

    /**
     * 创建人
     */
    @Column(name = "created_user")
    private String createdUser;

    /**
     * 更新人
     */
    @Column(name = "updated_user")
    private String updatedUser;

    /**
     * 是否删除：0-否，1-是
     */
    @Column(name = "is_deleted")
    private Integer isDeleted;

    /**
     * 删除时间
     */
    @Column(name = "deleted_at")
    private java.util.Date deletedAt;

    // =============================表关联==================================

    // ===========================自定义字段=================================

}
