package cn.shencom.model;

import cn.shencom.scloud.common.base.snowflake.SnowflakeGenerator;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.io.Serializable;

/**
 * AI场景-厂商场景关联表
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Entity
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "aim_scene_firm_relationship")
@EntityListeners(AuditingEntityListener.class)
public class AimSceneFirmRelationship implements Serializable {
//===========================数据库字段================================
    /**
     * 
     */
    @Id
    @Column(name = "id")
    @GenericGenerator(name = "snowflake", strategy = SnowflakeGenerator.TYPE)
    @GeneratedValue(generator = "snowflake")
    private String id;

    /**
     * 厂商名称
     */
    @Column(name = "scene_id")
    private String sceneId;

    /**
     * 厂商名称
     */
    @Column(name = "firm_id")
    private String firmId;

}
