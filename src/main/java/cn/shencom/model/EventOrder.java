package cn.shencom.model;

import cn.shencom.scloud.common.base.snowflake.SnowflakeGenerator;
import cn.shencom.scloud.common.jpa.scpage.ScLink;
import cn.shencom.scloud.common.util.Date2LongSerializer;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.*;
import org.hibernate.annotations.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.Table;
import javax.persistence.*;
import java.io.Serializable;

/**
 * 事件工单表
 *
 * <AUTHOR>
 * @since 2021-06-29
 */
@Entity
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@DynamicUpdate
@DynamicInsert
@Table(name = "event_order")
@Where(clause = "is_deleted = 0")
@EntityListeners(AuditingEntityListener.class)
public class EventOrder implements Serializable {
//===========================数据库字段================================

    /**
     * 工单ID
     */
    @Id
    @Column(name = "id")
    @GenericGenerator(name = "snowflake", strategy = SnowflakeGenerator.TYPE)
    @GeneratedValue(generator = "snowflake")
    private String id;

    /**
     * 辖区id
     */
    @Column(name = "region_pid")
    private String regionPid;

    /**
     * 街道id
     */
    @Column(name = "region_id")
    private String regionId;

    /**
     * 社区id
     */
    @Column(name = "region_cid")
    private String regionCid;


    /**
     * 项目id
     */
    @Column(name = "project_id")
    private String projectId;

    /**
     * 摄像头编号
     */
    @Column(name = "camera_no")
    private String cameraNo;

    /**
     * 事件编号
     */
    @Column(name = "event_code")
    private String eventCode;

    /**
     * 事件来源 1-海康 2-海康服务器 3-鲲云 4-深传 5-大华
     * 6-自研分析  8-锐明云  9-天天创科
     *
     * 13-aiot
     */
    @Column(name = "event_source")
    private Integer eventSource;

    /**
     * 事件描述
     */
    @Column(name = "event_desc")
    private String eventDesc;

    /**
     * 图片url列表
     */
    @Column(name = "pic")
    private String pic;

    /**
     * 处理事件时的实时截图
     */
    @Column(name = "disposing_pic")
    private String disposingPic;
    /**
     * 违规类型编号
     */
    @Column(name = "type_code")
    private String typeCode;

    /**
     * 违规类型名称
     */
    @Column(name = "type_name")
    private String typeName;

    /**
     * 持续时间/秒
     */
    @Column(name = "duration_time")
    private Integer durationTime;

    /**
     * 持续时间字符串
     */
    @Column(name = "duration_time_str")
    private String durationTimeStr;

    /**
     * 所属运营公司id
     */
    @Column(name = "company_id")
    private String companyId;

    /**
     * 来源  1-其他平台
     * 2-aiot
     */
    @Column(name = "origin")
    private Integer origin;

    /**
     * 状态，0-待处理，1-已处理，2-超时自动结束， 3-待复核， 4-已复核
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 复核状态，1-重新处理，2-识别有误
     */
    @Column(name = "review_status")
    private Integer reviewStatus;
    /**
     * 处理人id
     */
    @Column(name = "member_id")
    private String memberId;

    /**
     * 复核人id
     */
    @Column(name = "reviewer_id")
    private String reviewerId;
    /**
     * 备注
     */
    @Column(name = "memo")
    private String memo;

    /**
     * 处理意见
     */
    @Column(name = "dispose_desc")
    private String disposeDesc;

    /**
     * 处理图片id列表
     */
    @Column(name = "dispose_pic")
    private String disposePic;

    /**
     * 违规图片范围
     */
    @Column(name = "violation_areas")
    private String violationAreas;

    /**
     * 是否删除，0-未删除，1-删除
     */
    @Column(name = "is_deleted")
    private Integer isDeleted;

    /**
     * 处理时间
     */
    @Column(name = "disposed_at")
    @Temporal(TemporalType.TIMESTAMP)
    @JsonSerialize(using = Date2LongSerializer.class)
    private java.util.Date disposedAt;

    /**
     * 复核时间
     */
    @Column(name = "reviewed_at")
    @Temporal(TemporalType.TIMESTAMP)
    @JsonSerialize(using = Date2LongSerializer.class)
    private java.util.Date reviewedAt;
    /**
     * 事件时间
     */
    @Column(name = "evented_at")
    @Temporal(TemporalType.TIMESTAMP)
    @JsonSerialize(using = Date2LongSerializer.class)
    private java.util.Date eventedAt;

    /**
     * 事件最后时间
     */
    @Column(name = "event_latest_at")
    @Temporal(TemporalType.TIMESTAMP)
    @JsonSerialize(using = Date2LongSerializer.class)
    private java.util.Date eventLatestAt;

    /**
     * 创建时间
     */
    @Column(name = "created_at")
    @Temporal(TemporalType.TIMESTAMP)
    @JsonSerialize(using = Date2LongSerializer.class)
    @CreatedDate
    private java.util.Date createdAt;

    /**
     * 更新时间
     */
    @Column(name = "updated_at")
    @Temporal(TemporalType.TIMESTAMP)
    @JsonSerialize(using = Date2LongSerializer.class)
    @LastModifiedDate
    private java.util.Date updatedAt;

    /**
     * 结束时间
     */
    @Column(name = "deleted_at")
    @Temporal(TemporalType.TIMESTAMP)
    @JsonSerialize(using = Date2LongSerializer.class)
    private java.util.Date deletedAt;

    /**
     * 超级管理员处理人id
     */
    @Column(name = "user_id")
    private String userId;

    /**
     * 处理人角色类型，1-运营公司人员，2-超级管理员
     */
    @Column(name = "dispose_type")
    private Integer disposeType;

    /**
     * 最后指派到的运营公司id
     */
    @Column(name = "assign_company_id")
    private String assignCompanyId;

    /**
     * 是否分派，0-否，1-是
     */
    @Column(name = "is_assign")
    private Integer isAssign;

    /**
     * 是否推送统一代办成功，0-否，1-是
     */
    @Column(name = "is_todo")
    private Integer isTodo = 0;

    /**
     * 处理人姓名(第三方)
     */
    @Column(name = "dispose_name")
    private String disposeName;

    /**
     * 最后指派人
     */
    @Column(name = "assign_user_id")
    private String assignUserId;

    /**
     * 最后指派时间
     */
    @Column(name = "assigned_at")
    @Temporal(TemporalType.TIMESTAMP)
    @JsonSerialize(using = Date2LongSerializer.class)
    private java.util.Date assignedAt;

    /**
     * 是否违规溯源，0-否，1-是
     */
    @Column(name = "is_rule_source")
    private Integer isRuleSource;

    /**
     * 违规溯源图片
     */
    @Column(name = "source_pic")
    private String sourcePic;

    /**
     * 违规溯源视频
     */
    @Column(name = "source_video")
    private String sourceVideo;

    /**
     * 超时情况（多个英文逗号隔开）  1=一级处理人超时 2=二级处理人超时 3=三级处理人超时
     */
    @Column(name = "timeout_case")
    private String timeoutCase;

    /**
     * 一小时内是否及时处理:0=否、1=是
     */
    @Column(name = "is_on_time")
    private Integer isOnTime;

    /**
     * 是否已上报市级平台（在市级事件列表显示，并且在AI建设率中统计）。0-否，1-是
     */
    @Column(name = "is_city_stat")
    private Integer isCityStat;

    //=============================表关联==================================

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "region_pid", insertable = false, updatable = false)
    @NotFound(action = NotFoundAction.IGNORE)
    @JSONField(serialize = false)
    private ComRegion district;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "region_id", insertable = false, updatable = false)
    @NotFound(action = NotFoundAction.IGNORE)
    @JSONField(serialize = false)
    private ComRegion street;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "region_cid", insertable = false, updatable = false)
    @NotFound(action = NotFoundAction.IGNORE)
    @JSONField(serialize = false)
    private ComRegion village;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "project_id", insertable = false, updatable = false)
    @NotFound(action = NotFoundAction.IGNORE)
    @JSONField(serialize = false)
    private SporadicProject project;


    @OneToOne(targetEntity = EventCameraPoint.class, fetch = FetchType.LAZY)
    @JoinColumn(
            name = "camera_no", referencedColumnName = "monitor_no", insertable = false, unique = false,
            updatable = false,
            foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT)
    )
    @NotFound(action = NotFoundAction.IGNORE)
    private EventCameraPoint camera;


    @OneToOne(targetEntity = EventOrderSource.class, fetch = FetchType.LAZY)
    @JoinColumn(
            name = "event_source", referencedColumnName = "code", insertable = false, unique = false,
            updatable = false,
            foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT)
    )
    @NotFound(action = NotFoundAction.IGNORE)
    private EventOrderSource eventOrderSource;
    //===========================自定义字段=================================

    @Transient
    @ScLink(objName = "district", filed = "title")
    private String districtName;

    @Transient
    @ScLink(objName = "street", filed = "title")
    private String streetName;

    @Transient
    @ScLink(objName = "village", filed = "title")
    private String villageName;

    @Transient
    @ScLink(objName = "project", filed = "name")
    private String projectName;

    @Transient
    @ScLink(objName = "camera",filed = "type")
    private Integer cameraType;

    @Transient
    @ScLink(objName = "eventOrderSource",filed = "name")
    private String eventSourceStr;
}
