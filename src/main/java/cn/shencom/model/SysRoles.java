package cn.shencom.model;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import cn.shencom.scloud.common.base.snowflake.SnowflakeGenerator;
import cn.shencom.scloud.common.jpa.scpage.ScLink;
import cn.shencom.scloud.common.jpa.util.query.ScBaseBean;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.*;
import java.io.Serializable;
import java.util.List;

/**
 * sys_roles实体类
 *
 * <AUTHOR>
@Entity
@Table(name = "sys_roles")
@Getter
@Setter
@NoArgsConstructor
@DynamicUpdate
@ExcelTarget("sys_roles")
public class SysRoles extends ScBaseBean implements Serializable {
//===========================数据库字段================================
    /**
     * id
     */
    @Id
    @Column(name = "id")
    @GenericGenerator(name = "snowflake", strategy = SnowflakeGenerator.TYPE)
    @GeneratedValue(generator = "snowflake")
    @Excel(name = "id", needMerge = false, width = 25)
    private String id;
    /**
     * 角色展示名
     */
    @Column(name = "display_name")
    @Excel(name = "角色展示名", needMerge = false, width = 25)
    private String displayName;
    /**
     * 角色描述
     */
    @Column(name = "description")
    @Excel(name = "角色描述", needMerge = false, width = 25)
    private String description;
    /**
     * 创建时间
     */
    @Column(name = "created_at")
    @Excel(name = "创建时间", needMerge = false, width = 25)
    @Temporal(TemporalType.TIMESTAMP)
    private java.util.Date createdAt;
    /**
     * 更新时间
     */
    @Column(name = "updated_at")
    @Excel(name = "更新时间", needMerge = false, width = 25)
    @Temporal(TemporalType.TIMESTAMP)
    private java.util.Date updatedAt;
    /**
     * 删除时间
     */
    @Column(name = "deleted_at")
    @Excel(name = "删除时间", needMerge = false, width = 25)
    @Temporal(TemporalType.TIMESTAMP)
    private java.util.Date deletedAt;
    /**
     * 角色标识
     */
    @Column(name = "name")
    @Excel(name = "角色标识", needMerge = false, width = 25)
    private String name;
    /***/
    @Column(name = "active")
    @Excel(name = "", needMerge = false, width = 25)
    private Integer active;


    /**
     *  特殊标记，此字段为1时，需要联表sys_role_user_organization查询，有记录时，角色才能生效
     */
    @Column(name = "flag")
    private Integer flag;

    //===========================表关联====================================
    @ManyToMany(targetEntity = SysPermissions.class,
            cascade = CascadeType.REFRESH,
            fetch = FetchType.LAZY)
    @JoinTable(
            name = "sys_permission_role",
            joinColumns = {@JoinColumn(name = "role_id", referencedColumnName = "id", insertable = false, updatable = false)},
            inverseJoinColumns = {@JoinColumn(name = "permission_id", referencedColumnName = "id", insertable = false, updatable = false)},
            foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT)
    )
    @JSONField(serialize = false)
    @NotFound(action = NotFoundAction.IGNORE)
    private List<SysPermissions> permissions;

    @ManyToMany(targetEntity = SysUsers.class,
            cascade = CascadeType.REFRESH,
            fetch = FetchType.LAZY)
    @JoinTable(
            name = "sys_role_user",
            joinColumns = {@JoinColumn(name = "role_id", insertable = false, updatable = false)},
            inverseJoinColumns = {@JoinColumn(name = "user_id", insertable = false, updatable = false)},
//                joinColumns={@JoinColumn(name="role_id",referencedColumnName="id", insertable=false, updatable=false)},
//                inverseJoinColumns={@JoinColumn(name="user_id",referencedColumnName="id", insertable=false, updatable=false)},
            foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT)
    )
    @JSONField(serialize = false)
    @NotFound(action = NotFoundAction.IGNORE)
    private List<SysUsers> users;


    //===========================自定义字段=================================
    @Transient
    @ScLink(objName = "users", filed = "id")
    private String userId;
    @Transient
    private String roleNameLike;
    @Transient
    @ScLink(objName = "permissions", filed = "id")
    private String permissionId;
}
