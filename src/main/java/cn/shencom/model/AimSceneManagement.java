package cn.shencom.model;

import cn.shencom.scloud.common.base.snowflake.SnowflakeGenerator;
import lombok.*;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Where;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.io.Serializable;
import java.util.List;

/**
 * AI场景管理
 *
 * <AUTHOR>
 * @since 2022-07-26
 */
@Entity
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@DynamicUpdate
@DynamicInsert
@Table(name = "aim_scene_management")
@Where(clause = "is_deleted = 0")
@EntityListeners(AuditingEntityListener.class)
public class AimSceneManagement implements Serializable {
//===========================数据库字段================================
    /**
     * 
     */
    @Id
    @Column(name = "id")
    @GenericGenerator(name = "snowflake", strategy = SnowflakeGenerator.TYPE)
    @GeneratedValue(generator = "snowflake")
    private String id;

    /**
     * 场景名称
     */
    @Column(name = "name")
    private String name;

    /**
     * 场景编号
     */
    @Column(name = "code")
    private String code;

    /**
     * 场景类别Id
     */
    @Column(name = "scene_category_id")
    private String sceneCategoryId;

    /**
     * 场景类型：0-基本 1-复合
     */
    @Column(name = "type")
    private Integer type;

    /**
     * 模式：1-现场宣导 2-事件处置（逗号分隔）
     */
    @Column(name = "mode")
    private String mode;

    /**
     * 标签（逗号分隔）
     */
    @Column(name = "tags")
    private String tags;

    /**
     * 图片（逗号分隔）
     */
    @Column(name = "pics")
    private String pics;

    /**
     * 场景说明
     */
    @Column(name = "directions")
    private String directions;

    /**
     * 厂商场景关联数
     */
    @Column(name = "firm_num")
    private Integer firmNum;

    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;

    /**
     * 是否删除：1-是 0-否
     */
    @Column(name = "is_deleted")
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @Column(name = "created_at")
    @CreatedDate
    private java.util.Date createdAt;

    /**
     * 更新时间
     */
    @Column(name = "updated_at")
    @LastModifiedDate
    private java.util.Date updatedAt;

//=============================表关联==================================

    @OneToMany(targetEntity = AimSceneFirmRelationship.class, fetch = FetchType.LAZY)
    @JoinColumn(name = "scene_id", referencedColumnName = "id", insertable = false, updatable = false,
            foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
    private List<AimSceneFirmRelationship> aimSceneFirmRelationships;
//===========================自定义字段=================================

}
