package cn.shencom.model;

import cn.shencom.scloud.common.base.snowflake.SnowflakeGenerator;
import lombok.*;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.io.Serializable;

/**
 * aiot_scene_category实体类
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Entity
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@DynamicUpdate
@DynamicInsert
@Table(name = "aiot_scene_category")
public class AiotSceneCategory implements Serializable {
//===========================数据库字段================================
    /**
     * 
     */
    @Id
    @Column(name = "id")
    @GenericGenerator(name = "snowflake", strategy = SnowflakeGenerator.TYPE)
    @GeneratedValue(generator = "snowflake")
    private String id;

    /**
     * 场景一级分类代码
     */
    @Column(name = "cate_code")
    private String cateCode;

    /**
     * 场景代码
     */
    @Column(name = "scene_code")
    private String sceneCode;

//=============================表关联==================================


//===========================自定义字段=================================

}
