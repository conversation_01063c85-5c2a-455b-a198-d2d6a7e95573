package cn.shencom.model;

import cn.shencom.scloud.common.base.snowflake.SnowflakeGenerator;
import cn.shencom.scloud.common.jpa.scpage.ScLink;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.*;
import org.hibernate.annotations.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 区域表
 *
 * <AUTHOR>
 * @since 2025-04-19
 */
@Entity
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@DynamicUpdate
@DynamicInsert
@Table(name = "com_region")
@Where(clause = "deleted_at is null")
@EntityListeners(AuditingEntityListener.class)
public class ComRegion implements Serializable {
//===========================数据库字段================================
    /**
     * 行政区划
     */
    @Id
    @Column(name = "id")
    @GenericGenerator(name = "snowflake", strategy = SnowflakeGenerator.TYPE)
    @GeneratedValue(generator = "snowflake")
    private String id;

    /**
     * 父级ID
     */
    @Column(name = "p_id")
    private String pId;

    /**
     * 类型， province, city, district, subdistrict, community, neighborhood, residential community
     */
    @Column(name = "type")
    private String type;

    /**
     * 名称
     */
    @Column(name = "title")
    private String title;

    /**
     * 名称拼音
     */
    @Column(name = "spell")
    private String spell;

    /**
     * 名称拼音首字母
     */
    @Column(name = "spell_short")
    private String spellShort;

    /**
     * 简述
     */
    @Column(name = "brief")
    private String brief;

    /**
     * 区域块点位id
     */
    @Column(name = "fence_id")
    private String fenceId;

    /**
     * 图片
     */
    @Column(name = "cover")
    private String cover;

    /**
     * 内容
     */
    @Column(name = "content")
    private String content;

    /**
     * 图片资源编号
     */
    @Column(name = "resource_id")
    private String resourceId;

    /**
     * 创建时间
     */
    @Column(name = "created_at")
    @CreatedDate
    private java.util.Date createdAt;

    /**
     * 删除时间
     */
    @Column(name = "deleted_at")
    private java.util.Date deletedAt;

    /**
     * 更新时间
     */
    @Column(name = "updated_at")
    @LastModifiedDate
    private java.util.Date updatedAt;

    /**
     * 是否开启
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 父级ID（未使用）
     */
    @Column(name = "pid")
    private String pid;

    /**
     * 排序值
     */
    @Column(name = "sort")
    private Integer sort;

    /**
     * 点位id
     */
    @Column(name = "poi_id")
    private String poiId;

    /**
     * 经度
     */
    @Column(name = "longitude")
    private java.math.BigDecimal longitude;

    /**
     * 纬度
     */
    @Column(name = "latitude")
    private java.math.BigDecimal latitude;

    /**
     * 行政区划在高德的id
     */
    @Column(name = "gd_id")
    private String gdId;

    /**
     * 行政区划在高德的adcode
     */
    @Column(name = "gd_adcode")
    private String gdAdcode;

    /**
     * 区id（未使用）
     */
    @Column(name = "district_id")
    private String districtId;

    /**
     * 行政区划编码
     */
    @Column(name = "region_code")
    private String regionCode;

//=============================表关联==================================

//===========================自定义字段=================================

}
