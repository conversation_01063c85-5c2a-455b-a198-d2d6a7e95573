package cn.shencom.model;

import cn.shencom.scloud.common.base.snowflake.SnowflakeGenerator;
import lombok.*;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Where;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.io.Serializable;
import java.util.List;

/**
 * 小散工程-工程人员
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Entity
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@DynamicUpdate
@DynamicInsert
@Table(name = "engineering_members")
@Where(clause = "is_deleted = 0")
@EntityListeners(AuditingEntityListener.class)
public class EngineeringMembers implements Serializable {
//===========================数据库字段================================
    /**
     * ID
     */
    @Id
    @Column(name = "id")
    @GenericGenerator(name = "snowflake", strategy = SnowflakeGenerator.TYPE)
    @GeneratedValue(generator = "snowflake")
    private String id;

    /**
     * 用户id
     */
    @Column(name = "user_id")
    private String userId;

    /**
     * 成员姓名
     */
    @Column(name = "realname")
    private String realname;

    /**
     * 手机号码
     */
    @Column(name = "mobile")
    private String mobile;

    /**
     * 职位类型
     */
    @Column(name = "type")
    private Integer type;

    /**
     * 身份证号
     */
    @Column(name = "id_card")
    private String idCard;

    /**
     * 工种名称
     */
    @Column(name = "work_type_name")
    private String workTypeName;

    /**
     * 证书编号
     */
    @Column(name = "certificate_number")
    private String certificateNumber;

    /**
     * 证书有效日期-开始时间
     */
    @Column(name = "certificate_start_date")
    private java.util.Date certificateStartDate;

    /**
     * 证书有效日期-结束时间
     */
    @Column(name = "certificate_end_date")
    private java.util.Date certificateEndDate;

    /**
     * 证书图片
     */
    @Column(name = "certificate_pic")
    private String certificatePic;

    /**
     * 描述
     */
    @Column(name = "desc")
    private String desc;

    /**
     * 有效状态，0-无效，1-有效
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 创建时间
     */
    @Column(name = "created_at")
    @CreatedDate
    private java.util.Date createdAt;

    /**
     * 最后更新时间
     */
    @Column(name = "updated_at")
    @LastModifiedDate
    private java.util.Date updatedAt;

    /**
     * 是否删除
     */
    @Column(name = "is_deleted")
    private Integer isDeleted;

    /**
     * 删除时间
     */
    @Column(name = "deleted_at")
    private java.util.Date deletedAt;

//=============================表关联==================================



    @OneToMany(targetEntity = EngineeringMembersProjectRelate.class, fetch = FetchType.LAZY)
    @JoinColumn(name = "member_id", referencedColumnName = "id", insertable = false, updatable = false,
            foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
    private List<EngineeringMembersProjectRelate> membersProjectRelateList;



//===========================自定义字段=================================

}
