package cn.shencom.model;

import cn.shencom.scloud.common.base.snowflake.SnowflakeGenerator;
import cn.shencom.scloud.common.properties.DesensitizationProperties;
import lombok.*;
import org.hibernate.annotations.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 用户表
 *
 * <AUTHOR>
 * @since 2025-04-20
 */
@Entity
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@DynamicUpdate
@DynamicInsert
@Table(name = "sys_users")
@Where(clause = "deleted_at is null")
@EntityListeners(AuditingEntityListener.class)
public class SysUsers implements Serializable {
//===========================数据库字段================================
    /**
     * 
     */
    @Id
    @Column(name = "id")
    @GenericGenerator(name = "snowflake", strategy = SnowflakeGenerator.TYPE)
    @GeneratedValue(generator = "snowflake")
    private String id;

    /**
     * 用户登录名
     */
    @Column(name = "username")
    private String username;

    /**
     * 用户密码
     */
    @Column(name = "password")
    private String password;

    /**
     * 用户屏显昵称，可以不同用户登录名
     */
    @Column(name = "nickname")
    private String nickname;

    /**
     * 邮箱
     */
    @Column(name = "email")
    private String email;

    /**
     * 用户真实姓名
     */
    @Column(name = "realname")
    @ColumnTransformer(
            read = "CAST(AES_DECRYPT(UNHEX(realname), '" + DesensitizationProperties.AES_KEY + "') as char(128))",
            write = "HEX(AES_ENCRYPT(?, '" + DesensitizationProperties.AES_KEY + "'))"
    )
    private String realname;

    /**
     * 身份证号
     */
    @Column(name = "pid")
    @ColumnTransformer(
            read = "CAST(AES_DECRYPT(UNHEX(pid), '" + DesensitizationProperties.AES_KEY + "') as char(128))",
            write = "HEX(AES_ENCRYPT(?, '" + DesensitizationProperties.AES_KEY + "'))"
    )
    private String pid;

    /**
     * 身份证证件正面（印有国徽图案、签发机关和有效期限）照片
     */
    @Column(name = "pid_card_thumb1")
    private String pidCardThumb1;

    /**
     * 身份证证件反面（印有个人基本信息和照片）照片
     */
    @Column(name = "pid_card_thumb2")
    private String pidCardThumb2;

    /**
     * 用户个人图像
     */
    @Column(name = "avatar")
    private String avatar;

    /**
     * 手机号码
     */
    @Column(name = "phone")
    @ColumnTransformer(
            read = "CAST(AES_DECRYPT(UNHEX(phone), '" + DesensitizationProperties.AES_KEY + "') as char(128))",
            write = "HEX(AES_ENCRYPT(?, '" + DesensitizationProperties.AES_KEY + "'))"
    )
    private String phone;

    /**
     * 联系地址
     */
    @Column(name = "address")
    private String address;

    /**
     * 被软删除时间
     */
    @Column(name = "deleted_at")
    private java.util.Date deletedAt;

    /**
     * 创建时间
     */
    @Column(name = "created_at")
    @CreatedDate
    private java.util.Date createdAt;

    /**
     * 修改更新时间
     */
    @Column(name = "updated_at")
    @LastModifiedDate
    private java.util.Date updatedAt;

    /**
     * 性别：1，男；2，女；3，保密
     */
    @Column(name = "sex")
    private Integer sex;

    /**
     * 生日
     */
    @Column(name = "birth")
    private java.util.Date birth;



    /**
     * 上一次选择的组织id-管理端
     */
    @Column(name = "last_organization")
    private String lastOrganization;


    /**
     * 上一次选择的组织id-移动端
     */
    @Column(name = "last_organization_mobile")
    private String lastOrganizationMobile;

//=============================表关联==================================


//===========================自定义字段=================================

}
