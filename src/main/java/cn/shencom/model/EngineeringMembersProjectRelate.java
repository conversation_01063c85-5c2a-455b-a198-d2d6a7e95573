package cn.shencom.model;

import cn.shencom.scloud.common.base.snowflake.SnowflakeGenerator;
import cn.shencom.scloud.common.jpa.scpage.ScLink;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.*;
import org.hibernate.annotations.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 小散工程-工程人员关联项目表
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Entity
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@DynamicUpdate
@DynamicInsert
@Table(name = "engineering_members_project_relate")
@Where(clause = "is_deleted = 0")
@EntityListeners(AuditingEntityListener.class)
public class EngineeringMembersProjectRelate implements Serializable {
//===========================数据库字段================================
    /**
     * ID
     */
    @Id
    @Column(name = "id")
    @GenericGenerator(name = "snowflake", strategy = SnowflakeGenerator.TYPE)
    @GeneratedValue(generator = "snowflake")
    private String id;

    /**
     * 成员id，关联engineering_members
     */
    @Column(name = "member_id")
    private String memberId;

    /**
     * relateId，engineering_members_relate
     */
    @Column(name = "relate_id")
    private String relateId;

    /**
     * 组织id,关联xsgc_organization
     */
    @Column(name = "organization_id")
    private String organizationId;

    /**
     * 项目id,关联sporadic_project
     */
    @Column(name = "project_id")
    private String projectId;

    /**
     * 创建时间
     */
    @Column(name = "created_at")
    @CreatedDate
    private java.util.Date createdAt;

    /**
     * 最后更新时间
     */
    @Column(name = "updated_at")
    @LastModifiedDate
    private java.util.Date updatedAt;

    /**
     * 是否删除
     */
    @Column(name = "is_deleted")
    private Integer isDeleted;

    /**
     * 删除时间
     */
    @Column(name = "deleted_at")
    private java.util.Date deletedAt;

//=============================表关联==================================


    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "project_id", insertable = false, updatable = false)
    @NotFound(action = NotFoundAction.IGNORE)
    @JSONField(serialize = false, deserialize = false)
    private SporadicProject project;


//===========================自定义字段=================================


    @Transient
    @ScLink(objName = "project", filed = "regionPid")
    private String regionPid;

    @Transient
    @ScLink(objName = "project", filed = "regionId")
    private String regionId;

    @Transient
    @ScLink(objName = "project", filed = "regionCid")
    private String regionCid;

}
