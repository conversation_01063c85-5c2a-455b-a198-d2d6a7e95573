package cn.shencom.model;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import cn.shencom.scloud.common.base.snowflake.SnowflakeGenerator;
import cn.shencom.scloud.common.jpa.util.query.ScBaseBean;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Set;

/**
 * sys_role_user实体类
 *
 * <AUTHOR>
@Entity
@Table(name = "sys_role_user")
@Getter
@Setter
@NoArgsConstructor
@DynamicUpdate
@ExcelTarget("sys_role_user")
public class SysRoleUser extends ScBaseBean implements Serializable {
//===========================数据库字段================================
    /***/
    @Id
    @Column(name = "id")
    @GenericGenerator(name = "snowflake", strategy = SnowflakeGenerator.TYPE)
    @GeneratedValue(generator = "snowflake")
    @Excel(name = "", needMerge = false, width = 25)
    private String id;
    /**
     * 用户
     */
    @Column(name = "user_id")
    @Excel(name = "用户", needMerge = false, width = 25)
    private String userId;
    /**
     * 角色
     */
    @Column(name = "role_id")
    @Excel(name = "角色", needMerge = false, width = 25)
    private String roleId;
//===========================表关联====================================


    //===========================自定义字段=================================
    @Transient
    @JSONField(serialize = false)
    private String userIds;
    @Transient
    @JSONField(serialize = false)
    private String roleIds;

    @Transient
    @JSONField(serialize = false)
    private Set<String> roleNames;

    @Transient
    private String roleName;

    /**
     * 是否自动创建不存在的角色名，默认不自动创建
     */
    @Transient
    private Boolean autoCreateByRoleName;
}
