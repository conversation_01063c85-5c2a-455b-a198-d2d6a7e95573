package cn.shencom.model;

import cn.shencom.scloud.common.base.snowflake.SnowflakeGenerator;
import cn.shencom.scloud.common.util.Date2LongSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Where;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 区域块/线路
 *
 * <AUTHOR>
 * @since 2022-01-11
 */
@Entity
@Getter
@Setter
@ToString
@NoArgsConstructor
@DynamicUpdate
@DynamicInsert
@Table(name = "gis_poi")
@Where(clause = "deleted_at is null")
@EntityListeners(AuditingEntityListener.class)
public class GisPoi implements Serializable {
//===========================数据库字段================================
    /**
     * 区域块/线路
     */
    @Id
    @Column(name = "id")
    @GenericGenerator(name = "snowflake", strategy = SnowflakeGenerator.TYPE)
    @GeneratedValue(generator = "snowflake")
    private String id;
    /**
     * 纬度
     */
    @Column(name = "lat")
    private String lat;
    /**
     * 经度
     */
    @Column(name = "lng")
    private String lng;
    /**
     * 地址
     */
    @Column(name = "addr")
    private String addr;
    /**
     * 备注
     */
    @Column(name = "memo")
    private String memo;
    /**
     * 创建时间
     */
    @Column(name = "created_at")
    @Temporal(TemporalType.TIMESTAMP)
    @JsonSerialize(using = Date2LongSerializer.class)
    @CreatedDate
    private java.util.Date createdAt;
    /**
     * 删除时间
     */
    @Column(name = "deleted_at")
    @Temporal(TemporalType.TIMESTAMP)
    @JsonSerialize(using = Date2LongSerializer.class)
    private java.util.Date deletedAt;
    /**
     * 修改时间
     */
    @Column(name = "updated_at")
    @Temporal(TemporalType.TIMESTAMP)
    @JsonSerialize(using = Date2LongSerializer.class)
    @LastModifiedDate
    private java.util.Date updatedAt;
    /**
     * 有效时间
     */
    @Column(name = "valid_time")
    @Temporal(TemporalType.TIMESTAMP)
    @JsonSerialize(using = Date2LongSerializer.class)
    private java.util.Date validTime;
    /**
     * 数据添加者
     */
    @Column(name = "author")
    private String author;
//=============================表关联==================================


//===========================自定义字段=================================

}
