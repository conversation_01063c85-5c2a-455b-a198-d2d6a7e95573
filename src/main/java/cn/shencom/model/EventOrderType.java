package cn.shencom.model;

import cn.shencom.scloud.common.base.snowflake.SnowflakeGenerator;
import lombok.*;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Where;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 监控事件类型
 *
 * <AUTHOR>
 * @since 2023-11-22
 */
@Entity
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@DynamicUpdate
@DynamicInsert
@Table(name = "event_order_type")
@Where(clause = "is_deleted = 0")
@EntityListeners(AuditingEntityListener.class)
public class EventOrderType implements Serializable {
//===========================数据库字段================================
    /**
     * 主键自增
     */
    @Id
    @Column(name = "id")
    @GenericGenerator(name = "snowflake", strategy = SnowflakeGenerator.TYPE)
    @GeneratedValue(generator = "snowflake")
    private String id;

    /**
     * 事件名称
     */
    @Column(name = "title")
    private String title;

    /**
     * 排序
     */
    @Column(name = "sort")
    private Integer sort;

    /**
     * 是否事件枚举 0-否 1-是
     */
    @Column(name = "is_self")
    private Integer isSelf;

    /**
     * 是否第三方事件枚举 0-否 1-是
     */
    @Column(name = "is_open")
    private Integer isOpen;

    /**
     * 删除标记 1-已删除 0-默认
     */
    @Column(name = "is_deleted")
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @Column(name = "created_at")
    @CreatedDate
    private java.util.Date createdAt;

    /**
     * 更新时间
     */
    @Column(name = "updated_at")
    @LastModifiedDate
    private java.util.Date updatedAt;

//=============================表关联==================================


//===========================自定义字段=================================

}
