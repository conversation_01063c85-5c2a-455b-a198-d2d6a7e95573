package cn.shencom.model;

import cn.shencom.scloud.common.base.snowflake.SnowflakeGenerator;
import lombok.*;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Where;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 小散工程-组织团队成员区域关联关系
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Entity
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@DynamicUpdate
@DynamicInsert
@Table(name = "fn_rmsv3_members_type_relate_binding")
@Where(clause = "is_deleted = 0")
@EntityListeners(AuditingEntityListener.class)
public class FnRmsv3MembersTypeRelateBinding implements Serializable {
//===========================数据库字段================================
    /**
     * 主键id
     */
    @Id
    @Column(name = "id")
    @GenericGenerator(name = "snowflake", strategy = SnowflakeGenerator.TYPE)
    @GeneratedValue(generator = "snowflake")
    private String id;

    /**
     * fn_rmsv3_members_type_relate id
     */
    @Column(name = "relate_id")
    private String relateId;

    /**
     * 区
     */
    @Column(name = "region_pid")
    private String regionPid;

    /**
     * 街道
     */
    @Column(name = "region_id")
    private String regionId;

    /**
     * 社区
     */
    @Column(name = "region_cid")
    private String regionCid;

    /**
     * 创建时间
     */
    @Column(name = "created_at")
    @CreatedDate
    private java.util.Date createdAt;

    /**
     * 修改时间
     */
    @Column(name = "updated_at")
    @LastModifiedDate
    private java.util.Date updatedAt;

    /**
     * 是否删除
     */
    @Column(name = "is_deleted")
    private Integer isDeleted;

//=============================表关联==================================


//===========================自定义字段=================================

}
