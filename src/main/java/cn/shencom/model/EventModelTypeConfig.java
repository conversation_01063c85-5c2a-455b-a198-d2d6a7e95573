package cn.shencom.model;

import cn.shencom.scloud.common.base.snowflake.SnowflakeGenerator;
import lombok.*;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Where;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 设备型号配置表
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Entity
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@DynamicUpdate
@DynamicInsert
@Table(name = "event_model_type_config")
@Where(clause = "is_deleted = 0")
@EntityListeners(AuditingEntityListener.class)
public class EventModelTypeConfig implements Serializable {
//===========================数据库字段================================
    /**
     * 
     */
    @Id
    @Column(name = "id")
    @GenericGenerator(name = "snowflake", strategy = SnowflakeGenerator.TYPE)
    @GeneratedValue(generator = "snowflake")
    private String id;

    /**
     * 深传设备型号
     */
    @Column(name = "sc_model_no")
    private String scModelNo;

    /**
     * 海康设备型号
     */
    @Column(name = "hk_model_no")
    private String hkModelNo;

    /**
     * 图片,在线图标
     */
    @Column(name = "pic")
    private String pic;

    /**
     * 离线图标
     */
    @Column(name = "off_line_pic")
    private String offLinePic;

    /**
     * 是否删除，1-删除，0-未删除
     */
    @Column(name = "is_deleted")
    private Integer isDeleted;

    /**
     * 
     */
    @Column(name = "created_at")
    @CreatedDate
    private java.util.Date createdAt;

    /**
     * 
     */
    @Column(name = "updated_at")
    @LastModifiedDate
    private java.util.Date updatedAt;

    /**
     * 
     */
    @Column(name = "deleted_at")
    private java.util.Date deletedAt;

//=============================表关联==================================


//===========================自定义字段=================================

}
