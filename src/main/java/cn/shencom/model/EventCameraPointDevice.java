package cn.shencom.model;

import cn.shencom.scloud.common.base.snowflake.SnowflakeGenerator;
import cn.shencom.scloud.common.jpa.scpage.ScLink;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.*;
import org.hibernate.annotations.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.List;

/**
 * 摄像头表
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Entity
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@DynamicUpdate
@DynamicInsert
@Table(name = "event_camera_point_device")
@Where(clause = "is_deleted = 0")
@EntityListeners(AuditingEntityListener.class)
public class EventCameraPointDevice implements Serializable {
//===========================数据库字段================================
    /**
     * 主键id
     */
    @Id
    @Column(name = "id")
    @GenericGenerator(name = "snowflake", strategy = SnowflakeGenerator.TYPE)
    @GeneratedValue(generator = "snowflake")
    private String id;

    /**
     * 序列号
     */
    @Column(name = "serial_no")
    private String serialNo;

    /**
     * 监控设备型号
     */
    @Column(name = "model_no")
    private String modelNo;

    /**
     * 国标视频编码
     */
    @Column(name = "sip_user_id")
    private String sipUserId;

    /**
     * 类型 event_camera_point_type
     */
    @Column(name = "type")
    private Integer type;

    /**
     * 小散项目id
     */
    @Column(name = "project_id")
    private String projectId;

    /**
     * 感知类型
     */
    @Column(name = "perception_types")
    private String perceptionTypes;
    /**
     * 是否语音播报，0-不是，1-是
     */
    @Column(name = "voice_broadcast")
    private Integer voiceBroadcast;


    /**
     * 是否健康 0-异常 1-健康
     */
    @Column(name = "is_healthy")
    private Integer isHealthy;

    /**
     * 是否智能，0-不是，1-是
     */
    @Column(name = "smart")
    private Integer smart;

    /**
     * 备注
     */
    @Column(name = "memo")
    private String memo;

    /**
     * 是否删除 0-未删除 1-已删除
     */
    @Column(name = "is_deleted")
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @Column(name = "created_at")
    @CreatedDate
    private java.util.Date createdAt;

    /**
     * 更新时间
     */
    @Column(name = "updated_at")
    @LastModifiedDate
    private java.util.Date updatedAt;

//=============================表关联==================================

    @OneToMany(targetEntity = EventCameraPoint.class, fetch = FetchType.LAZY)
    @JoinColumn(
            name = "device_id", referencedColumnName = "id", insertable = false, updatable = false,
            foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT)
    )
    private List<EventCameraPoint> eventCameraPointList;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "project_id", insertable = false, updatable = false)
    @NotFound(action = NotFoundAction.IGNORE)
    @JSONField(serialize = false)
    private SporadicProject project;

//===========================自定义字段=================================

    /**
     * 当前入驻经营区
     */
    @ScLink(objName = "project", filed = "name")
    @Transient
    private String projectName;

    @Transient
    @ScLink(objName = "project.pCate", filed = "name")
    private String pCateName;
    @Transient
    @ScLink(objName = "project.cate", filed = "name")
    private String cateName;

    /**
     * 经营区 区id
     */
    @ScLink(objName = "project", filed = "regionPid")
    @Transient
    private String regionPid;

    /**
     * 经营区 街道id
     */
    @ScLink(objName = "project", filed = "regionId")
    @Transient
    private String regionId;

    /**
     * 经营区 社区id
     */
    @ScLink(objName = "project", filed = "regionCid")
    @Transient
    private String regionCid;

    /**
     * 项目所属区名称
     */
    @ScLink(objName = "project.district", filed = "title")
    @Transient
    private String districtName;

    /**
     * 项目所属街道名称
     */
    @ScLink(objName = "project.street", filed = "title")
    @Transient
    private String streetName;

    /**
     * 项目所属社区名称
     */
    @ScLink(objName = "project.village", filed = "title")
    @Transient
    private String villageName;
}
