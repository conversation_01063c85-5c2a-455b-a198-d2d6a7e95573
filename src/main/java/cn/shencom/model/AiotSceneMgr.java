package cn.shencom.model;

import cn.shencom.scloud.common.base.snowflake.SnowflakeGenerator;
import cn.shencom.scloud.common.jpa.scpage.ScLink;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.*;
import org.hibernate.annotations.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * 场景类别
 *
 * <AUTHOR>
 * @since 2024-08-03
 */
@Entity
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@DynamicUpdate
@DynamicInsert
@Table(name = "aiot_scene_mgr")
@Where(clause = "is_deleted = 0")
@EntityListeners({AuditingEntityListener.class})
public class AiotSceneMgr  implements Serializable {
//===========================数据库字段================================
    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    @GenericGenerator(name = "snowflake", strategy = SnowflakeGenerator.TYPE)
    @GeneratedValue(generator = "snowflake")
    private String id;

    /**
     * 父级ID
     */
    @Column(name = "p_id")
    private String pId = "0";

    /**
     * 场景类别编号
     */
    @Column(name = "scene_code")
    private String sceneCode;

    /**
     * 场景类别名称
     */
    @Column(name = "scene_name")
    private String sceneName;

    /**
     * 场景类别说明
     */
    @Column(name = "scene_illustrate")
    private String sceneIllustrate;

    /**
     * 场景类别全称
     */
    @Column(name = "whole_ids")
    private String wholeIds;

    /**
     * 场景类别全称
     */
    @Column(name = "whole_name")
    private String wholeName;

    /**
     * 创建人
     */
    @Column(name = "created_by")
    private String createdBy;

    /**
     * 创建人姓名
     */
    @Column(name = "created_by_name")
    private String createdByName;

    /**
     * 修改人
     */
    @Column(name = "updated_by")
    private String updatedBy;

    /**
     * 修改人姓名
     */
    @Column(name = "updated_by_name")
    private String updatedByName;

    /**
     * 创建时间
     */
    @Column(name = "created_at")
    @CreatedDate
    private java.util.Date createdAt;

    /**
     * 更新时间
     */
    @Column(name = "updated_at")
    @LastModifiedDate
    private Timestamp updatedAt;

    /**
     * 1:删除；0:未删除
     */
    @Column(name = "is_deleted")
    private Integer isDeleted;


//=============================表关联==================================


    //===========================自定义字段=================================
    @ManyToOne(targetEntity = AiotSceneMgr.class, fetch = FetchType.LAZY)
    @JoinColumn(
            name = "p_id", referencedColumnName = "id", insertable = false, updatable = false
    )
    @NotFound(action = NotFoundAction.IGNORE)
    @JSONField(serialize = false)
    private AiotSceneMgr aiotSceneMgr;

    @OneToMany(targetEntity = AiotSceneMgr.class, fetch = FetchType.LAZY)
    @JoinColumn(
            name = "p_id", referencedColumnName = "id", insertable = false, updatable = false
    )
    @Where(clause = "is_deleted=0")
    @JSONField(serialize = false)
    private List<AiotSceneMgr> children;

    //===========================自定义字段=================================

    @ScLink(objName = "aiotSceneMgr", filed = "title")
    @Transient
    private String parentTitle;
    @ScLink(objName = "aiotSceneMgr", filed = "pId")
    @Transient
    private String childrenSceneId;
    @ScLink(objName = "children", filed = "title")
    @Transient
    private String chikdrenSceneName;
    @Transient
    private int num;

}