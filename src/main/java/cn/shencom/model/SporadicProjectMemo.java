package cn.shencom.model;

import cn.shencom.scloud.common.base.snowflake.SnowflakeGenerator;
import cn.shencom.scloud.common.jpa.scpage.ScLink;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.*;
import org.hibernate.annotations.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 小散工程-工程备注表
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Entity
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@DynamicUpdate
@DynamicInsert
@Table(name = "sporadic_project_memo")
@Where(clause = "is_deleted = 0")
@EntityListeners(AuditingEntityListener.class)
public class SporadicProjectMemo implements Serializable {
//===========================数据库字段================================
    /**
     * ID
     */
    @Id
    @Column(name = "id")
    @GenericGenerator(name = "snowflake", strategy = SnowflakeGenerator.TYPE)
    @GeneratedValue(generator = "snowflake")
    private String id;

    /**
     * 工程ID
     */
    @Column(name = "project_id")
    private String projectId;

    /**
     * 备注内容
     */
    @Column(name = "content")
    private String content;

    /**
     * 是否删除:0-否,1-是
     */
    @Column(name = "is_deleted")
    private Integer isDeleted;

    /**
     * 用户id
     */
    @Column(name = "user_id")
    private String userId;

    /**
     * 创建时间
     */
    @Column(name = "created_at")
    @CreatedDate
    private java.util.Date createdAt;

    /**
     * 更新时间
     */
    @Column(name = "updated_at")
    @LastModifiedDate
    private java.util.Date updatedAt;

    /**
     * 删除时间
     */
    @Column(name = "deleted_at")
    private java.util.Date deletedAt;

//=============================表关联==================================



    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", referencedColumnName = "id", insertable = false, updatable = false,
            foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
    @NotFound(action = NotFoundAction.IGNORE)
    @JSONField(serialize = false)
    private SysUsers createdSysUser;



//===========================自定义字段=================================



    /** 系统用户真实姓名 */
    @ScLink(objName = "createdSysUser", filed = "realname")
    @Transient
    private String createdUserName;



}
