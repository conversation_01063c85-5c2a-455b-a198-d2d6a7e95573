package cn.shencom.model;

import cn.shencom.scloud.common.base.snowflake.SnowflakeGenerator;
import cn.shencom.scloud.common.jpa.scpage.ScLink;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.*;
import org.hibernate.annotations.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 小散工程表
 *
 * <AUTHOR>
 * @since 2025-05-29
 */
@Entity
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@DynamicUpdate
@DynamicInsert
@Table(name = "sporadic_project")
@Where(clause = "is_deleted = 0")
@EntityListeners(AuditingEntityListener.class)
public class SporadicProject implements Serializable {
    // ===========================数据库字段================================
    /**
     * 工程ID
     */
    @Id
    @Column(name = "id")
    @GenericGenerator(name = "snowflake", strategy = SnowflakeGenerator.TYPE)
    @GeneratedValue(generator = "snowflake")
    private String id;

    /**
     * 工程名称
     */
    @Column(name = "name")
    private String name;

    /**
     * 工程关联的组织id
     */
    @Column(name = "organization_id")
    private String organizationId;

    /**
     * 工程分类ID
     */
    @Column(name = "cate_pid")
    private String catePid;

    /**
     * 工程类别ID
     */
    @Column(name = "cate_id")
    private String cateId;

    /**
     * 工程金额(元)
     */
    @Column(name = "amount")
    private java.math.BigDecimal amount;

    /**
     * 实际施工面积
     */
    @Column(name = "area")
    private java.math.BigDecimal area;

    /**
     * 工程开始时间
     */
    @Column(name = "start_at")
    private java.util.Date startAt;

    /**
     * 工程结束时间
     */
    @Column(name = "end_at")
    private java.util.Date endAt;

    /**
     * 所在区
     */
    @Column(name = "region_pid")
    private String regionPid;

    /**
     * 所在街道
     */
    @Column(name = "region_id")
    private String regionId;

    /**
     * 所在社区
     */
    @Column(name = "region_cid")
    private String regionCid;

    /**
     * 详细地址
     */
    @Column(name = "address")
    private String address;

    /**
     * 经度
     */
    @Column(name = "lng")
    private BigDecimal lng;

    /**
     * 纬度
     */
    @Column(name = "lat")
    private BigDecimal lat;

    /**
     * 建设单位
     */
    @Column(name = "constructor_name")
    private String constructorName;

    /**
     * 建设单位负责人
     */
    @Column(name = "constructor_charger")
    private String constructorCharger;

    /**
     * 业主电话
     */
    @Column(name = "owner_mobile")
    private String ownerMobile;

    /**
     * 施工单位ID
     */
    @Column(name = "contractor_id")
    private String contractorId;

    /**
     * 施工单位名称
     */
    @Column(name = "contractor_name")
    private String contractorName;

    /**
     * 施工单位负责人
     */
    @Column(name = "contractor_charger")
    private String contractorCharger;

    /**
     * 施工单位负责人电话
     */
    @Column(name = "contractor_charger_mobile")
    private String contractorChargerMobile;

    /**
     * 是否删除:0-否,1-是
     */
    @Column(name = "is_deleted")
    private Integer isDeleted;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 备案编号
     */
    @Column(name = "project_number")
    private String projectNumber;

    /**
     * 是否已接入监管
     * 0-未接入 1-已接入 2-已结束监管
     */
    @Column(name = "monitor_flag")
    private Integer monitorFlag;

    /**
     * 施工状态:0-未开始,1-施工中,2-已结束
     */
    @Column(name = "status")
    private Integer status;

    /**
     * gis_poi
     * 点位id
     */
    @Column(name = "poi_id")
    private String poiId;

    /**
     * 创建时间
     */
    @Column(name = "created_at")
    @CreatedDate
    private java.util.Date createdAt;

    /**
     * 更新时间
     */
    @Column(name = "updated_at")
    @LastModifiedDate
    private java.util.Date updatedAt;

    /**
     * 删除时间
     */
    @Column(name = "deleted_at")
    private java.util.Date deletedAt;

    // =============================表关联==================================
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cate_pid", insertable = false, updatable = false)
    @NotFound(action = NotFoundAction.IGNORE)
    @JSONField(serialize = false)
    private SporadicProjectCategory pCate;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cate_id", insertable = false, updatable = false)
    @NotFound(action = NotFoundAction.IGNORE)
    @JSONField(serialize = false)
    private SporadicProjectCategory cate;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "region_pid", insertable = false, updatable = false)
    @NotFound(action = NotFoundAction.IGNORE)
    @JSONField(serialize = false)
    private ComRegion district;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "region_id", insertable = false, updatable = false)
    @NotFound(action = NotFoundAction.IGNORE)
    @JSONField(serialize = false)
    private ComRegion street;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "region_cid", insertable = false, updatable = false)
    @NotFound(action = NotFoundAction.IGNORE)
    @JSONField(serialize = false)
    private ComRegion village;


    @OneToOne(fetch = FetchType.LAZY, mappedBy = "project")
    @JSONField(serialize = false)
    private MonitorOrder order;

    // ===========================自定义字段=================================
    @Transient
    @ScLink(objName = "pCate", filed = "name")
    private String pCateName;
    @Transient
    @ScLink(objName = "cate", filed = "name")
    private String cateName;
    @Transient
    @ScLink(objName = "district", filed = "title")
    private String districtName;
    @Transient
    @ScLink(objName = "street", filed = "title")
    private String streetName;
    @Transient
    @ScLink(objName = "village", filed = "title")
    private String villageName;


    @Transient
    @ScLink(objName = "order", filed = "id")
    private String orderId;

    @Transient
    @ScLink(objName = "order", filed = "flow")
    private Integer flow;

    @Transient
    @ScLink(objName = "order", filed = "flowId")
    private String flowId;



}
