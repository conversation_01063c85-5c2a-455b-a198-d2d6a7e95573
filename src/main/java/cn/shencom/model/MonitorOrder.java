package cn.shencom.model;

import cn.shencom.scloud.common.base.snowflake.SnowflakeGenerator;
import cn.shencom.scloud.common.jpa.scpage.ScLink;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.*;
import org.hibernate.annotations.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 小散工程-监管工单
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@Entity
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@DynamicUpdate
@DynamicInsert
@Table(name = "monitor_order")
@Where(clause = "is_deleted = 0")
@EntityListeners(AuditingEntityListener.class)
public class MonitorOrder implements Serializable {
//===========================数据库字段================================
    /**
     * id
     */
    @Id
    @Column(name = "id")
    @GenericGenerator(name = "snowflake", strategy = SnowflakeGenerator.TYPE)
    @GeneratedValue(generator = "snowflake")
    private String id;

    /**
     * 项目id
     */
    @Column(name = "project_id")
    private String projectId;

    /**
     * 组织id
     */
    @Column(name = "organization_id")
    private String organizationId;

    /**
     * 区id
     */
    @Column(name = "region_pid")
    private String regionPid;

    /**
     * 街道id
     */
    @Column(name = "region_id")
    private String regionId;

    /**
     * 社区id
     */
    @Column(name = "region_cid")
    private String regionCid;

    /**
     * 当前流程, 0-工程创建，1-安装预约，2-现场勘察，3-上门安装，4-接入监管， 5-施工完成，6-回收预约，7-上门回收， 9-监管结束
     */
    @Column(name = "flow")
    private Integer flow;


    @Column(name = "flow_id")
    private String flowId;

    /**
     * 自定义的排序字段
     * 0-预约安装
     * 1-预约回收
     * 2-进行中的工程
     * 3-已结束的工程
     */
    @Column(name = "sort")
    private Integer sort;



    /**
     * 删除标记 1-已删除 0-默认
     */
    @Column(name = "is_deleted")
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @Column(name = "created_at")
    @CreatedDate
    private java.util.Date createdAt;

    /**
     * 更新时间
     */
    @Column(name = "updated_at")
    @LastModifiedDate
    private java.util.Date updatedAt;

    /**
     * 创建人
     */
    @Column(name = "created_user")
    private String createdUser;

    /**
     * 修改人
     */
    @Column(name = "updated_user")
    private String updatedUser;

//=============================表关联==================================

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "project_id", insertable = false, updatable = false)
    @NotFound(action = NotFoundAction.IGNORE)
    @JSONField(serialize = false)
    private SporadicProject project;

//===========================自定义字段=================================


    @Transient
    @ScLink(objName = "project", filed = "name")
    private String projectName;



    @Transient
    @ScLink(objName = "project", filed = "address")
    private String projectAddress;


    @Transient
    @ScLink(objName = "project", filed = "poiId")
    private String projectPoiId;

    @Transient
    @ScLink(objName = "project", filed = "projectNumber")
    private String projectNumber;
}
