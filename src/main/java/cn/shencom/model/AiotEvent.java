package cn.shencom.model;

import cn.shencom.scloud.common.base.snowflake.SnowflakeGenerator;
import cn.shencom.scloud.common.jpa.scpage.ScLink;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.*;
import org.hibernate.annotations.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.List;

/**
 * 监控事件
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Entity
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@DynamicUpdate
@DynamicInsert
@Table(name = "aiot_event")
@Where(clause = "is_deleted = 0")
@EntityListeners(AuditingEntityListener.class)
public class AiotEvent implements Serializable {
//===========================数据库字段================================
    /**
     * 
     */
    @Id
    @Column(name = "id")
    @GenericGenerator(name = "snowflake", strategy = SnowflakeGenerator.TYPE)
    @GeneratedValue(generator = "snowflake")
    private String id;

    /**
     * 区ID
     */
    @Column(name = "region_pid")
    private String regionPid;

    /**
     * 街道ID
     */
    @Column(name = "region_id")
    private String regionId;

    /**
     * 社区ID
     */
    @Column(name = "region_cid")
    private String regionCid;

    /**
     * 项目ID
     */
    @Column(name = "project_id")
    private String projectId;

    /**
     * 事件编号
     */
    @Column(name = "event_no")
    private String eventNo;

    /**
     * 设备编码(国标视频认证编码ID)
     */
    @Column(name = "device_code")
    private String deviceCode;

    /**
     * 通道编码
     */
    @Column(name = "channel_code")
    private String channelCode;

    /**
     * 摄像头编号
     */
    @Column(name = "monitor_no")
    private String monitorNo;

    /**
     * 场景编码
     */
    @Column(name = "scene_code")
    private String sceneCode;

    /**
     * 事件图片
     */
    @Column(name = "pics")
    private String pics;

    /**
     * 违规框
     */
    @Column(name = "violation_box")
    private String violationBox;

    /**
     * 事件时间
     */
    @Column(name = "event_at")
    private java.util.Date eventAt;

    /**
     * 是否删除:0-否,1-是
     */
    @Column(name = "is_deleted")
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @Column(name = "created_at")
    @CreatedDate
    private java.util.Date createdAt;

    /**
     * 更新时间
     */
    @Column(name = "updated_at")
    @LastModifiedDate
    private java.util.Date updatedAt;

    /**
     * 删除时间
     */
    @Column(name = "deleted_at")
    private java.util.Date deletedAt;

//=============================表关联==================================

//    @OneToOne(fetch = FetchType.LAZY)
//    @JoinColumn(name = "scene_code",referencedColumnName="code",insertable = false, updatable = false)
//    @NotFound(action = NotFoundAction.IGNORE)
//    @JSONField(serialize = false,deserialize = false)
//    private AimFirmSceneManagement scene;

//    @ManyToMany(targetEntity = AiotCategory.class, fetch = FetchType.EAGER)
//    @JoinTable(
//            name = "aiot_scene_category",
//            joinColumns = @JoinColumn(name = "scene_code", referencedColumnName = "scene_code"),
//            inverseJoinColumns = @JoinColumn(name = "cate_code", referencedColumnName = "code")
//    )
//    @NotFound(action = NotFoundAction.IGNORE)
//    @JSONField(serialize = false,deserialize = false)
//    private List<AiotCategory> sceneCategories;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "region_pid", insertable = false, updatable = false)
    @NotFound(action = NotFoundAction.IGNORE)
    @JSONField(serialize = false,deserialize = false)
    private ComRegion district;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "region_id", insertable = false, updatable = false)
    @NotFound(action = NotFoundAction.IGNORE)
    @JSONField(serialize = false,deserialize = false)
    private ComRegion street;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "region_cid", insertable = false, updatable = false)
    @NotFound(action = NotFoundAction.IGNORE)
    @JSONField(serialize = false,deserialize = false)
    private ComRegion village;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "project_id", insertable = false, updatable = false)
    @NotFound(action = NotFoundAction.IGNORE)
    @JSONField(serialize = false, deserialize = false)
    private SporadicProject project;

    //===========================自定义字段=================================

    @Transient
    @ScLink(objName = "district", filed = "title")
    private String districtName;

    @Transient
    @ScLink(objName = "street", filed = "title")
    private String streetName;

    @Transient
    @ScLink(objName = "village", filed = "title")
    private String villageName;

    @Transient
    @ScLink(objName = "project", filed = "name")
    private String projectName;

    @Transient
    @ScLink(objName = "project", filed = "address")
    private String projectAddress;

    @Transient
    @ScLink(objName = "project.cate", filed = "name")
    private String projectCateName;

//    @Transient
//    @ScLink(objName = "scene", filed = "name")
//    private String sceneName;

}
