package cn.shencom.model.dto.query;

import cn.shencom.scloud.common.jpa.util.query.ScBaseBean;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * 监控事件类型DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.EventOrderType
 * @since 2023-11-22
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
public class EventOrderTypeQueryDTO extends ScBaseBean implements Serializable {
    /**
     * 1-监控事件
     * 2-第三方监控事件
     */
    private Integer tag;
}
