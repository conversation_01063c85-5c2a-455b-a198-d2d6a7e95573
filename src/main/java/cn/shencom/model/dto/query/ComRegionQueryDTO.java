package cn.shencom.model.dto.query;

import cn.shencom.scloud.common.jpa.util.query.ScBaseBean;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * 区域表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.ComRegion
 * @since 2025-04-19
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
public class ComRegionQueryDTO extends ScBaseBean implements Serializable {
    /**
     * 根节点id
     * 默认为2 返回深圳市的区数据 不包括深圳市
     * 3  光明区、79 福田区、80 罗湖区、81 南山区、82 盐田区
     * 83 宝安区、84 龙华区、85 龙岗区、86 坪山区、87 大鹏新区
     */
    private String rootId;

    /**
     * 深度
     * 不传root 或 root 为 0 时
     * deep 为 0  不返回任何数据
     * deep 为 1  返回当前根节点下一级的数据 以此类推
     * root为 1 时
     * deep 为 0  只返回当前根节点数据
     * deep 为 1  返回当前根节点下一级的数据 以此类推
     */
    private Integer deep;

    /**
     * 是否携带根节点：0 不携带、1 携带
     * 默认为0 不携带
     */
    private Integer root;

    private String type;



    private List<String> regionPids;

    private List<String> regionIds;

    private List<String> regionCids;
}
