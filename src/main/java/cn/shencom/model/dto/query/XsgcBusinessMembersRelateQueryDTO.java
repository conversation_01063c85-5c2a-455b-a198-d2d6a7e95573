package cn.shencom.model.dto.query;

import cn.shencom.scloud.common.jpa.util.query.ScBaseBean;
import lombok.*;

import java.io.Serializable;

/**
 * 小散工程-业务人员客户关联表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.XsgcBusinessMembersRelate
 * @since 2025-06-25
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
public class XsgcBusinessMembersRelateQueryDTO extends ScBaseBean implements Serializable {

    private String realname;

    private String phone;

    private String organizationId;

    /**
     * 职位
     * 1	技术人员
     * 2	商务人员
     * 3	销售人员
     * 4	安装人员
     */
    private String type;

}
