package cn.shencom.model.dto.query;

import cn.shencom.scloud.common.jpa.util.query.ScBaseBean;
import lombok.*;

import java.io.Serializable;

/**
 * 小散工程-监管工单流程-安装预约详情DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.MonitorInstallReservation
 * @since 2025-07-04
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
public class MonitorInstallReservationQueryDTO extends ScBaseBean implements Serializable {

}
