package cn.shencom.model.dto.query;

import cn.shencom.scloud.common.jpa.util.query.ScBaseBean;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * 事件工单表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.EventOrder
 * @since 2021-06-29
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
public class EventOrderQueryDTO extends ScBaseBean implements Serializable {

    private Integer queryType;

    /**
     * 投放点id
     */
    private String projectId;

    /**
     * 场景id（只针对于人工智能管理下的事件列表必传）
     */
    private String sceneId;

    /**
     * 辖区id
     */
    private String regionPid;

    /**
     * 街道id
     */
    private String regionId;

    /**
     * 社区id
     */
    private String regionCid;

    /**
     * 事件编号
     */
    private String eventCode;

    /**
     * 事件描述
     */
    private String eventDesc;

    /**
     * 监控设备号
     */
    private String cameraNo;

    /**
     * 来源
     */
    private Integer origin;

    /**
     * 状态，0-待处理，1-已处理，2-超时自动结束， 3-待复核， 4-已复核
     */
    private Integer status;

    /**
     * 工单状态 0-进行中，1-已完结
     */
    private String finishStatus;

    /**
     * 报警时间
     */
    private String createdAt;

    /**
     * 报警时间
     */
    private String eventedAt;

    /**
     * 开始时间
     */
    private String startDate;

    /**
     * 结束时间
     */
    private String endDate;

    /**
     * 该场景关联的违规类型
     */
    private List<String> typeCodeList;


    /**
     * 该场景关联的违规类型
     */

    private String typeCode;

    private String disposeDesc;

    /**
     * 事件id列表
     */
    private List<String> ids;

    private String eventSource;

    private Integer type;
}
