package cn.shencom.model.dto.query;

import cn.shencom.scloud.common.jpa.util.query.ScBaseBean;
import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * 小散工程表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.SporadicProject
 * @since 2025-05-29
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
public class SporadicProjectQueryDTO extends ScBaseBean implements Serializable {


    //0-未施工  1-施工中  2-已完工
    private Integer status;


    private String regionPid;
    private String regionId;
    private String regionCid;
    private String name;

    private List<String> ids;


    private Integer flow;
}
