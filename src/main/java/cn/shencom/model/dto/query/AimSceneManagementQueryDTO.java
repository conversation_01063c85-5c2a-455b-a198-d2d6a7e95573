package cn.shencom.model.dto.query;

import cn.shencom.scloud.common.jpa.util.query.ScBaseBean;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * AI场景管理DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.AimSceneManagement
 * @since 2022-07-26
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
public class AimSceneManagementQueryDTO extends ScBaseBean implements Serializable {
    /**
     * 关联页面标识，查询已关联-1，查询未关联-0 (关联操作必传，其他不传)
     */
    private Integer isLinked;

    /**
     * 厂商id(关联操作必传，其他不传)
     */
    private String firmId;

    /**
     * 场景名称
     */
    private String name;

    /**
     * 场景编号
     */
    private String code;

    /**
     * 场景类别id
     */
    private String sceneCategoryId;

    /**
     * 场景类别
     */
    private String sceneCategory;

    /**
     * 标签（逗号分隔）
     */
    private String tags;

    /**
     * 管理端简易查询
     */
    private String simpleUsed;
}
