package cn.shencom.model.dto.query;

import cn.shencom.scloud.common.jpa.util.query.ScBaseBean;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * 场景类别管理表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.AimSceneCategoryManagement
 * @since 2022-07-26
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
public class AimSceneCategoryManagementQueryDTO extends ScBaseBean implements Serializable {

}
