package cn.shencom.model.dto.query;

import cn.shencom.scloud.common.jpa.util.query.ScBaseBean;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * 人工智能管理厂商表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.AimFirm
 * @since 2022-08-02
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
public class AimFirmQueryDTO extends ScBaseBean implements Serializable {

}
