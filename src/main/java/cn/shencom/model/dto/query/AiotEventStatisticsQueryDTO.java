package cn.shencom.model.dto.query;

import cn.shencom.scloud.common.jpa.util.query.ScBaseBean;
import lombok.*;

import java.io.Serializable;

/**
 * 事件统计分类配置表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.AiotEventStatistics
 * @since 2025-07-14
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
public class AiotEventStatisticsQueryDTO extends ScBaseBean implements Serializable {

    /**
     * 场景编码
     */
    private String sceneCode;

    /**
     * 时间开始日期
     */
    private java.util.Date startDate;

    /**
     * 时间结束日期
     */
    private java.util.Date endDate;

    /**
     * 是否同步
     */
    private Boolean isSync;
}
