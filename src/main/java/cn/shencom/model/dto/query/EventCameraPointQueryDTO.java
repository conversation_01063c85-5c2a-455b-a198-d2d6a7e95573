package cn.shencom.model.dto.query;

import cn.shencom.scloud.common.jpa.util.query.ScBaseBean;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 摄像头信息表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.EventCameraPoint
 * @since 2025-05-16
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
public class EventCameraPointQueryDTO extends ScBaseBean implements Serializable {

    private String id;

    private String typeId;

    /**
     * 设备序列号
     */
    private String serialNo;

    /**
     * 通道号
     */
    private String channelNo;

    private Date startTime;

    private Date endTime;

    private String regionPid;

    private String regionId;

    private String regionCid;

    private String areaId;

    /**
     * 国标sip编码
     */
    private String sipUserId;

    private Integer type;

    private List<String> projectId;

    private String projectName;
}
