package cn.shencom.model.dto.query;

import cn.shencom.scloud.common.jpa.util.query.ScBaseBean;
import lombok.*;

import java.io.Serializable;

/**
 * 小散工程-客户服务开通记录DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.XsgcCustomerServiceRecord
 * @since 2025-06-25
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
public class XsgcCustomerServiceRecordQueryDTO extends ScBaseBean implements Serializable {

    private String customerId;

}
