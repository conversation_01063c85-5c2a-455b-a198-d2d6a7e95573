package cn.shencom.model.dto.query;

import cn.shencom.scloud.common.jpa.util.query.ScBaseBean;
import lombok.*;

import java.io.Serializable;

/**
 * 小散工程-组织团队成员关系DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.FnRmsv3MembersTypeRelate
 * @since 2025-06-25
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
public class FnRmsv3MembersTypeRelateQueryDTO extends ScBaseBean implements Serializable {

    private String typeId;


    private String regionPid;

    private String regionId;

    private String regionCid;

}
