package cn.shencom.model.dto.query;

import cn.shencom.scloud.common.jpa.util.query.ScBaseBean;
import lombok.*;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * 小散工程表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.SporadicProject
 * @since 2025-05-29
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
public class SporadicProjectMobileQueryDTO extends ScBaseBean implements Serializable {

    // 0-未施工 1-施工中 2-已完工
    private Integer status;

    // 接入监管状态
    private Integer monitorFlag;

    private Set<String> regionPidSet;
    private Set<String> regionIdSet;
    private Set<String> regionCidSet;
    private Set<String> projectIdSet;

    /**
     * 由于前端需要根据区域筛选，所以需要提供字段
     */
    private String regionPid;
    private String regionId;
    private String regionCid;

    private String keyword;

    private String projectId;

    private String organizationId;

    private Integer flow;

    private String flowId;


    private List<Integer> flowList;

}
