package cn.shencom.model.dto.query;

import cn.shencom.scloud.common.jpa.util.query.ScBaseBean;
import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * 系统角色，权限，组织关联表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.SysRoleUserOrganization
 * @since 2025-06-25
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
public class SysRoleUserOrganizationQueryDTO extends ScBaseBean implements Serializable {



    private String userId;

    /**
     * 0-管理端   1-移动端
     * 默认管理端
     */
    private int platform = 0 ;

    private String organizationId;

    private List<String>  roleNameList;


}
