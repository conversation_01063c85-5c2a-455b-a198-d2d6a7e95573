package cn.shencom.model.dto.query;

import cn.shencom.scloud.common.jpa.util.query.ScBaseBean;
import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * 监控设备表移动端查询DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.EventCameraPointDevice
 * @since 2025-05-16
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
public class EventCameraPointDeviceMobileQueryDTO extends ScBaseBean implements Serializable {
    /**
     * 区域相关字段
     */
    private String regionPid;
    private String regionId;
    private String regionCid;

    /**
     * 关键字
     */
    private String keyword;

    /**
     * 项目ID集合
     */
    private String projectId;
    /**
     * 项目ID集合
     */
    private List<String> projectIds;
}