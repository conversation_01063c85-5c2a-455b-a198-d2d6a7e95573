package cn.shencom.model.dto.query;

import cn.shencom.scloud.common.jpa.util.query.ScBaseBean;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * 场景类别DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.AiotSceneMgr
 * @since 2024-08-03
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
public class AiotSceneMgrQueryDTO extends ScBaseBean implements Serializable {

    /**
     * 父级ID
     */
    private String pId;

}
