package cn.shencom.model.dto.query;

import lombok.*;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Set;

import cn.shencom.scloud.common.jpa.util.query.ScBaseBean;

/**
 * 监控事件DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.AiotEvent
 * @see cn.shencom.model.dto.query.AiotEventQueryDTO
 * @since 2025-06-10
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
public class AiotEventMobileQueryDTO extends ScBaseBean implements Serializable {
    private String keyword;

    private String regionPid;
    private String regionId;
    private String regionCid;

    private String sceneCode;

    private LocalDate startTime;
    private LocalDate endTime;
    private String projectId;

    private String eventAtSort; // desc, asc

    /** 不用于前端传值 */
    private Set<String> regionPidSet;
    private Set<String> regionIdSet;
    private Set<String> regionCidSet;
    private Set<String> projectIdSet;
    private String organizationId;
}
