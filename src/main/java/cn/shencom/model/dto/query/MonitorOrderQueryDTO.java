package cn.shencom.model.dto.query;

import cn.shencom.scloud.common.jpa.util.query.ScBaseBean;
import lombok.*;

import java.io.Serializable;

/**
 * 小散工程-监管工单DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.MonitorOrder
 * @since 2025-07-04
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
public class MonitorOrderQueryDTO extends ScBaseBean implements Serializable {


    /**
     *  安装状态
     *  0-全部，1-未勘察，2-未安装 ，3-已完成
     */
    private Integer installType;


    /**
     * 回收状态 0-全部，1-待回收，2-已完成
     */
    private Integer recycleType;


    /**
     * 安装工人/商务人员-1 ，  建设方（业主）/施工负责人-2
     */
    private Integer queryType;

}
