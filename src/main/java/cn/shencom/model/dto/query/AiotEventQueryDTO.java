package cn.shencom.model.dto.query;

import cn.shencom.scloud.common.jpa.util.query.ScBaseBean;
import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * 监控事件DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.AiotEvent
 * @since 2025-06-10
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
public class AiotEventQueryDTO extends ScBaseBean implements Serializable {
    private String sceneId;

    /**
     * 场景类别id
     */
    private String sceneCategoryId;

    /**
     * 场景编码
     */
    private String sceneCode;

    /**
     * 事件开始日期
     */
    private java.util.Date startDate;

    /**
     * 事件结束日期
     */
    private java.util.Date endDate;

    /**
     * 项目id集合
     */
    private List<String> projectId;
}
