package cn.shencom.model.dto.query;

import cn.shencom.scloud.common.jpa.util.query.ScBaseBean;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * 厂商场景管理DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.AimFirmSceneManagement
 * @since 2022-07-27
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
public class AimFirmSceneManagementQueryDTO extends ScBaseBean implements Serializable {
    /**
     * 关联页面标识，查询已关联-1，查询未关联-0 (关联操作必传，其他不传)
     */
    private Integer isLinked;

    /**
     * ai场景id(关联操作必传，其他不传)
     */
    private String sceneId;

    /**
     * 厂商
     */
    private String firmId;

    /**
     * 厂商名称
     */
    private String firmName;

    /**
     * 厂商场景编号
     */
    private String code;

    /**
     * 厂商场景名称
     */
    private String sceneName;

    /**
     * 管理端简易查询
     */
    private String simpleUsed;

}
