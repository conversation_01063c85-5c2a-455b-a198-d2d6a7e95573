package cn.shencom.model.dto.resp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import cn.shencom.model.FnRmsv3MembersTypeRelateBinding;
import cn.shencom.model.dto.SimpleRegionDTO;
import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * 小散工程-组织团队成员关系DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.FnRmsv3MembersTypeRelate
 * @since 2025-06-25
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@ExcelTarget("FnRmsv3MembersTypeRelateRespDTO")
public class FnRmsv3MembersTypeRelateRespDTO implements Serializable {

    /**
     * id
     */
    @Excel(name = "id", width = 25)
    private String id;

    /**
     * 成员id
     */
    @Excel(name = "成员id", width = 25)
    private String memberId;


    /**
     * 0-市级 1-区级 2-街道级 3-社区级
     */
    private Integer level;

    /**
     * 类型id (字典表：fn_rmsv3_members_type)
     */
    @Excel(name = "类型id (字典表：fn_rmsv3_members_type)", width = 25)
    private String typeId;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 25)
    private java.util.Date createdAt;

    /**
     * 修改时间
     */
    @Excel(name = "修改时间", width = 25)
    private java.util.Date updatedAt;

    /**
     * 是否有效 0-无效 1-有效
     */
    @Excel(name = "是否有效 0-无效 1-有效", width = 25)
    private Integer active;

    /**
     * 照片id
     */
    @Excel(name = "照片id", width = 25)
    private String pic;


    /**
     * 姓名
     */
    private String realname;


    /**
     * 电话
     */
    private String mobile;


    /**
     * 区域信息
     */
    private String regionNames;


    List<FnRmsv3MembersTypeRelateBinding> regionIds;

}
