package cn.shencom.model.dto.resp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import lombok.*;

import java.io.Serializable;

/**
 * 施工单位表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.ConstructionUnit
 * @since 2025-07-14
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@ExcelTarget("ConstructionUnitRespDTO")
public class ConstructionUnitRespDTO implements Serializable {

    /**
     * 主键ID
     */
    @Excel(name = "主键ID", width = 25)
    private String id;

    /**
     * 施工单位名称
     */
    @Excel(name = "施工单位名称", width = 25)
    private String name;

    /**
     * 管理员用户ID，关联移动端认证的管理员
     */
    @Excel(name = "管理员用户ID，关联移动端认证的管理员", width = 25)
    private String adminUserId;

    /**
     * 管理员姓名
     */
    @Excel(name = "管理员姓名", width = 25)
    private String adminName;

    /**
     * 管理员联系方式
     */
    @Excel(name = "管理员联系方式", width = 25)
    private String adminMobile;

    /**
     * 状态：0-无效，1-有效
     */
    @Excel(name = "状态：0-无效，1-有效", width = 25)
    private Integer status;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 25)
    private java.util.Date createdAt;

    /**
     * 更新时间
     */
    @Excel(name = "更新时间", width = 25)
    private java.util.Date updatedAt;

    /**
     * 创建人
     */
    @Excel(name = "创建人", width = 25)
    private String createdUser;

    /**
     * 更新人
     */
    @Excel(name = "更新人", width = 25)
    private String updatedUser;

}
