package cn.shencom.model.dto.resp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import lombok.*;

import java.io.Serializable;

/**
 * 小散工程-监管工单流程-回收预约详情DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.MonitorRecycleReservation
 * @since 2025-07-04
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@ExcelTarget("MonitorRecycleReservationRespDTO")
public class MonitorRecycleReservationRespDTO implements Serializable {

    /**
     * id
     */
    @Excel(name = "id", width = 25)
    private String id;

    /**
     * 工单id，关联monitor_order
     */
    @Excel(name = "工单id，关联monitor_order", width = 25)
    private String orderId;

    /**
     * 联系人电话
     */
    @Excel(name = "联系人电话", width = 25)
    private String contactMobile;

    /**
     * 联系人姓名
     */
    @Excel(name = "联系人姓名", width = 25)
    private String contactName;

    /**
     * 预约时间
     */
    @Excel(name = "预约时间", width = 25)
    private java.util.Date reservationTime;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 25)
    private java.util.Date createdAt;

    /**
     * 更新时间
     */
    @Excel(name = "更新时间", width = 25)
    private java.util.Date updatedAt;

    /**
     * 创建人
     */
    @Excel(name = "创建人", width = 25)
    private String createdUser;

    /**
     * 修改人
     */
    @Excel(name = "修改人", width = 25)
    private String updatedUser;

}
