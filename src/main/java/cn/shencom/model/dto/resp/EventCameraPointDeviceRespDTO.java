package cn.shencom.model.dto.resp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import cn.shencom.model.EventCameraPoint;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * 摄像头表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.EventCameraPointDevice
 * @since 2025-05-16
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@ExcelTarget("EventCameraPointDeviceRespDTO")
public class EventCameraPointDeviceRespDTO implements Serializable {

    /**
     * 主键id
     */
    @Excel(name = "主键id", width = 25)
    private String id;

    /**
     * 序列号
     */
    @Excel(name = "序列号", width = 25)
    private String serialNo;

    /**
     * 监控设备型号
     */
    @Excel(name = "监控设备型号", width = 25)
    private String modelNo;


    /**
     * 国标视频编码
     */
    @Excel(name = "国标视频编码", width = 25)
    private String sipUserId;

    /**
     * 类型 event_camera_point_type
     */
    @Excel(name = "类型 event_camera_point_type", width = 25)
    private Integer type;

    /**
     * 小散项目id
     */
    @Excel(name = "小散项目id", width = 25)
    private String projectId;

    /**
     * 是否语音播报，0-不是，1-是
     */
    @Excel(name = "是否语音播报，0-不是，1-是", width = 25)
    private Integer voiceBroadcast;

    /**
     * 感知类型
     */
    @Excel(name = "感知类型", width = 25)
    private String perceptionTypes;

    /**
     * 是否健康 0-异常 1-健康
     */
    @Excel(name = "是否健康 0-异常 1-健康", width = 25)
    private Integer isHealthy;

    /**
     * 是否智能，0-不是，1-是
     */
    @Excel(name = "是否智能，0-不是，1-是", width = 25)
    private Integer smart;

    /**
     * 备注
     */
    @Excel(name = "备注", width = 25)
    private String memo;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 25,format = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private java.util.Date createdAt;

    /**
     * 更新时间
     */
    @Excel(name = "更新时间", width = 25, format = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private java.util.Date updatedAt;
    /**
     * 项目名称
     */
    @Excel(name = "项目名称", width = 25)
    private String projectName;
    /**
     * 项目分类名称
     */
    @Excel(name = "项目分类名称", width = 25)
    private String pCateName;
    /**
     * 项目类别名称
     */
    @Excel(name = "项目类别名称", width = 25)
    private String cateName;

    /**
     * 经营区 区id
     */
    private String regionPid;

    /**
     * 经营区 街道id
     */
    private String regionId;

    /**
     * 经营区 社区id
     */
    private String regionCid;

    /**
     * 经营区 区名称
     */
    @Excel(name = "所属辖区", width = 25)
    private String districtName;

    /**
     * 经营区 街道名称
     */
    @Excel(name = "所属街道", width = 25)
    private String streetName;

    /**
     * 经营区 社区名称
     */
    @Excel(name = "所属社区", width = 25)
    private String villageName;

    /**
     * 摄像头列表
     */
    private List<EventCameraPoint> eventCameraPointList;
}
