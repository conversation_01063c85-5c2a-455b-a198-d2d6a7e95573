package cn.shencom.model.dto.resp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import lombok.*;

import java.io.Serializable;

/**
 * 小散工程分类DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.SporadicProjectCategory
 * @since 2025-05-29
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@ExcelTarget("SporadicProjectCategoryRespDTO")
public class SporadicProjectCategoryRespDTO implements Serializable {

    /**
     * 
     */
    @Excel(name = "", width = 25)
    private String id;

    /**
     * 分类名称
     */
    @Excel(name = "分类名称", width = 25)
    private String name;

    /**
     * 父分类ID
     */
    @Excel(name = "父分类ID", width = 25)
    private String pId;

    /**
     * 状态:0-关闭,1-开启
     */
    @Excel(name = "状态:0-关闭,1-开启", width = 25)
    private Integer active;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 25)
    private java.util.Date createdAt;

    /**
     * 更新时间
     */
    @Excel(name = "更新时间", width = 25)
    private java.util.Date updatedAt;

}
