package cn.shencom.model.dto.resp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import lombok.*;

import java.io.Serializable;

/**
 * 小散工程-工程人员DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.EngineeringMembers
 * @since 2025-07-03
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@ExcelTarget("EngineeringMembersRespDTO")
public class EngineeringMembersRespDTO implements Serializable {

    /**
     * ID
     */
    @Excel(name = "ID", width = 25)
    private String id;

    /**
     * 用户id
     */
    @Excel(name = "用户id", width = 25)
    private String userId;

    /**
     * 成员姓名
     */
    @Excel(name = "成员姓名", width = 25)
    private String realname;

    /**
     * 手机号码
     */
    @Excel(name = "手机号码", width = 25)
    private String mobile;

    /**
     * 职位类型
     */
    @Excel(name = "职位类型", width = 25)
    private Integer type;

    /**
     * 身份证号
     */
    @Excel(name = "身份证号", width = 25)
    private String idCard;

    /**
     * 工种名称
     */
    @Excel(name = "工种名称", width = 25)
    private String workTypeName;

    /**
     * 证书编号
     */
    @Excel(name = "证书编号", width = 25)
    private String certificateNumber;

    /**
     * 证书有效日期-开始时间
     */
    @Excel(name = "证书有效日期-开始时间", width = 25)
    private java.util.Date certificateStartDate;

    /**
     * 证书有效日期-结束时间
     */
    @Excel(name = "证书有效日期-结束时间", width = 25)
    private java.util.Date certificateEndDate;

    /**
     * 证书图片
     */
    @Excel(name = "证书图片", width = 25)
    private String certificatePic;

    /**
     * 描述
     */
    @Excel(name = "描述", width = 25)
    private String desc;

    /**
     * 有效状态，0-无效，1-有效
     */
    @Excel(name = "有效状态，0-无效，1-有效", width = 25)
    private Integer status;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 25)
    private java.util.Date createdAt;

    /**
     * 最后更新时间
     */
    @Excel(name = "最后更新时间", width = 25)
    private java.util.Date updatedAt;

}
