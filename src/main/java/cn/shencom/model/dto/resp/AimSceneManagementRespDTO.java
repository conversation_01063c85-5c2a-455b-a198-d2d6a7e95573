package cn.shencom.model.dto.resp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import lombok.*;

import java.io.Serializable;

/**
 * AI场景管理DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.AimSceneManagement
 * @since 2022-07-26
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@ExcelTarget("AimSceneManagementRespDTO")
public class AimSceneManagementRespDTO implements Serializable {

    /**
     * 
     */
    @Excel(name = "", width = 25)
    private String id;

    /**
     * 场景名称
     */
    @Excel(name = "场景名称", width = 25)
    private String name;

    /**
     * 场景编号
     */
    @Excel(name = "场景编号", width = 25)
    private String code;

    /**
     * 场景类别Id
     */
    private String sceneCategoryId;

    /**
     *
     */
    @Excel(name = "场景类别", width = 25)
    private String sceneCategory;

    /**
     * 场景类型：0-基本 1-复合
     */
    @Excel(name = "场景类型", replace = {"基本_0","复合_1"}, width = 25)
    private Integer type;

    /**
     * 处置模式：1-现场宣导 2-事件处置（逗号分隔）
     */
    private String mode;

    /**
     * 处置模式
     */
    @Excel(name = "处置模式", width = 25)
    private String modeMsg;

    /**
     * 标签（逗号分隔）
     */
    @Excel(name = "标签（逗号分隔）", width = 25)
    private String tags;

    /**
     * 图片（逗号分隔）
     */
    @Excel(name = "图片（逗号分隔）", width = 25)
    private String pics;

    /**
     * 场景说明
     */
    @Excel(name = "场景说明", width = 25)
    private String directions;

    /**
     * 厂商场景关联数
     */
    @Excel(name = "厂商场景关联数", width = 25)
    private Integer firmNum;

    /**
     * 厂商场景
     */
    @Excel(name = "厂商场景", width = 25)
    private String firmScene;

    /**
     * 产生事件数
     */
    @Excel(name = "产生事件数", width = 25)
    private Long eventNum;

    /**
     * 已处理事件数
     */
    @Excel(name = "已处理事件数", width = 25)
    private Long handledEventNum;

    /**
     * 待处理事件数
     */
    @Excel(name = "待处理事件数", width = 25)
    private Long pendingEventNum;

    /**
     * 超时事件数
     */
    @Excel(name = "超时事件数", width = 25)
    private Long timeOutEventNum;

    /**
     * 识别准确数
     */
    @Excel(name = "识别准确数", width = 25)
    private Long eventPreciseNum;

    /**
     * 识别不准确数
     */
    @Excel(name = "识别不准确数", width = 25)
    private Long eventInaccurateNum;

    /**
     * 事件识别准确率
     */
    @Excel(name = "事件识别准确率", width = 25)
    private String eventAccuracy;

    /**
     * 识别有误数
     */
    @Excel(name = "识别有误数", width = 25)
    private Long eventWrongNum;

    /**
     * 备注
     */
    @Excel(name = "备注", width = 25)
    private String remark;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 25, format = "yyyy-MM-dd HH:mm:ss")
    private java.util.Date createdAt;

    /**
     * 更新时间
     */
    private java.util.Date updatedAt;

}
