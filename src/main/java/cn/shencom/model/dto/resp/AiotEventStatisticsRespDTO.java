package cn.shencom.model.dto.resp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import lombok.*;

import java.io.Serializable;

/**
 * 事件统计分类配置表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.AiotEventStatistics
 * @since 2025-07-14
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@ExcelTarget("AiotEventStatisticsRespDTO")
public class AiotEventStatisticsRespDTO implements Serializable {

    /**
     * 
     */
    @Excel(name = "", width = 25)
    private String id;

    /**
     * 场景编码
     */
    @Excel(name = "场景编码", width = 25)
    private String sceneCode;

    /**
     * 事件次数
     */
    @Excel(name = "事件次数", width = 25)
    private Integer eventCount;

    /**
     * 事件日期
     */
    @Excel(name = "事件日期", width = 25)
    private java.util.Date eventDate;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 25)
    private java.util.Date createdAt;

    /**
     * 更新时间
     */
    @Excel(name = "更新时间", width = 25)
    private java.util.Date updatedAt;

    /**
     * 违规类型
     */
    @Excel(name = "违规类型", width = 25)
    private String typeName;

    /**
     * 违规问题
     */
    @Excel(name = "违规问题", width = 25)
    private String issueName;

}
