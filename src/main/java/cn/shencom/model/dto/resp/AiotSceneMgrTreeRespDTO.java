package cn.shencom.model.dto.resp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.shencom.scloud.common.base.dto.BaseTree;
import cn.shencom.scloud.common.jpa.scpage.ScLink;
import lombok.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

import javax.persistence.Transient;
import java.io.Serializable;

/**
 * 场景类别
 *
 * <AUTHOR>
 * @since 2024-08-03
 */
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Data
public class AiotSceneMgrTreeRespDTO extends BaseTree<AiotSceneMgrTreeRespDTO> implements Serializable {
//===========================数据库字段================================
    /**
     * 主键
     */
    private String id;


    /**
     * 父节点，用于接收数据库的p_id
     */
    private String pId;

    /**
     * 场景类别编号
     */
    private String sceneCode;

    /**
     * 场景类别名称
     */
    private String sceneName;

    /**
     * 场景类别说明
     */
    private String sceneIllustrate;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建人姓名
     */
    private String createdByName;

    /**
     * 修改人
     */
    private String updatedBy;

    /**
     * 修改人姓名
     */
    private String updatedByName;

    /**
     * 创建时间
     */
    @CreatedDate
    private java.util.Date createdAt;

    /**
     * 更新时间
     */
    @LastModifiedDate
    private java.util.Date updatedAt;

    /**
     * 1:删除；0:未删除
     */
    private Integer isDeleted;


    @Transient
    private int num;

    @Transient
    @ScLink(objName = "parent", filed = "displayName")
    @Excel(name = "父级菜单", width = 25)
    private String parentDisplayName;

}
