package cn.shencom.model.dto.resp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import lombok.*;

import java.io.Serializable;

/**
 * 系统角色，权限，组织关联表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.SysRoleUserOrganization
 * @since 2025-06-25
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@ExcelTarget("SysRoleUserOrganizationRespDTO")
public class SysRoleUserOrganizationRespDTO implements Serializable {

    /**
     * 
     */
    @Excel(name = "", width = 25)
    private String id;

    /**
     * 用户id
     */
    @Excel(name = "用户id", width = 25)
    private String userId;

    /**
     * 角色id
     */
    @Excel(name = "角色id", width = 25)
    private String roleId;

    /**
     * 角色标识
     */
    private String roleName;

    /**
     * 角色名称
     */
    private String displayName;

    /**
     * 组织id
     */
    @Excel(name = "组织id", width = 25)
    private String organizationId;

}
