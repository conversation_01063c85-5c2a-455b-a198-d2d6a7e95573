package cn.shencom.model.dto.resp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import lombok.*;

import java.io.Serializable;

/**
 * 施工单位组织关联表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.ContractingUnitOrganizationRelate
 * @since 2025-07-16
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@ExcelTarget("ContractingUnitOrganizationRelateRespDTO")
public class ContractingUnitOrganizationRelateRespDTO implements Serializable {

    /**
     * 主键ID
     */
    @Excel(name = "主键ID", width = 25)
    private String id;

    /**
     * 施工单位ID
     */
    @Excel(name = "施工单位ID", width = 25)
    private String contractingUnitId;

    /**
     * 组织ID
     */
    @Excel(name = "组织ID", width = 25)
    private String organizationId;

    /**
     * 信用分数，每个组织独立计算
     */
    @Excel(name = "信用分数，每个组织独立计算", width = 25)
    private java.math.BigDecimal creditScore;

    /**
     * 是否黑名单：0-否，1-是
     */
    @Excel(name = "是否黑名单：0-否，1-是", width = 25)
    private Integer isBlacklist;

    /**
     * 黑名单原因
     */
    @Excel(name = "黑名单原因", width = 25)
    private String blacklistReason;

    /**
     * 加入黑名单时间
     */
    @Excel(name = "加入黑名单时间", width = 25)
    private java.util.Date blacklistTime;

    /**
     * 施工单位负责人
     */
    @Excel(name = "施工单位负责人", width = 25)
    private String leader;

    /**
     * 施工单位负责人电话
     */
    @Excel(name = "施工单位负责人电话", width = 25)
    private String leaderMobile;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 25)
    private java.util.Date createdAt;

    /**
     * 更新时间
     */
    @Excel(name = "更新时间", width = 25)
    private java.util.Date updatedAt;

    /**
     * 创建人
     */
    @Excel(name = "创建人", width = 25)
    private String createdUser;

    /**
     * 更新人
     */
    @Excel(name = "更新人", width = 25)
    private String updatedUser;

}
