package cn.shencom.model.dto.resp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import cn.shencom.scloud.common.util.Date2LongSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.*;

import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.io.Serializable;

/**
 * 场景类别管理表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.AimSceneCategoryManagement
 * @since 2022-07-26
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@ExcelTarget("AimSceneCategoryManagementRespDTO")
public class AimSceneCategoryManagementRespDTO implements Serializable {

    /**
     * 
     */
    private String id;

    /**
     * 场景类别名称
     */
    @Excel(name = "场景类别名称", width = 25, orderNum = "1")
    private String name;

    /**
     * 场景类别说明
     */
    @Excel(name = "场景类别说明", width = 25, orderNum = "2")
    private String directions;

    /**
     * 关联场景数
     */
    @Excel(name = "关联场景数", width = 25, orderNum = "3")
    private Long sceneNum;

    /**
     * 备注
     */
    @Excel(name = "备注", width = 25, orderNum = "5")
    private String remark;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 25, orderNum = "4", format = "yyyy-MM-dd HH:mm:ss")
    @Temporal(TemporalType.TIMESTAMP)
    @JsonSerialize(using = Date2LongSerializer.class)
    private java.util.Date createdAt;

    /**
     * 更新时间
     */
    private java.util.Date updatedAt;

}
