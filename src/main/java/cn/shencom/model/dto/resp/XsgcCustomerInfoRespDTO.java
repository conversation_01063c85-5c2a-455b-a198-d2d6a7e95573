package cn.shencom.model.dto.resp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * 小散工程-客户信息表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.XsgcCustomerInfo
 * @since 2025-06-25
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@ExcelTarget("XsgcCustomerInfoRespDTO")
public class XsgcCustomerInfoRespDTO implements Serializable {

    /**
     * id
     */
    @Excel(name = "id", width = 25)
    private String id;

    /**
     * 客户编码
     */
    @Excel(name = "客户编码", width = 25)
    private String number;

    /**
     * 客户名称
     */
    @Excel(name = "客户名称", width = 25)
    private String name;

    /**
     * 当前状态,0无效，1-有效
     */
    @Excel(name = "当前状态,0无效，1-有效", width = 25)
    private Integer status;

    /**
     * 服务开始日期
     */
    @Excel(name = "服务开始日期", width = 25)
    private java.util.Date startDate;

    /**
     * 服务结束日期
     */
    @Excel(name = "服务结束日期", width = 25)
    private java.util.Date endDate;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 25)
    private java.util.Date createdAt;

    /**
     * 更新时间
     */
    @Excel(name = "更新时间", width = 25)
    private java.util.Date updatedAt;

    private String createdUser;



    /**
     * 创建人
     */
    @Excel(name = "创建人", width = 25)
    private String createdUserName;

    /**
     * 修改人
     */
    @Excel(name = "修改人", width = 25)
    private String updatedUser;

    /**
     * 套餐id
     */
    @Excel(name = "套餐id", width = 25)
    private String optionId;

    /**
     * 组织id
     */
    @Excel(name = "组织id", width = 25)
    private String organizationId;

    /**
     * 联系人
     */
    @Excel(name = "联系人", width = 25)
    private String contactUser;

    /**
     * 联系人电话
     */
    @Excel(name = "联系人电话", width = 25)
    private String contactPhone;

    /**
     * 联系人邮箱
     */
    @Excel(name = "联系人邮箱", width = 25)
    private String contactEmail;

    /**
     * 备注
     */
    @Excel(name = "备注", width = 25)
    private String memo;

    /**
     * logo
     */
    @Excel(name = "logo", width = 25)
    private String logo;

    /**
     * 续签标记，0-首次开通,1-续签,2-临时续签
     */
    @Excel(name = "续签标记，0-首次开通,1-续签,2-临时续签", width = 25)
    private Integer renewalMark;


    /**
     * 套餐名称
     */
    @Excel(name = "套餐名称", width = 25)
    private String subscriptionName;


    /**
     * 管理员数量
     */
    @Excel(name = "管理员数量", width = 25)
    private Integer administratorNum;

    /**
     * 普通用户数量
     */
    @Excel(name = "普通用户数量", width = 25)
    private Integer ordinaryUserNum;


    /**
     * 技术人员
     */
    private List<XsgcBusinessMembersRelateRespDTO> technician;

    /**
     * 商务人员
     */
    private List<XsgcBusinessMembersRelateRespDTO> businessPeople;

    /**
     * 销售人员
     */
    private List<XsgcBusinessMembersRelateRespDTO> salesperson;

    /**
     * 安装人员
     */
    private List<XsgcBusinessMembersRelateRespDTO> installer;
}
