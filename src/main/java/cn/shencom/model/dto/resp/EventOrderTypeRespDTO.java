package cn.shencom.model.dto.resp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import lombok.*;

import java.io.Serializable;

/**
 * 监控事件类型DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.EventOrderType
 * @since 2023-11-22
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@ExcelTarget("EventOrderTypeRespDTO")
public class EventOrderTypeRespDTO implements Serializable {

    /**
     * 主键自增
     */
    @Excel(name = "主键自增", width = 25)
    private String id;

    /**
     * 事件名称
     */
    @Excel(name = "事件名称", width = 25)
    private String title;

    /**
     * 排序
     */
    @Excel(name = "排序", width = 25)
    private Integer sort;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 25)
    private java.util.Date createdAt;

    /**
     * 更新时间
     */
    @Excel(name = "更新时间", width = 25)
    private java.util.Date updatedAt;

}
