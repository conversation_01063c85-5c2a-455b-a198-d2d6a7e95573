package cn.shencom.model.dto.resp;

import lombok.*;

import java.io.Serializable;

/**
 * 统计DTO
 *
 * <AUTHOR>
 * @since 2022-07-26
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class AimSceneStatisticsDTO implements Serializable {

    /**
     * 厂商数
     */
    private Long firmNum;

    /**
     * 厂商场景数
     */
    private Long firmSceneNum;

    /**
     * 场景数
     */
    private Long sceneNum;

    /**
     * 场景类别数
     */
    private Long sceneCategoryNum;

    /**
     * 产生事件数
     */
    private Long eventNum;

    /**
     * 已处理事件数
     */
    private Long handledEventNum;

    /**
     * 投放点数
     */
    private Long pointNum;

}
