package cn.shencom.model.dto.resp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import lombok.*;

import java.io.Serializable;

/**
 * aiot_scene_categoryDTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.AiotSceneCategory
 * @since 2025-06-10
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@ExcelTarget("AiotSceneCategoryRespDTO")
public class AiotSceneCategoryRespDTO implements Serializable {

    /**
     * 
     */
    @Excel(name = "", width = 25)
    private String id;

    /**
     * 场景一级分类代码
     */
    @Excel(name = "场景一级分类代码", width = 25)
    private String cateCode;

    /**
     * 场景代码
     */
    @Excel(name = "场景代码", width = 25)
    private String sceneCode;

}
