package cn.shencom.model.dto.resp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import lombok.*;

import java.io.Serializable;

/**
 * 小散工程-工程备注表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.SporadicProjectMemo
 * @since 2025-07-18
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@ExcelTarget("SporadicProjectMemoRespDTO")
public class SporadicProjectMemoRespDTO implements Serializable {

    /**
     * ID
     */
    @Excel(name = "ID", width = 25)
    private String id;

    /**
     * 工程ID
     */
    @Excel(name = "工程ID", width = 25)
    private String projectId;

    /**
     * 备注内容
     */
    @Excel(name = "备注内容", width = 25)
    private String content;

    /**
     * 用户id
     */
    @Excel(name = "用户id", width = 25)
    private String userId;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 25)
    private java.util.Date createdAt;

    /**
     * 更新时间
     */
    @Excel(name = "更新时间", width = 25)
    private java.util.Date updatedAt;

    /**
     * 创建人
     */
    @Excel(name = "创建人", width = 25)
    private String createdUserName;

}
