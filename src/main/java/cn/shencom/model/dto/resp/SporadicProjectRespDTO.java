package cn.shencom.model.dto.resp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 小散工程表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.SporadicProject
 * @since 2025-05-29
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@ExcelTarget("SporadicProjectRespDTO")
public class SporadicProjectRespDTO implements Serializable {

    /**
     * 工程ID
     */
    @Excel(name = "工程ID", width = 25)
    private String id;

    /**
     * 工程名称
     */
    @Excel(name = "工程名称", width = 25)
    private String name;

    /**
     * 工程分类ID
     */
    private String catePid;

    /**
     * 工程类别ID
     */
    private String cateId;
    @Excel(name = "工程分类", width = 25)
    private String pCateName;
    @Excel(name = "工程类别", width = 25)
    private String cateName;

    /**
     * 工程金额(元)
     */
    @Excel(name = "工程金额(元)", width = 25, format = "#.##")
    private java.math.BigDecimal amount;

    /**
     * 实际施工面积
     */
    @Excel(name = "实际施工面积", width = 25, format = "#.##")
    private java.math.BigDecimal area;

    /**
     * 工程开始时间
     */
    @Excel(name = "工程开始时间", width = 25, format = "yyyy-MM-dd")
    @JSONField(format = "yyyy-MM-dd")
    private java.util.Date startAt;

    /**
     * 工程结束时间
     */
    @Excel(name = "工程结束时间", width = 25)
    @JSONField(format = "yyyy-MM-dd")
    private java.util.Date endAt;

    @Excel(name = "所属区域", width = 25)
    private String districtName;
    @Excel(name = "所属街道", width = 25)
    private String streetName;
    @Excel(name = "所属社区", width = 25)
    private String villageName;

    /**
     * 所在区
     */

    private String regionPid;

    /**
     * 所在街道
     */

    private String regionId;

    /**
     * 所在社区
     */
    private String regionCid;

    /**
     * 详细地址
     */
    @Excel(name = "详细地址", width = 25)
    private String address;

    /**
     * 经度
     */
    private BigDecimal lng;

    /**
     * 纬度
     */
    private BigDecimal lat;
    /**
     * 建设单位
     */
    @Excel(name = "建设单位", width = 25)
    private String constructorName;

    /**
     * 建设单位负责人
     */
    @Excel(name = "建设单位负责人", width = 25)
    private String constructorCharger;

    /**
     * 业主电话
     */
    @Excel(name = "业主电话", width = 25)
    private String ownerMobile;

    /**
     * 施工单位ID
     */
    private String contractorId;

    /**
     * 施工单位名称
     */
    @Excel(name = "施工单位名称", width = 25)
    private String contractorName;

    /**
     * 施工单位负责人
     */
    @Excel(name = "施工单位负责人", width = 25)
    private String contractorCharger;

    /**
     * 施工单位负责人电话
     */
    @Excel(name = "施工单位负责人电话", width = 25)
    private String contractorChargerMobile;

    /**
     * 施工状态:0-未开始,1-施工中,2-已结束
     */
    private Integer status;
    /**
     * 施工状态:0-未开始,1-施工中,2-已结束
     */
    @Excel(name = "施工状态", width = 25)
    private String statusName;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 25, format = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd")
    private java.util.Date createdAt;

    /**
     * 更新时间
     */
    @Excel(name = "更新时间", width = 25, format = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd")
    private java.util.Date updatedAt;

    /**
     * 是否接入监管
     * 0-否 1-是
     */
    @Excel(name = "是否接入监管", width = 25, replace = { "否_0", "是_1", "已结束监管_2" })
    private Integer monitorFlag;

    /**
     * 备案编号
     */
    @Excel(name = "备案编号", width = 25)
    private String projectNumber;

    /**
     * poiId
     */
    private String poiId;

    // ========= 移动端需要的其他字段
    /**
     * 巡查次数
     */
    private Integer inspectRecordNum;
    /**
     * 违规事项数
     */
    private Integer violationRecordNum;
    /**
     * 整改事项数
     */
    private Integer rectifyRecordNum;

    /**
     * 监控事件数
     */
    private Integer eventNumber;
    /**
     * 已处理的监控事件数
     */
    private Integer rectifyEventNumber;

    // ===========监管流程工单

    /**
     * 工单id
     */
    private String orderId;

    /**
     * 当前的流程
     */
    private Integer flow;

    /**
     * 当前流程id
     */
    private String flowId;

    private Integer sort;


    private Date reservationTime;

    private Integer installStatus;

    private Integer recycleStatus;



    private List<String> cameraDeviceIds;
    private List<String> cameraSerialNos;


    // ========= 备注信息
    private List<SporadicProjectMemoRespDTO> memoRespDTOList;
}
