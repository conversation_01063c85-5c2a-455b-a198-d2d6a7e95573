package cn.shencom.model.dto.resp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import lombok.*;

import java.io.Serializable;

/**
 * 小散工程-业务人员DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.XsgcBusinessMembers
 * @since 2025-06-25
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@ExcelTarget("XsgcBusinessMembersRespDTO")
public class XsgcBusinessMembersRespDTO implements Serializable {

    /**
     * ID
     */
    @Excel(name = "ID", width = 25)
    private String id;

    /**
     * 用户id
     */
    @Excel(name = "用户id", width = 25)
    private String userId;

    /**
     * 成员姓名
     */
    @Excel(name = "成员姓名", width = 25)
    private String realname;

    /**
     * 手机号码
     */
    @Excel(name = "手机号码", width = 25)
    private String mobile;

    /**
     * 职位类型
     * 1.技术人员
     * 2.商务人员
     * 3.销售人员
     * 4.安装人员
     */
    @Excel(name = "职位类型", width = 25)
    private Integer type;

    /**
     * 描述
     */
    @Excel(name = "描述", width = 25)
    private String desc;

    /**
     * 有效状态，0-无效，1-有效
     */
    @Excel(name = "有效状态，0-无效，1-有效", width = 25)
    private Integer status;

    /**
     * 图片id列表
     */
    @Excel(name = "图片id列表", width = 25)
    private String pic;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 25)
    private java.util.Date createdAt;

    /**
     * 最后更新时间
     */
    @Excel(name = "最后更新时间", width = 25)
    private java.util.Date updatedAt;

}
