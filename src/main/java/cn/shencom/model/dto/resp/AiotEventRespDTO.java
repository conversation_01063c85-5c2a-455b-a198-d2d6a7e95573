package cn.shencom.model.dto.resp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import cn.shencom.scloud.common.jpa.scpage.ScLink;
import lombok.*;

import javax.persistence.Transient;
import java.io.Serializable;
import java.util.List;

/**
 * 监控事件DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.AiotEvent
 * @since 2025-06-10
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@ExcelTarget("AiotEventRespDTO")
public class AiotEventRespDTO implements Serializable {

    /**
     * 
     */
    @Excel(name = "", width = 25)
    private String id;

    /**
     * 区ID
     */
    private String regionPid;

    /**
     * 街道ID
     */
    private String regionId;

    /**
     * 社区ID
     */
    private String regionCid;

    /**
     * 项目ID
     */
    private String projectId;
    /**
     * 辖区
     */
    @Excel(name = "辖区", width = 25)
    private String districtName;
    /**
     * 街道
     */
    @Excel(name = "街道", width = 25)
    private String streetName;
    /**
     * 社区
     */
    @Excel(name = "社区", width = 25)
    private String villageName;
    /**
     * 工程名称
     */
    @Excel(name = "工程名称", width = 25)
    private String projectName;

    /**
     * 工程地址
     */
    @Excel(name = "工程地址", width = 25)
    private String projectAddress;


    /**
     * 工程类别
     */
    @Excel(name = "工程类别", width = 25)
    private String projectCateName;

    /**
     * 事件编号
     */
    @Excel(name = "事件编号", width = 25)
    private String eventNo;

    /**
     * 设备编码(国标视频认证编码ID)
     */
    @Excel(name = "设备编码(国标视频认证编码ID)", width = 25)
    private String deviceCode;

    /**
     * 通道编码
     */
    @Excel(name = "通道编码", width = 25)
    private String channelCode;

    /**
     * 摄像头编号
     */
    @Excel(name = "摄像头编号", width = 25)
    private String monitorNo;

    /**
     * 违规场景编码
     */
    @Excel(name = "违规场景编码", width = 25)
    private String sceneCode;

    /**
     * 违规类型
     */
    @Excel(name = "违规类型", width = 25)
    private String sceneName;


    /**
     * AI场景
     */
    @Excel(name = "AI场景", width = 25)
    private String typeName;


    /**
     * 事件图片
     */
    @Excel(name = "事件图片", width = 25)
    private String pics;

    /**
     * 违规框
     */
    @Excel(name = "违规框", width = 25)
    private String violationBox;

    /**
     * 事件时间
     */
    @Excel(name = "事件时间", width = 25)
    private java.util.Date eventAt;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 25)
    private java.util.Date createdAt;

    /**
     * 更新时间
     */
    @Excel(name = "更新时间", width = 25)
    private java.util.Date updatedAt;
//
//    /**
//     * 场景分类名称
//     */
//    private List<String> sceneCateNames;
//    /**
//     * AI场景名称
//     */
//    private List<String> aiSceneNames;



    /**
     * 场景类别
     */
    @Excel(name = "场景类别", width = 25)
    private String sceneCategoryName;
}
