package cn.shencom.model.dto.resp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import lombok.*;

import java.io.Serializable;

/**
 * 小散工程-业务人员客户关联表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.XsgcBusinessMembersRelate
 * @since 2025-06-25
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@ExcelTarget("XsgcBusinessMembersRelateRespDTO")
public class XsgcBusinessMembersRelateRespDTO implements Serializable {

    /**
     * ID
     */
    @Excel(name = "ID", width = 25)
    private String id;

    /**
     * 客户id
     */
    @Excel(name = "客户id", width = 25)
    private String memberId;


    private String realname;

    private String mobile;

    private String type;


    /**
     * 客户id
     */
    @Excel(name = "客户id", width = 25)
    private String customerId;

    /**
     * 组织id
     */
    @Excel(name = "组织id", width = 25)
    private String organizationId;

    /**
     * 有效状态，0-无效，1-有效
     */
    @Excel(name = "有效状态，0-无效，1-有效", width = 25)
    private Integer status;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 25)
    private java.util.Date createdAt;

    /**
     * 最后更新时间
     */
    @Excel(name = "最后更新时间", width = 25)
    private java.util.Date updatedAt;

}
