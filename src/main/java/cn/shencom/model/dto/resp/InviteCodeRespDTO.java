package cn.shencom.model.dto.resp;

import lombok.Data;

import java.util.Date;

/**
 * 邀请码生成响应DTO
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Data
public class InviteCodeRespDTO {

    /**
     * 邀请码
     */
    private String inviteCode;

    /**
     * 工程ID
     */
    private String projectId;

    /**
     * 工程名称
     */
    private String projectName;

    /**
     * 生成时间
     */
    private Date generatedAt;

    /**
     * 过期时间
     */
    private Date expireAt;

    /**
     * 有效期（秒）
     */
    private Long expireSeconds;
}
