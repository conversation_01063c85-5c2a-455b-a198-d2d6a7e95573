package cn.shencom.model.dto.resp;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ExApiCameraStatusRespDTO implements Serializable {

    /**
     * id 原样返回
     */
    private String id;
    /**
     * 设备号
     */
    private String deviceCode;
    /**
     * 原样返回
     */
    private String peripheryCode;
    /**
     * 状态
     */
    private Integer status;
}
