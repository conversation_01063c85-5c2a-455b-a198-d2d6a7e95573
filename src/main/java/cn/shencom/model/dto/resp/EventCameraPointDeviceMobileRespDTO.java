package cn.shencom.model.dto.resp;

import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import cn.shencom.model.EventCameraPoint;
import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * 监控设备表移动端响应DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.EventCameraPointDevice
 * @see cn.shencom.model.dto.resp.EventCameraPointDeviceRespDTO
 * @since 2025-05-16
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@ExcelTarget("EventCameraPointDeviceMobileRespDTO")
public class EventCameraPointDeviceMobileRespDTO implements Serializable {

    /**
     * 主键id
     */
    private String id;

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目地址
     */
    private String projectAddress;

    /**
     * 摄像头列表
     */
    private List<EventCameraPoint> eventCameraPointList;
}