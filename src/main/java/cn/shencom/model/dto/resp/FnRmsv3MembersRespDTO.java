package cn.shencom.model.dto.resp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import lombok.*;

import java.io.Serializable;
import java.util.Set;

/**
 * 小散工程-组织团队成员表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.FnRmsv3Members
 * @since 2025-06-25
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@ExcelTarget("FnRmsv3MembersRespDTO")
public class FnRmsv3MembersRespDTO implements Serializable {

    /**
     * id
     */
    @Excel(name = "id", width = 25)
    private String id;

    /**
     * 用户id
     */
    @Excel(name = "用户id", width = 25)
    private String userId;


    /**
     * 姓名
     */
    @Excel(name = "姓名", width = 25)
    private String realname;



    /**
     * 类型id
     * */
    private Set<String> typeIds;


    /**
     * 手机号
     */
    @Excel(name = "手机号", width = 25)
    private String mobile;

    /**
     * 组织id
     */
    @Excel(name = "组织id", width = 25)
    private String organizationId;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 25)
    private java.util.Date createdAt;

    /**
     * 最后更新时间
     */
    @Excel(name = "最后更新时间", width = 25)
    private java.util.Date updatedAt;

}
