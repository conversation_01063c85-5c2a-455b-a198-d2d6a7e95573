package cn.shencom.model.dto.resp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import lombok.*;

import java.io.Serializable;

/**
 * 设备型号配置表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.EventModelTypeConfig
 * @since 2025-05-16
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@ExcelTarget("EventModelTypeConfigRespDTO")
public class EventModelTypeConfigRespDTO implements Serializable {

    /**
     * 
     */
    @Excel(name = "", width = 25)
    private String id;

    /**
     * 深传设备型号
     */
    @Excel(name = "深传设备型号", width = 25)
    private String scModelNo;

    /**
     * 海康设备型号
     */
    @Excel(name = "海康设备型号", width = 25)
    private String hkModelNo;

    /**
     * 图片,在线图标
     */
    @Excel(name = "图片,在线图标", width = 25)
    private String pic;

    /**
     * 离线图标
     */
    @Excel(name = "离线图标", width = 25)
    private String offLinePic;

    /**
     * 
     */
    @Excel(name = "", width = 25)
    private java.util.Date createdAt;

    /**
     * 
     */
    @Excel(name = "", width = 25)
    private java.util.Date updatedAt;

}
