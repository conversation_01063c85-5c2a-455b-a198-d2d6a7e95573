package cn.shencom.model.dto.resp;

import lombok.*;

import java.io.Serializable;

/**
 * 小散工程-监管工单DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.MonitorOrder
 * @since 2025-07-04
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class MonitorOrderRespOverviewDTO implements Serializable {
  /** 待预约 */
  private long waitingReservation;
  /** 待检测 */
  private long waitingInspection;
  /** 待安装 */
  private long waitingInstallation;
  /** 待回收 */
  private long waitingRecycle;
  /** 待服务 */
  private long waitingService;
}
