package cn.shencom.model.dto.resp;

import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 统计数据响应DTO
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
public class ReportRespDTO implements Serializable {

    @Getter
    @Setter
    @ToString
    @Builder
    static public class ProjectStatisticsRespDTO {
        /** 施工单位数量 */
        private Integer contractingUnitCount;
        /** 工程总数 */
        private Integer totalProjectCount;
        /** 施工中工程数量 */
        private Integer ongoingProjectCount;
        /** 工程总金额（万元） */
        private BigDecimal totalAmount;
        /** 待整改工程数量 */
        private Integer rectifyProjectCount;
    }

}
