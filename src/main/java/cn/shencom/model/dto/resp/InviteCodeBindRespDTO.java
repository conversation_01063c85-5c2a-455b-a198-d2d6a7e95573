package cn.shencom.model.dto.resp;

import lombok.Data;

import java.util.Date;

/**
 * 邀请码绑定响应DTO
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Data
public class InviteCodeBindRespDTO {

    /**
     * 工程成员ID
     */
    private String memberId;

    /**
     * 工程ID
     */
    private String projectId;

    /**
     * 工程名称
     */
    private String projectName;

    /**
     * 成员姓名
     */
    private String realname;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 职位类型
     */
    private Integer type;

    /**
     * 职位名称
     */
    private String typeName;

    /**
     * 绑定时间
     */
    private Date bindTime;

    /**
     * 是否新创建的成员
     */
    private Boolean isNewMember;
}
