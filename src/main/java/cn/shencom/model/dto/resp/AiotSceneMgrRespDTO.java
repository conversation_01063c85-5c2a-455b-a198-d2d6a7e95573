package cn.shencom.model.dto.resp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import lombok.*;

import java.io.Serializable;

/**
 * 场景类别DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.AiotSceneMgr
 * @since 2024-08-03
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@ExcelTarget("AiotSceneMgrRespDTO")
public class AiotSceneMgrRespDTO implements Serializable {

    /**
     * 主键
     */
    @Excel(name = "主键", width = 25)
    private String id;

    /**
     * 父id
     */
    @Excel(name = "父id", width = 25)
    private String pId;

    private String allParentId;

    /**
     * 场景类别编号
     */
    @Excel(name = "场景类别编号", width = 25)
    private String sceneCode;

    /**
     * 场景名称
     */
    @Excel(name = "场景类别名称", width = 25)
    private String sceneName;

    /**
     * 场景说明
     */
    @Excel(name = "场景类别说明", width = 25)
    private String sceneIllustrate;

    /**
     * 创建人
     */
    @Excel(name = "创建人", width = 25)
    private String createdBy;

    /**
     * 创建人姓名
     */
    @Excel(name = "创建人姓名", width = 25)
    private String createdByName;

    /**
     * 修改人
     */
    @Excel(name = "修改人", width = 25)
    private String updatedBy;

    /**
     * 修改人姓名
     */
    @Excel(name = "修改人姓名", width = 25)
    private String updatedByName;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 25)
    private java.util.Date createdAt;

    /**
     * 更新时间
     */
    @Excel(name = "更新时间", width = 25)
    private java.util.Date updatedAt;

}
