package cn.shencom.model.dto.resp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import lombok.*;

import java.io.Serializable;

/**
 * aiot违规类型场景DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.AiotScene
 * @since 2025-06-10
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@ExcelTarget("AiotSceneRespDTO")
public class AiotSceneRespDTO implements Serializable {

    /**
     * 
     */
    @Excel(name = "", width = 25)
    private String id;

    /**
     * 场景名称
     */
    @Excel(name = "场景名称", width = 25)
    private String name;

    /**
     * 场景代码
     */
    @Excel(name = "场景代码", width = 25)
    private String code;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 25)
    private java.util.Date createdAt;

    /**
     * 更新时间
     */
    @Excel(name = "更新时间", width = 25)
    private java.util.Date updatedAt;

}
