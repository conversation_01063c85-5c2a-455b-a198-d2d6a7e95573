package cn.shencom.model.dto.resp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import lombok.*;

import java.io.Serializable;

/**
 * 摄像头信息表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.EventCameraPoint
 * @since 2025-05-16
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@ExcelTarget("EventCameraPointRespDTO")
public class EventCameraPointRespDTO implements Serializable {

    /**
     * 主键id
     */
    @Excel(name = "主键id", width = 25)
    private String id;

    /**
     * device_id
     */
    @Excel(name = "device_id", width = 25)
    private String deviceId;

    /**
     * 序列号
     */
    @Excel(name = "序列号", width = 25)
    private String serialNo;

    /**
     * 设备号
     */
    @Excel(name = "设备号", width = 25)
    private String monitorNo;

    /**
     * FLV协议播放地址（流畅）
     */
    @Excel(name = "FLV协议播放地址（流畅）", width = 25)
    private String flvAddress;

    /**
     * FLV协议直播地址（高清）
     */
    @Excel(name = "FLV协议直播地址（高清）", width = 25)
    private String hdFlvAddress;

    @Excel(name = "真实的设备型号，海康", width = 25)
    private String modelNo;

    /**
     * 小散项目ID
     */
    @Excel(name = "小散项目id", width = 25)
    private String projectId;

    /**
     * 是否语音播报，0-不是，1-是
     */
    @Excel(name = "是否语音播报，0-不是，1-是", width = 25)
    private Integer voiceBroadcast;
    /**
     * 感知类型
     */
    @Excel(name = "感知类型", width = 25)
    private String perceptionTypes;

    /**
     * 是否智能，0-不是，1-是
     */
    @Excel(name = "是否智能，0-不是，1-是", width = 25)
    private Integer smart;

    @Excel(name = "类型，1-自建，2-环卫云，3-车载监控，4-萤石云，5-车载视频API，6-锐明，7-海康车载视频API", width = 25)
    private Integer type;

    /**
     * 在线状态，0-离线，1-在线
     */
    @Excel(name = "在线状态，0-离线，1-在线", width = 25)
    private Integer status;

    /**
     * 真实状态 0-离线 1-在线
     */
    @Excel(name = "真实状态 0-离线 1-在线", width = 25)
    private Integer realStatus;

    /**
     * 直播状态，0-未使用或直播已关闭，1-使用中，2-已过期，3-直播已暂停
     */
    @Excel(name = "直播状态，0-未使用或直播已关闭，1-使用中，2-已过期，3-直播已暂停", width = 25)
    private Integer openLive;

    /**
     * 通道号
     */
    @Excel(name = "通道号", width = 25)
    private String channel;

    /**
     * 备注
     */
    @Excel(name = "备注", width = 25)
    private String memo;

    /**
     * 在线时间
     */
    @Excel(name = "在线时间", width = 25)
    private java.util.Date onlineAt;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 25)
    private java.util.Date createdAt;

    /**
     * 最后更新时间
     */
    @Excel(name = "最后更新时间", width = 25)
    private java.util.Date updatedAt;

    /**
     * 物联网卡id
     */
    @Excel(name = "物联网卡id", width = 25)
    private String cardId;

    /**
     * 出入库状态：1-已入库，2-已出库
     */
    @Excel(name = "出入库状态：1-已入库，2-已出库", width = 25)
    private Integer stockStatus;

    /**
     * 是否锁定 0-正常 1-锁定
     */
    @Excel(name = "是否锁定 0-正常 1-锁定", width = 25)
    private Integer isLock;

    /**
     * 是否健康 0-异常 1-健康
     */
    @Excel(name = "是否健康 0-异常 1-健康", width = 25)
    private Integer isHealthy;

    /**
     * 原始序列号
     */
    @Excel(name = "原始序列号", width = 25)
    private String originalSerialNo;

    /**
     * 通道名称
     */
    @Excel(name = "通道名称", width = 25)
    private String monitorName;

    /**
     * 通道id
     */
    @Excel(name = "通道id", width = 25)
    private String channelId;

    /**
     * 真实在线时间
     */
    @Excel(name = "真实在线时间", width = 25)
    private java.util.Date realOnlineAt;

    @Excel(name = "深传设备型号", width = 25)
    private String scModelNo;

    private String sipUserId;
}
