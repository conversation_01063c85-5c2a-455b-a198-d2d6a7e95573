package cn.shencom.model.dto.resp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import lombok.*;

import java.io.Serializable;

/**
 * 小散工程-组织团队成员区域关联关系DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.FnRmsv3MembersTypeRelateBinding
 * @since 2025-06-25
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@ExcelTarget("FnRmsv3MembersTypeRelateBindingRespDTO")
public class FnRmsv3MembersTypeRelateBindingRespDTO implements Serializable {

    /**
     * 主键id
     */
    @Excel(name = "主键id", width = 25)
    private String id;

    /**
     * fn_rmsv3_members_type_relate id
     */
    @Excel(name = "fn_rmsv3_members_type_relate id", width = 25)
    private String relateId;

    /**
     * 区
     */
    @Excel(name = "区", width = 25)
    private String regionPid;

    /**
     * 街道
     */
    @Excel(name = "街道", width = 25)
    private String regionId;

    /**
     * 社区
     */
    @Excel(name = "社区", width = 25)
    private String regionCid;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 25)
    private java.util.Date createdAt;

    /**
     * 修改时间
     */
    @Excel(name = "修改时间", width = 25)
    private java.util.Date updatedAt;

}
