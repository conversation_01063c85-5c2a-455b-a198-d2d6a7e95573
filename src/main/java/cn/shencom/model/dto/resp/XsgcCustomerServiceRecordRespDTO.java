package cn.shencom.model.dto.resp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import lombok.*;

import java.io.Serializable;

/**
 * 小散工程-客户服务开通记录DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.XsgcCustomerServiceRecord
 * @since 2025-06-25
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@ExcelTarget("XsgcCustomerServiceRecordRespDTO")
public class XsgcCustomerServiceRecordRespDTO implements Serializable {

    /**
     * id
     */
    @Excel(name = "id", width = 25)
    private String id;

    /**
     * 客户id
     */
    @Excel(name = "客户id", width = 25)
    private String customerId;

    /**
     * 服务开始日期
     */
    @Excel(name = "服务开始日期", width = 25)
    private java.util.Date startDate;

    /**
     * 服务结束日期
     */
    @Excel(name = "服务结束日期", width = 25)
    private java.util.Date endDate;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 25)
    private java.util.Date createdAt;

    /**
     * 更新时间
     */
    @Excel(name = "更新时间", width = 25)
    private java.util.Date updatedAt;

    /**
     * 创建人
     */
    @Excel(name = "创建人", width = 25)
    private String createdUser;

    /**
     * 修改人
     */
    @Excel(name = "修改人", width = 25)
    private String updatedUser;

    /**
     * 套餐id
     */
    @Excel(name = "套餐id", width = 25)
    private String optionId;

    /**
     * 组织id
     */
    @Excel(name = "组织id", width = 25)
    private String organizationId;

    /**
     * 套餐说明
     */
    @Excel(name = "套餐说明", width = 25)
    private String memo;

    /**
     * 续签标记，0-首次开通,1-续签,2-临时续签
     */
    @Excel(name = "续签标记，0-首次开通,1-续签,2-临时续签", width = 25)
    private Integer renewalMark;


    /**
     * 开通人
     */
    private String createdUserName;


    /**
     * 套餐名称
     */
    private String subscriptionName;
}
