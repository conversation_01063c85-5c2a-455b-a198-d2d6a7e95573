package cn.shencom.model.dto.resp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import cn.shencom.scloud.common.util.Date2LongSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.*;

import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.io.Serializable;

/**
 * 厂商场景管理DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.AimFirmSceneManagement
 * @since 2022-07-27
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@ExcelTarget("AimFirmSceneManagementRespDTO")
public class AimFirmSceneManagementRespDTO implements Serializable {

    /**
     * 
     */
    @Excel(name = "", width = 25)
    private String id;

    /**
     * 厂商id
     */
    private String firmId;

    /**
     * 厂商名称
     */
    @Excel(name = "厂商名称", width = 25)
    private String firmName;

    /**
     * 厂商场景编号
     */
    @Excel(name = "厂商场景编号", width = 25)
    private String code;

    /**
     * 厂商场景名称
     */
    @Excel(name = "厂商场景名称", width = 25)
    private String sceneName;

    /**
     * 关联AI场景数
     */
    @Excel(name = "关联AI场景数", width = 25)
    private Integer sceneNum;

    /**
     * 场景介绍
     */
    @Excel(name = "场景介绍", width = 25)
    private String directions;

    /**
     * 图片（逗号分隔）
     */
    @Excel(name = "图片（逗号分隔）", width = 25)
    private String pics;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 25, format = "yyyy-MM-dd HH:mm:ss")
    @Temporal(TemporalType.TIMESTAMP)
    @JsonSerialize(using = Date2LongSerializer.class)
    private java.util.Date createdAt;

    /**
     * 更新时间
     */
    private java.util.Date updatedAt;

}
