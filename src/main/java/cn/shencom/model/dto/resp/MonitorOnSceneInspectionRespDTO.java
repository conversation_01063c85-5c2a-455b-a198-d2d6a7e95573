package cn.shencom.model.dto.resp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import lombok.*;

import java.io.Serializable;

/**
 * 小散工程-监管工单流程-现场勘察详情DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.MonitorOnSceneInspection
 * @since 2025-07-04
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@ExcelTarget("MonitorOnSceneInspectionRespDTO")
public class MonitorOnSceneInspectionRespDTO implements Serializable {

    /**
     * id
     */
    @Excel(name = "id", width = 25)
    private String id;

    /**
     * 工单id，关联monitor_order
     */
    @Excel(name = "工单id，关联monitor_order", width = 25)
    private String orderId;

    /**
     * 联系人电话
     */
    @Excel(name = "联系人电话", width = 25)
    private String contactMobile;

    /**
     * 联系人姓名
     */
    @Excel(name = "联系人姓名", width = 25)
    private String contactName;

    /**
     * 勘察说明
     */
    @Excel(name = "勘察说明", width = 25)
    private String memo;

    /**
     * 现场图片
     */
    @Excel(name = "现场图片", width = 25)
    private String pic;

    /**
     * 勘察时间
     */
    @Excel(name = "勘察时间", width = 25)
    private java.util.Date inspectTime;



    /**
     * 预约时间
     */
    @Excel(name = "预约时间", width = 25)
    private java.util.Date reservationTime;



    /**
     * 评估需要安装的监控数
     */
    @Excel(name = "评估需要安装的监控数", width = 25)
    private Integer installCnt;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 25)
    private java.util.Date createdAt;

    /**
     * 更新时间
     */
    @Excel(name = "更新时间", width = 25)
    private java.util.Date updatedAt;

    /**
     * 创建人
     */
    @Excel(name = "创建人", width = 25)
    private String createdUser;

    /**
     * 修改人
     */
    @Excel(name = "修改人", width = 25)
    private String updatedUser;




}
