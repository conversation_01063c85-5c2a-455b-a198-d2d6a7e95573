package cn.shencom.model.dto.resp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import cn.shencom.model.EngineeringMembers;
import lombok.*;

import java.io.Serializable;

/**
 * 小散工程-工程人员关联项目表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.EngineeringMembersProjectRelate
 * @since 2025-07-03
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@ExcelTarget("EngineeringMembersProjectRelateRespDTO")
public class EngineeringMembersProjectRelateRespDTO implements Serializable {

    /**
     * ID
     */
    @Excel(name = "ID", width = 25)
    private String id;

    /**
     * 成员id，关联engineering_members
     */
    @Excel(name = "成员id，关联engineering_members", width = 25)
    private String memberId;

    /**
     * relateId，engineering_members_relate
     */
    @Excel(name = "relateId，engineering_members_relate", width = 25)
    private String relateId;

    /**
     * 组织id,关联xsgc_organization
     */
    @Excel(name = "组织id,关联xsgc_organization", width = 25)
    private String organizationId;

    /**
     * 项目id,关联sporadic_project
     */
    @Excel(name = "项目id,关联sporadic_project", width = 25)
    private String projectId;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 25)
    private java.util.Date createdAt;

    /**
     * 最后更新时间
     */
    @Excel(name = "最后更新时间", width = 25)
    private java.util.Date updatedAt;

    private EngineeringMembers member;
}
