package cn.shencom.model.dto.resp;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class LiveRespDTO {
    /**
     * 高清 flv
     */
    private String hdFlvAddress;

    /**
     * 流畅 flv
     */
    private String flvAddress;

    /**
     *  高清 hls
     */
    private String hdHls;

    /**
     *  流畅 hls
     */
    private String hls;

    /**
     * 1078 url
     */
    private String url;

    /**
     * 超时时间
     */
    private String expireTime;
}
