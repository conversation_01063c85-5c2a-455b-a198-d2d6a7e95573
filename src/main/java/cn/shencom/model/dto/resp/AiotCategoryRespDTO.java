package cn.shencom.model.dto.resp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import lombok.*;

import java.io.Serializable;

/**
 * aiot场景分类DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.AiotCategory
 * @since 2025-06-10
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@ExcelTarget("AiotCategoryRespDTO")
public class AiotCategoryRespDTO implements Serializable {

    /**
     * 
     */
    @Excel(name = "", width = 25)
    private String id;

    /**
     * 上级场景分类ID，顶级为0
     */
    @Excel(name = "上级场景分类ID，顶级为0", width = 25)
    private String pId;

    /**
     * 场景分类级别:0-顶级，1-一级
     */
    @Excel(name = "场景分类级别:0-顶级，1-一级", width = 25)
    private Integer level;

    /**
     * 场景分类名称
     */
    @Excel(name = "场景分类名称", width = 25)
    private String name;

    /**
     * 场景分类代码
     */
    @Excel(name = "场景分类代码", width = 25)
    private String code;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 25)
    private java.util.Date createdAt;

    /**
     * 更新时间
     */
    @Excel(name = "更新时间", width = 25)
    private java.util.Date updatedAt;

}
