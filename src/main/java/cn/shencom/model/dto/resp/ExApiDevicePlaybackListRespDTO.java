package cn.shencom.model.dto.resp;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ExApiDevicePlaybackListRespDTO implements Serializable {
    /**
     * 名称
     */
    private String name;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;



//    private String deviceID;
//    private String type;
//    private String filePath;
//    private int secrecy;
}
