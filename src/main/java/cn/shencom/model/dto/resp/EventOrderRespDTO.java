package cn.shencom.model.dto.resp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * 事件工单表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.EventOrder
 * @since 2021-06-29
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@ExcelTarget("EventOrderRespDTO")
public class EventOrderRespDTO implements Serializable {

    @Excel(name = "工单ID", width = 25)
    private String id;

    @Excel(name = "辖区id", width = 25)
    private String regionPid;

    @Excel(name = "街道id", width = 25)
    private String regionId;

    @Excel(name = "社区id", width = 25)
    private String regionCid;

    @Excel(name = "项目id", width = 25)
    private String projectId;

    @Excel(name = "摄像头编号", width = 25)
    private String cameraNo;

    @Excel(name = "事件编号", width = 25)
    private String eventCode;

    /**
     * 事件来源 1-海康 2-海康服务器 3-鲲云 4-深传
     */
    private Integer eventSource;

    @Excel(name = "事件描述", width = 25)
    private String eventDesc;

    @Excel(name = "图片url列表", width = 25)
    private String pic;

    @Excel(name = "处理事件时的实时截图", width = 25)
    private String disposingPic;

    @Excel(name = "违规类型编号", width = 25)
    private String typeCode;

    @Excel(name = "复核状态，1-重新处理，2-识别有误", width = 25)
    private Integer reviewStatus;

    @Excel(name = "违规类型名称", width = 25)
    private String typeName;

    @Excel(name = "持续时间/秒", width = 25)
    private Integer durationTime;

    @Excel(name = "持续时间字符串", width = 25)
    private String durationTimeStr;

    @Excel(name = "来源，1-智能分析", width = 25)
    private Integer origin;

    @Excel(name = "处理人id", width = 25)
    private String memberId;

    @Excel(name = "备注", width = 25)
    private String memo;

    @Excel(name = "处理意见", width = 25)
    private String disposeDesc;

    @Excel(name = "处理图片id列表", width = 25)
    private String disposePic;

    @Excel(name = "处理时间", width = 25)
    private java.util.Date disposedAt;

    @Excel(name = "事件时间", width = 25)
    private java.util.Date eventedAt;

    @Excel(name = "创建时间", width = 25)
    private java.util.Date createdAt;

    @Excel(name = "更新时间", width = 25)
    private java.util.Date updatedAt;

    @Excel(name = "辖区", width = 25)
    private String districtName;

    @Excel(name = "街道", width = 25)
    private String streetName;

    @Excel(name = "社区", width = 25)
    private String villageName;

    @Excel(name = "项目", width = 25)
    private String pointName;

    @Excel(name = "投放点详细地址", width = 25)
    private String pointAddress;


    @Excel(name = "状态，0-待处理，1-已处理，2-超时自动结束， 3-待复核， 4-已复核", width = 25)
    private Integer status;

    private String userId;

    private Integer disposeType;

    private Integer isAssign;

    private String assignCompanyId;

    private String assignUserId;

    private java.util.Date reviewedAt;

    private java.util.Date assignedAt;


    @Excel(name = "违规图片范围", width = 25)
    private String violationAreas;



    @Excel(name = "是否违规溯源，0-否，1-是", width = 25)
    private Integer isRuleSource;

    @Excel(name = "违规溯源图片", width = 25)
    private String sourcePic;

    @Excel(name = "违规溯源视频", width = 25)
    private String sourceVideo;

    @Excel(name = "超时情况 1=一级处理人超时 2=二级处理人超时 3=三级处理人超时", width = 25)
    private String timeoutCase;

    @Excel(name = "是否及时处理" , replace = {"否_0", "是_1"}, width = 25)
    private Integer isOnTime;

    private Integer cameraType;

    /**
     * 是否需要流转的事件
     */
    private Integer isNeedReturn;

    /**
     * 是否需要本人处理
     */
    private Integer isNeedSelfDispose = 1;

    /**
     * 工单状态
     */
    private String finishStatus;

    /**
     * AI场景
     */
    @Excel(name = "AI场景", width = 25)
    private String sceneName;


    @Excel(name = "事件来源", width = 25)
    private String eventSourceStr;



    /**
     * 是否平过分 1:是 0:否
     * */
    private Integer evaluation;

    /**
     * 是否有截取视频
     * */
    private Integer hasVideoUrl;

    /**
     * 视频mp4
     * */
    private String videoUrl;


}
