package cn.shencom.model.dto.resp;

import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import lombok.*;

import java.io.Serializable;

/**
 * 监控事件DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.AiotEvent
 * @see cn.shencom.model.dto.resp.AiotEventRespDTO
 * @since 2025-06-10
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@ExcelTarget("AiotEventMobileRespDTO")
public class AiotEventMobileRespDTO implements Serializable {
    private String id;

    private String eventNo;

    private String projectId;

    private String projectName;

    private String projectAddress;

    private String pics;

    private java.util.Date eventAt;

    private String typeName;

    private String sceneCode;

}
