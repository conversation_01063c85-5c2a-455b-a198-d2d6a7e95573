package cn.shencom.model.dto.resp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import lombok.*;

import java.io.Serializable;

/**
 * 小散工程-套餐DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.XsgcSubscription
 * @since 2025-06-25
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@ExcelTarget("XsgcSubscriptionRespDTO")
public class XsgcSubscriptionRespDTO implements Serializable {

    /**
     * id
     */
    @Excel(name = "id", width = 25)
    private String id;

    @Excel(name = "套餐名称", width = 25)
    private String name;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 25)
    private java.util.Date createdAt;

    /**
     * 更新时间
     */
    @Excel(name = "更新时间", width = 25)
    private java.util.Date updatedAt;


    /**
     * 创建人
     */
    @Excel(name = "创建人", width = 25)
    private String createdUserName;


    /**
     * 修改人
     */
    @Excel(name = "修改人", width = 25)
    private String updatedUser;

    /**
     * 套餐说明
     */
    @Excel(name = "套餐说明", width = 25)
    private String memo;

    /**
     * 套餐状态, 0-无效， 1-有效
     */
    @Excel(name = "套餐状态, 0-无效， 1-有效", width = 25)
    private Integer status;

    /**
     * 套餐类型, 0-低级套餐， 1-高级套餐
     */
    @Excel(name = "套餐类型, 0-低级套餐， 1-高级套餐", width = 25)
    private Integer type;

}
