package cn.shencom.model.dto.resp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import cn.shencom.model.ComRegion;
import cn.shencom.scloud.common.annotation.EsEntity;
import cn.shencom.scloud.common.annotation.EsKeyword;
import cn.shencom.scloud.common.annotation.EsLocalTime;
import cn.shencom.scloud.common.annotation.EsProperty;
import cn.shencom.scloud.common.jpa.scpage.ScLink;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 事件工单表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.EventOrder
 * @since 2021-06-29
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@ExcelTarget("EventOrderRespDTO")
@EsEntity(index = "event_order", format = "yyyyMM")
public class ESEventOrderRespDTO implements Serializable {

    @Excel(name = "工单ID", width = 25)
    private String id;

    @Excel(name = "辖区id", width = 25)
    private String regionPid;

    @Excel(name = "街道id", width = 25)
    private String regionId;

    @Excel(name = "社区id", width = 25)
    private String regionCid;

    @Excel(name = "小区id", width = 25)
    private String communityId;

    @Excel(name = "投放点id", width = 25)
    private String poiId;

    @Excel(name = "摄像头编号", width = 25,orderNum = "13")
    @EsProperty(type = "text",keyword = @EsKeyword, match = "wildcard")
    @Field(type = FieldType.Keyword)
    private String cameraNo;

    @Excel(name = "事件编号", width = 25,orderNum = "1")
    @EsProperty(type = "text",keyword = @EsKeyword, match = "wildcard")
    @Field(type = FieldType.Keyword)
    private String eventCode;

    /**
     * 事件来源 1-海康 2-海康服务器 3-鲲云 4-深传
     */
    private Integer eventSource;

    @Excel(name = "事件描述", width = 25,orderNum = "6")
    private String eventDesc;

    @Excel(name = "图片url列表", width = 25,orderNum = "14")
    private String pic;

    @Excel(name = "处理事件时的实时截图", width = 25)
    private String disposingPic;

    @Excel(name = "违规类型编号", width = 25)
    private String typeCode;

    @Excel(name = "复核状态，1-重新处理，2-识别有误", width = 25)
    private Integer reviewStatus;

    @Excel(name = "违规类型名称", width = 25,orderNum = "2")
    private String typeName;

    @Excel(name = "持续时间/秒", width = 25,orderNum = "16")
    private Integer durationTime;

    @Excel(name = "持续时间字符串", width = 25,orderNum = "16")
    private String durationTimeStr;

    @Excel(name = "所属运营公司id", width = 25)
    private String companyId;

    @Excel(name = "来源，1-智能分析", width = 25,orderNum = "4",replace = {"智能分析_1","_null"})
    private Integer origin;

    @Excel(name = "处理人id", width = 25)
    private String memberId;

    @Excel(name = "备注", width = 25)
    private String memo;

    @Excel(name = "处理意见", width = 25,orderNum = "23")
    private String disposeDesc;

    @Excel(name = "处理图片id列表", width = 25)
    private String disposePic;

    @Excel(name = "处理时间", width = 25,exportFormat = "yyyy-MM-dd HH:mm:ss",orderNum = "23")
    @EsLocalTime
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @Field(type = FieldType.Date,format = DateFormat.custom,pattern = "yyyy-MM-dd HH:mm:ss.S||epoch_millis")
    private java.util.Date disposedAt;

    @Excel(name = "事件时间", width = 25,exportFormat = "yyyy-MM-dd HH:mm:ss",orderNum = "15")
    @EsLocalTime
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @Field(type = FieldType.Date,format = DateFormat.custom,pattern = "yyyy-MM-dd HH:mm:ss.S||epoch_millis")
    private java.util.Date eventedAt;

    @Excel(name = "创建时间", width = 25,exportFormat = "yyyy-MM-dd HH:mm:ss")
    @EsLocalTime
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @Field( type = FieldType.Date,format = DateFormat.custom,pattern = "yyyy-MM-dd HH:mm:ss.S||epoch_millis")
    private java.util.Date createdAt;

    @Excel(name = "更新时间", width = 25,exportFormat = "yyyy-MM-dd HH:mm:ss")
    @EsLocalTime
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @Field(name = "updated_at",type = FieldType.Date,format = DateFormat.custom,pattern = "yyyy-MM-dd HH:mm:ss.S||epoch_millis")
    private java.util.Date updatedAt;

    @Excel(name = "处理人名称", width = 25,orderNum = "20")
    private String realname;
    @Field(name = "dispose_name")
    private String disposeName;

    @Excel(name = "辖区", width = 25,orderNum = "7")
    @ScLink(objName = "district", filed = "title")
    private String districtName;

    @Excel(name = "街道", width = 25,orderNum = "8")
    private String streetName;

    @Excel(name = "社区", width = 25,orderNum = "9")
    private String villageName;

    @Excel(name = "小区", width = 25,orderNum = "10")
    private String communityName;

    @Excel(name = "投放点", width = 25,orderNum = "11")
    private String pointName;

    @Excel(name = "投放点详细地址", width = 25,orderNum = "12")
    private String pointAddress;

    @Excel(name = "运营公司名称", width = 25)
    private String companyName;

    @Excel(name = "状态，0-待处理，1-已处理，2-超时自动结束， 3-待复核， 4-已复核", width = 25,replace = {"待处理_0","已处理_1","超时自动结束_2","待复核_3","已复核_4"},orderNum = "22")
    private Integer status;

    @Excel(name = "用户id", width = 25)
    private String userId;

    @Excel(name = "处理类型", width = 25)
    private Integer disposeType;

    private Integer isAssign;

    private String assignCompanyId;

    private String assignUserId;

    private java.util.Date reviewedAt;

    private java.util.Date assignedAt;

    private String memberNames;

    @Excel(name = "分派的运营公司名称", width = 25)
    private String assignCompanyName;

    @Excel(name = "超级管理员头像", width = 25)
    private String userAvatar;

    @Excel(name = "超级管理员名称", width = 25)
    private String userRealname;

    @Excel(name = "处理人头像", width = 25)
    private String memberAvatar;

    @Excel(name = "违规图片范围", width = 25)
    private String violationAreas;

    @Excel(name = "复核人", width = 25)
    private String reviewerRealname;

    private String reviewerId;

    @Excel(name = "场所类型", width = 25)
    private String placeType;

    @Excel(name = "场所类型", width = 25)
    private String placeTypeVal;

    @Excel(name = "是否违规溯源，0-否，1-是", width = 25)
    private Integer isRuleSource;

    @Excel(name = "违规溯源图片", width = 25)
    private String sourcePic;

    @Excel(name = "违规溯源视频", width = 25)
    private String sourceVideo;

    @Excel(name = "超时情况 1=一级处理人超时 2=二级处理人超时 3=三级处理人超时", width = 25,replace = {"一级处理人超时_1","二级处理人超时_2","三级处理人超时_3"},orderNum = "18")
    private String timeoutCase;

    @Excel(name = "是否及时处理" , replace = {"否_0", "是_1"}, width = 25,orderNum = "22")
    private Integer isOnTime;

    private Integer cameraType;

    /**
     * 工单状态
     */
    private String finishStatus;

    /**
     * AI场景
     */
    @Excel(name = "AI场景", width = 25,orderNum = "3")
    private String sceneName;

    @Excel(name = "事件来源", width = 25,orderNum = "5")
    private String eventSourceStr;

    /**
     * 相差 h
     */
    private BigDecimal diffHours;


    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "region_pid", insertable = false, updatable = false)
    @NotFound(action = NotFoundAction.IGNORE)
    @JSONField(serialize = false)
    private ComRegion district;

    /**
     * 评价分数
     */
    private Double score;

    /**
     * 评价
     */
    private String mark;

    /**
     * 是否平过分 1:是 0:否
     * */
    private Integer evaluation;

    /**
     * 事件识别是否准确 (0-否、1-是)
     */
    @Excel(name = "准确性" , replace = {"事件识别不准确_0", "事件识别准确_1"}, width = 25, orderNum = "24")
    private Integer recognizeInexactly;

}
