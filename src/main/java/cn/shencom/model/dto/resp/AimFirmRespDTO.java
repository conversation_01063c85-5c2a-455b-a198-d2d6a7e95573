package cn.shencom.model.dto.resp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import lombok.*;

import java.io.Serializable;

/**
 * 人工智能管理厂商表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.AimFirm
 * @since 2022-08-02
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@ExcelTarget("AimFirmRespDTO")
public class AimFirmRespDTO implements Serializable {

    /**
     * 
     */
    @Excel(name = "", width = 25)
    private String id;

    /**
     * 厂商名称
     */
    @Excel(name = "厂商名称", width = 25)
    private String firmName;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 25)
    private java.util.Date createdAt;

    /**
     * 更新时间
     */
    @Excel(name = "更新时间", width = 25)
    private java.util.Date updatedAt;

}
