package cn.shencom.model.dto.resp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import lombok.*;

import java.io.Serializable;

/**
 * 小散工程-监管工单流程-监控接入详情DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.MonitorAccessInfo
 * @since 2025-07-05
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@ExcelTarget("MonitorAccessInfoRespDTO")
public class MonitorAccessInfoRespDTO implements Serializable {

    /**
     * id
     */
    @Excel(name = "id", width = 25)
    private String id;

    /**
     * 工单id，关联monitor_order
     */
    @Excel(name = "工单id，关联monitor_order", width = 25)
    private String orderId;

    /**
     * 设备id，关联event_camera_point_device表
     */
    @Excel(name = "设备id，关联event_camera_point_device表", width = 25)
    private String deviceId;

    /**
     * 监控序列号
     */
    @Excel(name = "监控序列号", width = 25)
    private String serialNo;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 25)
    private java.util.Date createdAt;

    /**
     * 更新时间
     */
    @Excel(name = "更新时间", width = 25)
    private java.util.Date updatedAt;

    /**
     * 创建人
     */
    @Excel(name = "创建人", width = 25)
    private String createdUser;

    /**
     * 修改人
     */
    @Excel(name = "修改人", width = 25)
    private String updatedUser;

}
