package cn.shencom.model.dto;

import lombok.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * AI场景关联厂商场景DTO
 *
 * <AUTHOR>
 * @since 2022-07-26
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class AimFirmRelevanceSceneDTO implements Serializable {

    /**
     * 厂商id
     */
    @NotBlank(message = "厂商id不能为空")
    private String firmId;

    /**
     * ai场景id
     */
    @NotEmpty(message = "AI场景id不能为空")
    private List<String> sceneIds;

}
