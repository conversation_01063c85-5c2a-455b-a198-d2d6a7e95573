package cn.shencom.model.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/12/4 16:36
 */
@Data
@Accessors(chain = true)
public class ExApiDeviceStatusResp implements Serializable {
    private String id;
    /**
     * 设备编码
     */
    private String deviceCode;

    /**
     * 通道编码
     */
    private String peripheryCode;

    /**
     * 状态 0:离线 1:在线
     * */
    private Integer status;

    private String deviceId;

    /**
     * 类型
     * event_camera_point 1
     * open_ai_camera 2
     */
    private Integer type;
}
