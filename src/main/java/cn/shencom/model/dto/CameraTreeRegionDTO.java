package cn.shencom.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 摄像头数据 - 树桩结构
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class CameraTreeRegionDTO {

    /**
     * id
     */
    private String id;

    /**
     * 公司id
     */
    private String companyId;


    /**
     * 名称
     */
    private String name;

    /**
     * 小散项目名称
     */
    private String projectName;

    /**
     * 名称
     */
    private String modelNo;

    /**
     * 类型，1-自建，2-环卫云，3-车载监控，4-萤石云，5-车载视频API, 6-锐明，7-海康车载视频API
     */
    private Integer type;

    /**
     * 车辆id
     */
    private String vehicleId;

    /**
     * 车辆状态(1:正常,0:停用)
     */
    private Integer vehicleState;

    /**
     * 通道号
     */
    private String channel;

    /**
     * 基地id
     */
    private String stationId;

    /**
     * 再生资源场所id
     */
    private String rebirthPointId;

    //================================下方属性选到投放点才存在==========================

    /**
     * flv直播地址
     */
    private String flvAddress;

    /**
     * flv高清直播地址
     */
    private String hdFlvAddress;

    /**
     * 自建摄像头直播地址
     */
    private String url;

    /**
     * 设备号
     */
    private String monitorNo;

    /**
     * 设备名稱
     */
    private String monitorName;

    /**
     * 是否开启直播，0-未开启，1-开启
     */
    private Integer openLive;

    /**
     * 作用类型：1-全景监控,2-厨余破袋
     */
    private Integer effectType;

    /**
     * 是否是叶子（可展开），true-是，false-否
     */
    private Boolean isLeaf;

    /**
     * 辖区
     */
    private String regionPid;

    /**
     * 街道
     */
    private String regionId;

    /**
     * 社区
     */
    private String regionCid;

    /**
     * 小散项目ID
     */
    private String projectId;

    /**
     * 在线状态，0-离线，1-在线，2-放空
     */
    private Integer status;

    /**
     * 序列号
     */
    private String serialNo;

    /**
     * 是否锁定 0-正常 1-锁定
     */
    private Integer isLock;

    private Integer districtSort = 0;

    public CameraTreeRegionDTO(String id, String name) {
        this.id = id;
        this.name = name;
        children = new ArrayList<>();
    }

    public CameraTreeRegionDTO(String id, String name, String stationId) {
        this.id = id;
        this.name = name;
        this.stationId = stationId;
        children = new ArrayList<>();
    }

    public CameraTreeRegionDTO(String flvAddress, String hdFlvAddress, Integer openLive) {
        this.flvAddress = flvAddress;
        this.hdFlvAddress = hdFlvAddress;
        this.openLive = openLive;
    }

    public CameraTreeRegionDTO(String id, String name, String monitorNo, Integer openLive, Integer type, String modelNo) {
        this.id = id;
        this.name = name;
        this.monitorNo = monitorNo;
        this.openLive = openLive;
        this.type = type;
        this.modelNo = modelNo;
        children = new ArrayList<>();
    }

    public CameraTreeRegionDTO(String monitorNo, Integer openLive, Integer type, String modelNo) {
        this.monitorNo = monitorNo;
        this.openLive = openLive;
        this.type = type;
        this.modelNo = modelNo;
        children = new ArrayList<>();
    }

    public CameraTreeRegionDTO(String monitorNo, Integer openLive, Integer type, String modelNo, String flvAddress, String hdFlvAddress, Integer effectType) {
        this.monitorNo = monitorNo;
        this.openLive = openLive;
        this.type = type;
        this.modelNo = modelNo;
        this.flvAddress = flvAddress;
        this.hdFlvAddress = hdFlvAddress;
        this.effectType = effectType;
    }

    public CameraTreeRegionDTO(String monitorNo, Integer openLive, Integer type, String modelNo, String flvAddress, String hdFlvAddress, Integer effectType, String channel) {
        this.monitorNo = monitorNo;
        this.openLive = openLive;
        this.type = type;
        this.modelNo = modelNo;
        this.flvAddress = flvAddress;
        this.hdFlvAddress = hdFlvAddress;
        this.effectType = effectType;
        this.channel = channel;
    }


    public CameraTreeRegionDTO(String monitorNo, Integer openLive, Integer type, String modelNo, String url, Integer effectType) {
        this.monitorNo = monitorNo;
        this.openLive = openLive;
        this.type = type;
        this.modelNo = modelNo;
        this.url = url;
        this.effectType = effectType;
    }

    private List<CameraTreeRegionDTO> children;
}
