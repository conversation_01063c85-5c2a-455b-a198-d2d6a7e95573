package cn.shencom.model.dto.create;

import lombok.*;

import java.io.Serializable;

/**
 * 小散工程-消息提醒表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.XsgcMessageRemind
 * @since 2025-07-15
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class XsgcMessageRemindCreateDTO implements Serializable {

    /**
     * 工程id
     */
    private String projectId;

    /**
     * 消息类型，1-预约安装提醒，2-预约回收提醒，3-现场勘察提醒，4-上门安装提醒，5-上门回收提醒，6-已接入监管的提醒，7-结束监管的提醒，8-违规告警的提醒
     */
    private Integer type;

    /**
     * 关联id，1~7是关联monitor_flow的id, 8是关联aiot_event的id
     */
    private String relateId;

    /**
     * 是否已读,0-未读，1-已读
     */
    private Integer status;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 备注信息，json字符串格式
     */
    private String memo;

    /**
     * 用户id
     */
    private String userId;

}
