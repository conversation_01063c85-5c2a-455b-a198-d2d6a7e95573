package cn.shencom.model.dto.create;

import lombok.*;

import java.io.Serializable;

/**
 * aiot场景分类DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.AiotCategory
 * @since 2025-06-10
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class AiotCategoryCreateDTO implements Serializable {

    /**
     * 上级场景分类ID，顶级为0
     */
    private String pId;

    /**
     * 场景分类级别:0-顶级，1-一级
     */
    private Integer level;

    /**
     * 场景分类名称
     */
    private String name;

    /**
     * 场景分类代码
     */
    private String code;

}
