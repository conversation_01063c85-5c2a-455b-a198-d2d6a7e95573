package cn.shencom.model.dto.create;

import lombok.*;

import java.io.Serializable;

/**
 * 小散工程-监管工单流程-上门安装详情DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.MonitorInstall
 * @since 2025-07-04
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class MonitorInstallCreateDTO implements Serializable {

    /**
     * 工单id，关联monitor_order
     */
    private String orderId;

    /**
     * 联系人电话
     */
    private String contactMobile;

    /**
     * 联系人姓名
     */
    private String contactName;

    /**
     * 安装说明
     */
    private String memo;

    /**
     * 现场图片
     */
    private String pic;

    /**
     * 安装时间
     */
    private java.util.Date installTime;

    /**
     * 安装的监控数
     */
    private Integer installCnt;

    /**
     * 创建人
     */
    private String createdUser;

    /**
     * 修改人
     */
    private String updatedUser;

}
