package cn.shencom.model.dto.create;

import lombok.*;

import java.io.Serializable;

/**
 * 小散工程-监管工单流程-上门回收详情DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.MonitorRecycle
 * @since 2025-07-04
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class MonitorRecycleCreateDTO implements Serializable {

    /**
     * 工单id，关联monitor_order
     */
    private String orderId;

    /**
     * 联系人电话
     */
    private String contactMobile;

    /**
     * 联系人姓名
     */
    private String contactName;

    /**
     * 回收说明
     */
    private String memo;

    /**
     * 现场图片
     */
    private String pic;

    /**
     * 回收时间
     */
    private java.util.Date recycleTime;

    /**
     * 回收的监控数
     */
    private Integer recycleCnt;

    /**
     * 创建人
     */
    private String createdUser;

    /**
     * 修改人
     */
    private String updatedUser;

}
