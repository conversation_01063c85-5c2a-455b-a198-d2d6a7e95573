package cn.shencom.model.dto.create;

import lombok.*;

import java.io.Serializable;

/**
 * 摄像头信息表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.EventCameraPoint
 * @since 2025-05-16
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class EventCameraPointCreateDTO implements Serializable {

    /**
     * device_id
     */
    private String deviceId;

    /**
     * 序列号
     */
    private String serialNo;

    /**
     * 设备号
     */
    private String monitorNo;

    /**
     * FLV协议播放地址（流畅）
     */
    private String flvAddress;

    /**
     * FLV协议直播地址（高清）
     */
    private String hdFlvAddress;

    /**
     * 
     */
    private String modelNo;

    /**
     * 小散项目id
     */
    private String projectId;

    /**
     * 是否语音播报，0-不是，1-是
     */
    private Integer voiceBroadcast;
    /**
     * 感知类型
     */
    private String perceptionTypes;

    /**
     * 是否智能，0-不是，1-是
     */
    private Integer smart;

    private Integer type;

    /**
     * 在线状态，0-离线，1-在线
     */
    private Integer status;

    /**
     * 真实状态 0-离线 1-在线
     */
    private Integer realStatus;

    /**
     * 直播状态，0-未使用或直播已关闭，1-使用中，2-已过期，3-直播已暂停
     */
    private Integer openLive;
    /**
     * 国标编码
     */
    private String sipUserId;
    /**
     * 通道号
     */
    private String channel;
    /**
     * 备注
     */
    private String memo;

    /**
     * 在线时间
     */
    private java.util.Date onlineAt;

    /**
     * 物联网卡id
     */
    private String cardId;

    /**
     * 出入库状态：1-已入库，2-已出库
     */
    private Integer stockStatus;

    /**
     * 是否锁定 0-正常 1-锁定
     */
    private Integer isLock;

    /**
     * 是否健康 0-异常 1-健康
     */
    private Integer isHealthy;

    /**
     * 原始序列号
     */
    private String originalSerialNo;

    /**
     * 通道名称
     */
    private String monitorName;

    /**
     * 通道id
     */
    private String channelId;

    /**
     * 真实在线时间
     */
    private java.util.Date realOnlineAt;

}
