package cn.shencom.model.dto.create;

import lombok.*;

import java.io.Serializable;

/**
 * 场景类别DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.AiotSceneMgr
 * @since 2024-08-03
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class AiotSceneMgrCreateDTO implements Serializable {

    /**
     * 父id
     */
    private String pId;

    /**
     * 场景类别编号
     */
    private String sceneCode;

    /**
     * 场景类别名称
     */
    private String sceneName;

    /**
     * 场景类别说明
     */
    private String sceneIllustrate;

    /**
     * 场景类别全称
     */
    private String wholeName;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建人姓名
     */
    private String createdByName;

    /**
     * 修改人
     */
    private String updatedBy;

    /**
     * 修改人姓名
     */
    private String updatedByName;

}
