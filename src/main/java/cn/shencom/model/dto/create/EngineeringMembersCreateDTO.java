package cn.shencom.model.dto.create;

import lombok.*;

import java.io.Serializable;

/**
 * 小散工程-工程人员DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.EngineeringMembers
 * @since 2025-07-03
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class EngineeringMembersCreateDTO implements Serializable {

    /**
     * 用户id
     */
    private String userId;

    /**
     * 成员姓名
     */
    private String realname;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 职位类型
     */
    private Integer type;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 工种名称
     */
    private String workTypeName;

    /**
     * 证书编号
     */
    private String certificateNumber;

    /**
     * 证书有效日期-开始时间
     */
    private java.util.Date certificateStartDate;

    /**
     * 证书有效日期-结束时间
     */
    private java.util.Date certificateEndDate;

    /**
     * 证书图片
     */
    private String certificatePic;

    /**
     * 描述
     */
    private String desc;

    /**
     * 有效状态，0-无效，1-有效
     */
    private Integer status;

}
