package cn.shencom.model.dto.create;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * 事件工单表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.EventOrder
 * @since 2021-06-29
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
public class EventOrderCreateDTO implements Serializable {

    private String regionPid;

    private String regionId;

    private String regionCid;

    private String projectId;

    private String cameraNo;

    private String eventCode;

    /**
     * 事件来源 1-海康 2-海康服务器 3-鲲云 4-深传
     */
    private Integer eventSource;

    private String eventDesc;

    private String pic;

    private String typeCode;

    private String typeName;

    private String violationAreas;

    private Integer durationTime;

    private String durationTimeStr;

    private String companyId;

    private Integer origin;

    private String memberId;

    private String memo;

    private String disposeDesc;

    private String disposePic;

    private java.util.Date disposedAt;

    private java.util.Date eventedAt;

    private java.util.Date status;

    private String userId;

    private Integer disposeType;

    private Integer isAssign;

    private String assignCompanyId;

    private String assignUserId;

    private java.util.Date assignedAt;

    private Integer isRuleSource;

    private String sourcePic;

    private String sourceVideo;

    private String timeoutCase;

    private Integer isOnTime;

    private String phone;
}
