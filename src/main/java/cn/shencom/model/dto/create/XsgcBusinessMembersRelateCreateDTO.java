package cn.shencom.model.dto.create;

import lombok.*;

import java.io.Serializable;

/**
 * 小散工程-业务人员客户关联表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.XsgcBusinessMembersRelate
 * @since 2025-06-25
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class XsgcBusinessMembersRelateCreateDTO implements Serializable {

    /**
     * 客户id
     */
    private String memberId;

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 组织id
     */
    private String organizationId;


}
