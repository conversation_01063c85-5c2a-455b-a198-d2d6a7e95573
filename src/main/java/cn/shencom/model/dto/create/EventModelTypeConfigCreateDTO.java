package cn.shencom.model.dto.create;

import lombok.*;

import java.io.Serializable;

/**
 * 设备型号配置表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.EventModelTypeConfig
 * @since 2025-05-16
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class EventModelTypeConfigCreateDTO implements Serializable {

    /**
     * 深传设备型号
     */
    private String scModelNo;

    /**
     * 海康设备型号
     */
    private String hkModelNo;

    /**
     * 图片,在线图标
     */
    private String pic;

    /**
     * 离线图标
     */
    private String offLinePic;

}
