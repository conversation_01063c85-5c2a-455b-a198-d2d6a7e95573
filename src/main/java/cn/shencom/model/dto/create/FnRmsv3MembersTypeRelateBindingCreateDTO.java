package cn.shencom.model.dto.create;

import lombok.*;

import java.io.Serializable;

/**
 * 小散工程-组织团队成员区域关联关系DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.FnRmsv3MembersTypeRelateBinding
 * @since 2025-06-25
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class FnRmsv3MembersTypeRelateBindingCreateDTO implements Serializable {

    /**
     * fn_rmsv3_members_type_relate id
     */
    private String relateId;

    /**
     * 区
     */
    private String regionPid;

    /**
     * 街道
     */
    private String regionId;

    /**
     * 社区
     */
    private String regionCid;

}
