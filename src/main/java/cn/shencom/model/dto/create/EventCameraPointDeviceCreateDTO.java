package cn.shencom.model.dto.create;

import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * 摄像头表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.EventCameraPointDevice
 * @since 2025-05-16
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class EventCameraPointDeviceCreateDTO implements Serializable {

    /**
     * 序列号
     */
    private String serialNo;
    /**
     * 监控设备型号
     */
    private String modelNo;

    /**
     * 国标视频编码
     */
    private String sipUserId;

    /**
     * 类型 event_camera_point_type
     */
    private Integer type;

    /**
     * 项目id
     */
    private String projectId;
    /**
     * 感知类型
     */
    private String perceptionTypes;
    /**
     * 是否语音播报，0-不是，1-是
     */
    private Integer voiceBroadcast;

    /**
     * 是否健康 0-异常 1-健康
     */
    private Integer isHealthy;

    /**
     * 是否智能，0-不是，1-是
     */
    private Integer smart;

    /**
     * 备注
     */
    private String memo;

    private List<EventCameraPointCreateDTO> channelList;
}
