package cn.shencom.model.dto.create;

import lombok.*;

import java.io.Serializable;

/**
 * 小散工程-工程人员关联项目表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.EngineeringMembersProjectRelate
 * @since 2025-07-03
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class EngineeringMembersProjectRelateCreateDTO implements Serializable {

    /**
     * 成员id，关联engineering_members
     */
    private String memberId;

    /**
     * relateId，engineering_members_relate
     */
    private String relateId;

    /**
     * 组织id,关联xsgc_organization
     */
    private String organizationId;

    /**
     * 项目id,关联sporadic_project
     */
    private String projectId;

}
