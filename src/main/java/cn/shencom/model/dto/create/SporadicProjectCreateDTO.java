package cn.shencom.model.dto.create;

import lombok.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 小散工程表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.SporadicProject
 * @since 2025-05-29
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SporadicProjectCreateDTO implements Serializable {

    /**
     * 工程名称
     */
    @NotBlank
    private String name;


    private String organizationId;

    /**
     * 工程分类ID
     */
    private String catePid;

    /**
     * 工程类别ID
     */
    private String cateId;



    /**
     * 工程分类
     */
    private String pCateName;

    /**
     * 工程类别
     */
    private String cateName;



    /**
     * 工程金额(元)
     */
    @NotNull
    private java.math.BigDecimal amount;

    /**
     * 实际施工面积
     */
    @NotNull
    private java.math.BigDecimal area;

    /**
     * 工程开始时间
     */
    @NotNull
    private java.util.Date startAt;

    /**
     * 工程结束时间
     */
    @NotNull
    private java.util.Date endAt;

    /**
     * 所在区
     */
    @NotBlank
    private String regionPid;

    /**
     * 所在街道
     */
    @NotBlank
    private String regionId;

    /**
     * 所在社区
     */
    @NotBlank
    private String regionCid;

    /**
     * 详细地址
     */
    @NotBlank(message = "地址不能为空!")
    private String address;

    /**
     * 建设单位
     */
    @NotBlank
    private String constructorName;

    /**
     * 建设单位负责人
     */
    @NotBlank
    private String constructorCharger;

    /**
     * 业主电话
     */
    @NotBlank
    private String ownerMobile;

    /**
     * 施工单位ID
     */
    private String contractorId;

    /**
     * 施工单位名称
     */
    @NotNull
    private String contractorName;

    /**
     * 施工单位负责人
     */
    @NotBlank
    private String contractorCharger;

    /**
     * 施工单位负责人电话
     */
    @NotBlank
    private String contractorChargerMobile;

    /**
     * 施工状态:0-未开始,1-施工中,2-已结束
     */
    private Integer status;



    /**
     * 备案编号
     */
    @NotBlank
    private String projectNumber;

    /**
     * 点位id
     */
    @NotBlank(message = "点位id不能为空!")
    private String poiId;

    /**
     * 额外保存经纬度
     */
    private BigDecimal lnt;
    private BigDecimal lat;

}
