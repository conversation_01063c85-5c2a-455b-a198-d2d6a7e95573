package cn.shencom.model.dto.create;

import lombok.*;

import java.io.Serializable;

/**
 * 小散工程-客户信息表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.XsgcCustomerInfo
 * @since 2025-06-25
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class XsgcCustomerInfoCreateDTO implements Serializable {

    /**
     * 客户编码
     */
    private String number;

    /**
     * 客户名称
     */
    private String name;

    /**
     * 当前状态,0无效，1-有效
     */
    private Integer status;

    /**
     * 服务开始日期
     */
    private java.util.Date startDate;

    /**
     * 服务结束日期
     */
    private java.util.Date endDate;

    /**
     * 创建人
     */
    private String createdUser;

    /**
     * 修改人
     */
    private String updatedUser;

    /**
     * 套餐id
     */
    private String optionId;

    /**
     * 组织id
     */
    private String organizationId;

    /**
     * 联系人
     */
    private String contactUser;

    /**
     * 联系人电话
     */
    private String contactPhone;

    /**
     * 联系人邮箱
     */
    private String contactEmail;

    /**
     * 备注
     */
    private String memo;

    /**
     * logo
     */
    private String logo;

    /**
     * 续签标记，0-首次开通,1-续签,2-临时续签
     */
    private Integer renewalMark;

}
