package cn.shencom.model.dto.create;

import lombok.*;

import java.io.Serializable;

/**
 * 事件统计分类配置表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.AiotEventStatistics
 * @since 2025-07-14
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class AiotEventStatisticsCreateDTO implements Serializable {

    /**
     * 场景编码
     */
    private String sceneCode;

    /**
     * 事件次数
     */
    private Integer eventCount;

    /**
     * 事件日期
     */
    private java.util.Date eventDate;

}
