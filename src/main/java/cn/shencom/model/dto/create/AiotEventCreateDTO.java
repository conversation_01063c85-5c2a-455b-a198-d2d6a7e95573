package cn.shencom.model.dto.create;

import lombok.*;

import java.io.Serializable;

/**
 * 监控事件DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.AiotEvent
 * @since 2025-06-10
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class AiotEventCreateDTO implements Serializable {

    /**
     * 区ID
     */
    private String regionPid;

    /**
     * 街道ID
     */
    private String regionId;

    /**
     * 社区ID
     */
    private String regionCid;

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 事件编号
     */
    private String eventNo;

    /**
     * 设备编码(国标视频认证编码ID)
     */
    private String deviceCode;

    /**
     * 通道编码
     */
    private String channelCode;

    /**
     * 摄像头编号
     */
    private String monitorNo;

    /**
     * 场景编码
     */
    private String sceneCode;

    /**
     * 事件图片
     */
    private String pics;

    /**
     * 违规框
     */
    private String violationBox;

    /**
     * 事件时间
     */
    private java.util.Date eventAt;

}
