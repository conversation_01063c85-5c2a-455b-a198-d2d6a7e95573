package cn.shencom.model.dto.create;

import lombok.*;

import java.io.Serializable;

/**
 * 小散工程-监管工单流程-安装预约详情DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.MonitorInstallReservation
 * @since 2025-07-04
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class MonitorInstallReservationCreateDTO implements Serializable {

    /**
     * 工单id，关联monitor_order
     */
    private String orderId;

    /**
     * 联系人电话
     */
    private String contactMobile;

    /**
     * 联系人姓名
     */
    private String contactName;

    /**
     * 预约时间
     */
    private java.util.Date reservationTime;

    /**
     * 创建人
     */
    private String createdUser;

    /**
     * 修改人
     */
    private String updatedUser;

}
