package cn.shencom.model.dto.create;

import lombok.*;

import java.io.Serializable;

/**
 * 小散工程-套餐DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.XsgcSubscription
 * @since 2025-06-25
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class XsgcSubscriptionCreateDTO implements Serializable {

    /**
     * 套餐名称
     */
    private String name;

    /**
     * 创建人
     */
    private String createdUser;

    /**
     * 修改人
     */
    private String updatedUser;

    /**
     * 套餐说明
     */
    private String memo;

    /**
     * 套餐状态, 0-无效， 1-有效
     */
    private Integer status;

    /**
     * 套餐类型, 0-低级套餐， 1-高级套餐
     */
    private Integer type;

}
