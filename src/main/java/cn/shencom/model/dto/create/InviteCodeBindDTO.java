package cn.shencom.model.dto.create;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 邀请码绑定请求DTO
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Data
public class InviteCodeBindDTO {

    /**
     * 邀请码
     */
    @NotBlank(message = "邀请码不能为空")
    private String inviteCode;

    /**
     * 成员姓名
     */
    @NotBlank(message = "成员姓名不能为空")
    private String realname;

    /**
     * 手机号码
     */
    @NotBlank(message = "手机号码不能为空")
    private String mobile;

    /**
     * 职位类型
     * 1-建设方（业主）
     * 2-施工单位负责人
     * 3-施工工人
     */
    @NotNull(message = "职位类型不能为空")
    private Integer type;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 工种名称
     */
    private String workTypeName;

    /**
     * 证书编号
     */
    private String certificateNumber;

    /**
     * 证书有效日期-开始时间
     */
    private java.util.Date certificateStartDate;

    /**
     * 证书有效日期-结束时间
     */
    private java.util.Date certificateEndDate;

    /**
     * 证书图片
     */
    private String certificatePic;

    /**
     * 描述
     */
    private String desc;
}
