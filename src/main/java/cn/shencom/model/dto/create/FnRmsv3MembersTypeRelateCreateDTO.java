package cn.shencom.model.dto.create;

import cn.shencom.log.ops.annotation.LogTag;
import cn.shencom.model.dto.SimpleRegionDTO;
import lombok.*;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * 小散工程-组织团队成员关系DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.FnRmsv3MembersTypeRelate
 * @since 2025-06-25
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class FnRmsv3MembersTypeRelateCreateDTO implements Serializable {

    /**
     * 成员id
     */
    private String memberId;

    /**
     *
     */
    private String typeId;

    /**
     * 是否有效 0-无效 1-有效
     */
    private Integer active;

    /**
     * 照片id
     */
    private String pic;


    /**
     * 多选单选时使用,
     * */
    private Set<String> memberIds;

    /**
     * 0-市级 1-区级 2-街道级 3-社区级
     */
    private Integer level;


    /**
     * 组织id
     */
    private String organizationId;


    private List<SimpleRegionDTO> regionIds;


}
