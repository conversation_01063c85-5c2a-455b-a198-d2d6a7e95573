package cn.shencom.model.dto.create;

import lombok.*;

import java.io.Serializable;

/**
 * AI场景管理DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.AimSceneManagement
 * @since 2022-07-26
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class AimSceneManagementCreateDTO implements Serializable {

    /**
     * 场景名称
     */
    private String name;

    /**
     * 场景编号
     */
    private String code;

    /**
     * 场景类别Id
     */
    private String sceneCategoryId;

    /**
     * 场景类型：0-基本 1-复合
     */
    private Integer type;

    /**
     * 模式：1-现场宣导 2-事件处置（逗号分隔）
     */
    private String mode;

    /**
     * 标签（逗号分隔）
     */
    private String tags;

    /**
     * 图片（逗号分隔）
     */
    private String pics;

    /**
     * 场景说明
     */
    private String directions;

    /**
     * 备注
     */
    private String remark;

    /**
     * 关联厂商场景数
     */
    private Integer firmNum;

}
