package cn.shencom.model.dto.create;

import lombok.*;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 小散工程-组织团队成员表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.FnRmsv3Members
 * @since 2025-06-25
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class FnRmsv3MembersCreateDTO implements Serializable {

    /**
     * 用户id
     */
    private String userId;


    private String realname;


    private String mobile;

    private String organizationId;

}
