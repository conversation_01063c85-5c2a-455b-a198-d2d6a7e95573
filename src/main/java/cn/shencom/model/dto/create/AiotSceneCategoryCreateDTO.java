package cn.shencom.model.dto.create;

import lombok.*;

import java.io.Serializable;

/**
 * aiot_scene_categoryDTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.AiotSceneCategory
 * @since 2025-06-10
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class AiotSceneCategoryCreateDTO implements Serializable {

    /**
     * 场景一级分类代码
     */
    private String cateCode;

    /**
     * 场景代码
     */
    private String sceneCode;

}
