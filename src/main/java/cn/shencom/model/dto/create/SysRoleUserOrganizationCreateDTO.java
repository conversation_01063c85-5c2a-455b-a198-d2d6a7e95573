package cn.shencom.model.dto.create;

import lombok.*;

import java.io.Serializable;

/**
 * 系统角色，权限，组织关联表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.SysRoleUserOrganization
 * @since 2025-06-25
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SysRoleUserOrganizationCreateDTO implements Serializable {

    /**
     * 用户id
     */
    private String userId;

    /**
     * 角色id
     */
    private String roleId;

    /**
     * 组织id
     */
    private String organizationId;

}
