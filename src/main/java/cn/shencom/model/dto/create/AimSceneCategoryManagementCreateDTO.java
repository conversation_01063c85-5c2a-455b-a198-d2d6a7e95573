package cn.shencom.model.dto.create;

import lombok.*;

import java.io.Serializable;

/**
 * 场景类别管理表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.AimSceneCategoryManagement
 * @since 2022-07-26
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class AimSceneCategoryManagementCreateDTO implements Serializable {

    /**
     * 场景类别名称
     */
    private String name;

    /**
     * 场景类别说明
     */
    private String directions;

    /**
     * 备注
     */
    private String remark;

}
