package cn.shencom.model.dto.create;

import lombok.*;

import java.io.Serializable;

/**
 * 小散工程-监管工单DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.MonitorOrder
 * @since 2025-07-04
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class MonitorOrderCreateDTO implements Serializable {

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 组织id
     */
    private String organizationId;

    /**
     * 区id
     */
    private String regionPid;

    /**
     * 街道id
     */
    private String regionId;

    /**
     * 社区id
     */
    private String regionCid;

    /**
     * 当前流程, 0-工程创建，1-安装预约，2-现场勘察，3-上门安装，4-接入监管， 5-施工完成，6-回收预约，7-上门回收， 9-监管结束
     */
    private Integer flow;

    /**
     * 创建人
     */
    private String createdUser;

    /**
     * 修改人
     */
    private String updatedUser;

}
