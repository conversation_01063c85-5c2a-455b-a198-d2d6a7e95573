package cn.shencom.model.dto.create;

import lombok.*;

import java.io.Serializable;

/**
 * 施工单位组织关联表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.ContractingUnitOrganizationRelate
 * @since 2025-07-16
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class ContractingUnitOrganizationRelateCreateDTO implements Serializable {

    /**
     * 施工单位ID
     */
    private String contractingUnitId;

    /**
     * 组织ID
     */
    private String organizationId;

    /**
     * 信用分数，每个组织独立计算
     */
    private java.math.BigDecimal creditScore;

    /**
     * 是否黑名单：0-否，1-是
     */
    private Integer isBlacklist;

    /**
     * 黑名单原因
     */
    private String blacklistReason;

    /**
     * 加入黑名单时间
     */
    private java.util.Date blacklistTime;

    /**
     * 施工单位负责人
     */
    private String leader;

    /**
     * 施工单位负责人电话
     */
    private String leaderMobile;

    /**
     * 创建人
     */
    private String createdUser;

    /**
     * 更新人
     */
    private String updatedUser;

}
