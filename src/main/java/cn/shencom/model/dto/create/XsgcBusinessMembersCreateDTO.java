package cn.shencom.model.dto.create;

import lombok.*;

import java.io.Serializable;

/**
 * 小散工程-业务人员DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.XsgcBusinessMembers
 * @since 2025-06-25
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class XsgcBusinessMembersCreateDTO implements Serializable {

    /**
     * 用户id
     */
    private String userId;

    /**
     * 成员姓名
     */
    private String realname;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 职位类型
     * 1.技术人员
     * 2.商务人员
     * 3.销售人员
     * 4.安装人员
     */
    private Integer type;

    /**
     * 描述
     */
    private String desc;

    /**
     * 有效状态，0-无效，1-有效
     */
    private Integer status;

    /**
     * 图片id列表
     */
    private String pic;

}
