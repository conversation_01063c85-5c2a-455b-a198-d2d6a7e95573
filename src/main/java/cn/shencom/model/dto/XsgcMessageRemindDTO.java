package cn.shencom.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class XsgcMessageRemindDTO implements Serializable {


    private Integer type;

    private String projectId;

    private String flowId;

    private String eventId;

    private Date reservationTime;

}
