package cn.shencom.model.dto.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;



@Data
@NoArgsConstructor
@ExcelTarget("MembersTypeRelateExcelDTO")
public class MembersTypeRelateExcelDTO implements Serializable {

    @Excel(name = "姓名", width = 25)
    private String realname;

    @Excel(name = "手机", width = 25)
    private String phone;

    @Excel(name = "层级(市级/区级/街道级/社区级)", width = 25)
    private String levelName;

    @Excel(name = "区", width = 25)
    private String districtName;

    @Excel(name = "街道", width = 25)
    private String streetName;

    @Excel(name = "社区", width = 25)
    private String communityName;

    /**
     * 失败原因
     */
    @Excel(name = "失败原因", width = 40)
    private String failReason;

    private Integer level;

    /**
     * 所属辖区
     */
    private String regionPid;

    /**
     * 所属街道
     */
    private String regionId;

    /**
     * 所属社区
     */
    private String regionCid;

    /**
     * 用户id
     */
    private String userId;

}
