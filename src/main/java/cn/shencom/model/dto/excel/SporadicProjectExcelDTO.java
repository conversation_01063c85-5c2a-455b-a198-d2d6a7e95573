package cn.shencom.model.dto.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelEntity;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.poi.hpsf.Decimal;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


@Data
@NoArgsConstructor
@ExcelTarget("SporadicProjectExcelDTO")
public class SporadicProjectExcelDTO implements Serializable {

    @Excel(name = "工程名称（必填）", width = 25)
    @NotBlank(message = "工程名称（必填）")
    private String name;

    @Excel(name = "工程分类（必填）", width = 25)
    @NotBlank(message = "工程分类（必填）")
    private String pCateName;

    @Excel(name = "工程类别（必填）", width = 25)
    private String cateName;

    @Excel(name = "工程金额（元）（必填）", width = 25,numFormat = "#.00")
    @NotNull(message = "工程金额（元）（必填）")
    @Digits(integer = 10, fraction = 2,message = "工程金额（元）必须为数字，保留2位小数")
    private BigDecimal amount;

    @Excel(name = "实际施工面积(㎡）（必填）", width = 25,numFormat = "#.00")
    @Digits(integer = 10, fraction = 2,message = "实际施工面积(㎡）（必填）必须为数字，保留2位小数")
    private BigDecimal area;

    @Excel(name = "工程开始时间（必填）", width = 25, format = "yyyy-MM-dd")
    @NotNull(message = "工程开始时间（必填）")
    private Date startAt;

    @NotNull(message = "工程结束时间（必填）")
    @Excel(name = "工程结束时间（必填）", width = 25, format = "yyyy-MM-dd")
    private Date endAt;

    @Excel(name = "所属区（必填）", width = 25)
    @NotBlank(message = "所属区（必填）")
    private String district;

    @Excel(name = "所属街道（必填）", width = 25)
    @NotBlank(message = "所属街道（必填）")
    private String street;

    @Excel(name = "所属社区（必填）", width = 25)
    @NotBlank(message = "所属社区（必填）")
    private String village;

    @Excel(name = "详细地址（必填）", width = 25)
    @NotBlank(message = "详细地址（必填）")
    private String address;

    private BigDecimal lng;
    private BigDecimal lat;

    @Excel(name = "建设单位（必填）", width = 25)
    @NotBlank(message = "建设单位（必填）")
    private String constructorName;

    @Excel(name = "建设单位负责人姓名（必填）", width = 25)
    @NotBlank(message = "建设单位负责人姓名（必填）")
    private String constructorCharger;

    @Excel(name = "业主电话（必填）", width = 25)
    @NotBlank(message = "业主电话（必填）")
    private String ownerMobile;

    @Excel(name = "施工单位（必填）", width = 25)
    @NotBlank(message = "施工单位（必填）")
    private String contractorName;

    @Excel(name = "施工负责人（必填）", width = 25)
    @NotBlank(message = "施工负责人（必填）")
    private String contractorCharger;

    @Excel(name = "施工负责人电话（必填）", width = 25)
    @NotBlank(message = "施工负责人电话（必填）")
    private String contractorChargerMobile;

    @Excel(name = "备案编号（必填）", width = 25)
    @NotBlank(message = "备案编号（必填）")
    private String projectNumber;

    /**
     * 失败原因
     */
    @Excel(name = "失败原因", width = 40)
    private String error;

}
