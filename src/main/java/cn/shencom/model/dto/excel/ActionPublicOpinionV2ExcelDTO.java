package cn.shencom.model.dto.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 舆情处置详情V2DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.ActionPublicOpinionV2
 * @since 2023-11-23
 */
@Data
@Accessors(chain = true)
public class ActionPublicOpinionV2ExcelDTO implements Serializable {

    /**
     *
     */
    private String id;

    @Excel(name = "序号", width = 25)
    private String exNo;
    /**
     * 案件编号
     * 案件编号（转办函号或工单编码等）
     */
    @Excel(name = "案件编号（转办函号或工单编码等）", width = 25)
    private String caseNo;

    /**
     * 案件转办部门监督指挥中心_0; 局信访局_1; 宣发中心_2
     */
    @Excel(name = "案件转办部门", width = 25,replace = {"监督指挥中心_0","局信访局_1","宣发中心_2"})
    private Integer transferDept;

    /**
     * 所属区
     */
    @Excel(name = "所属区", width = 25)
    private String districtName;

    private String regionPid;

    /**
     * 所属街道
     */
    @Excel(name = "所属街道", width = 25)
    private String streetName;
    private String regionId;

    /**
     * 所属社区
     */
    @Excel(name = "所属社区", width = 25)
    private String communityName;

    private String regionCid;

    /**
     * 场所名称
     */
    @Excel(name = "场所名称", width = 25)
    private String placeName;

    private String placeId;
    /**
     * 场所所属大类类型名称
     */
    @Excel(name = "场所类型", width = 25)
    private String placeTypeName;

    private String placeType;

    /**
     * 案件类型
     */
    @Excel(name = "案件类型", width = 25)
    private String caseType;

    /**
     * 案件等级 敏感_0；一般_1
     */
    @Excel(name = "案件等级", width = 25,replace = {"敏感_0","一般_1"})
    private Integer caseLevel;

    /**
     * 案件派发时间
     */
    @Excel(name = "案件派发时间", width = 25)
    private java.util.Date distributionAt;

    /**
     * 案件办结时间
     */
    @Excel(name = "案件办结时间", width = 25)
    private java.util.Date completionAt;

    /**
     * 是否按期整改反馈 是_1、否_0
     */
    @Excel(name = "是否按期整改反馈", width = 25,replace = {"是_1","否_0"})
    private Integer isRectification;

    /**
     * 是否属于市领导批示的重大负面信访舆情案件 是_1、否_0
     */
    @Excel(name = "是否属于市领导批示的重大负面信访舆情案件", width = 25,replace = {"是_1","否_0"})
    private Integer isSentiment;

    /**
     * 案件来源（信访方式、文章类型或工单受理渠道等）
     */
    @Excel(name = "案件来源（信访方式、文章类型或工单受理渠道等）", width = 25)
    private String caseSource;

    /**
     * 案件详情
     */
    @Excel(name = "案件详情（信访情况、诉求工单内容、或网络标题等）", width = 25)
    private String caseDetails;

    /**
     * 案件整改情况
     */
    @Excel(name = "案件整改情况", width = 25)
    private String rectificationSituation;

    /**
     * 创建时间
     */

    private java.util.Date createdAt;
    /**
     * 修改时间
     */
    private java.util.Date updatedAt;


    /**
     * 是否为重点申报社区 是_1  否_0
     *
     * */
    private Integer communityFlag;

    /**
     * 是否是申报社区 是_1  否_0
     * */
    private Integer declarationFlag;

    @Excel(name = "失败原因", width = 25)
    private String error;
}
