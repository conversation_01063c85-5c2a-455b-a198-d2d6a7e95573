package cn.shencom.model.dto;

import lombok.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * AI场景关联厂商场景DTO
 *
 * <AUTHOR>
 * @since 2022-07-26
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class AimSceneRelevanceFirmDTO implements Serializable {

    /**
     * 
     */
    @NotBlank(message = "AI场景不能为空")
    private String sceneId;

    /**
     * 场景名称
     */
    @NotEmpty(message = "厂商id不能为空")
    private List<String> firmIds;

}
