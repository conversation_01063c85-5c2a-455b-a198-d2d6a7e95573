package cn.shencom.model.dto;

import cn.shencom.model.AiotEvent;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.*;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * aiot_eventDTO
 *
 * <AUTHOR>
 * @see AiotEvent
 * @since 2025-06-06
 */
@Data
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class AiotEventPushDTO implements Serializable {


    @JSONField(name = "accountId")
    private String accountId;
    @JSONField(name = "accountName")
    private String accountName;
    @J<PERSON>NField(name = "aiScenes")
    private List<AiScenesDTO> aiScenes;
    @JSONField(name = "analyzeId")
    private String analyzeId;
    @JSONField(name = "analyzeProcess")
    private String analyzeProcess;
    @JSONField(name = "channelCode")
    private String channelCode;
    @J<PERSON>NField(name = "createdAt")
    private Long createdAt;
    @J<PERSON><PERSON>ield(name = "deviceCode")
    private String deviceCode;
    @JSONField(name = "deviceId")
    private String deviceId;
    @JSONField(name = "eventAt")
    private Date eventAt;
    @JSONField(name = "eventNo")
    private String eventNo;
    @JSONField(name = "identifyEcologicManufactorId")
    private String identifyEcologicManufactorId;
    @JSONField(name = "identifyEcologicManufactorName")
    private String identifyEcologicManufactorName;
    @JSONField(name = "monitorNo")
    private String monitorNo;
    @JSONField(name = "pics")
    private String pics;
    @JSONField(name = "regionWholeIds")
    private String regionWholeIds;
    @JSONField(name = "regionWholeTitles")
    private String regionWholeTitles;
    @JSONField(name = "sceneCategories")
    private List<SceneCategoriesDTO> sceneCategories;
    @JSONField(name = "sceneCode")
    private String sceneCode;
    @JSONField(name = "sceneId")
    private String sceneId;
    @JSONField(name = "sceneName")
    private String sceneName;
    @JSONField(name = "tenant")
    private String tenant;
    @JSONField(name = "violationBox")
    private String violationBox;

    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    public static class AiScenesDTO{
        @JSONField(name = "sceneCode")
        private String sceneCode;
        @JSONField(name = "sceneId")
        private String sceneId;
        @JSONField(name = "sceneName")
        private String sceneName;
    }

    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    public static class SceneCategoriesDTO {
        @JSONField(name = "categoryCode")
        private String categoryCode;
        @JSONField(name = "categoryId")
        private String categoryId;
        @JSONField(name = "categoryName")
        private String categoryName;
    }
}
