package cn.shencom.model.dto.update;

import lombok.*;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 厂商场景管理DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.AimFirmSceneManagement
 * @since 2022-07-27
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class AimFirmSceneManagementUpdateDTO implements Serializable {

    /**
     * 
     */
    @NotBlank(message = "id不能为空")
    private String id;

    /**
     * 厂商名称
     */
    private String firmId;

    /**
     * 厂商场景编号
     */
    private String code;

    /**
     * 厂商场景名称
     */
    private String sceneName;

    /**
     * 关联AI场景数
     */
    private Integer sceneNum;

    /**
     * 场景介绍
     */
    private String directions;

    /**
     * 图片（逗号分隔）
     */
    private String pics;

}
