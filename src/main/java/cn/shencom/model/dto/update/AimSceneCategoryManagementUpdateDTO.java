package cn.shencom.model.dto.update;

import lombok.*;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 场景类别管理表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.AimSceneCategoryManagement
 * @since 2022-07-26
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class AimSceneCategoryManagementUpdateDTO implements Serializable {

    /**
     * 
     */
    @NotBlank(message = "id不能为空")
    private String id;

    /**
     * 场景类别名称
     */
    private String name;

    /**
     * 场景类别说明
     */
    private String directions;

    /**
     * 备注
     */
    private String remark;

}
