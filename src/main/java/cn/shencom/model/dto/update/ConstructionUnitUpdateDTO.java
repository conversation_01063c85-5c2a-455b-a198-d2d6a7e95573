package cn.shencom.model.dto.update;

import lombok.*;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 施工单位表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.ConstructionUnit
 * @since 2025-07-14
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class ConstructionUnitUpdateDTO implements Serializable {

    /**
     * 主键ID
     */
    @NotBlank(message = "id不能为空")
    private String id;

    /**
     * 施工单位名称
     */
    private String name;

    /**
     * 管理员用户ID，关联移动端认证的管理员
     */
    private String adminUserId;

    /**
     * 管理员姓名
     */
    private String adminName;

    /**
     * 管理员联系方式
     */
    private String adminMobile;

    /**
     * 状态：0-无效，1-有效
     */
    private Integer status;

    /**
     * 创建人
     */
    private String createdUser;

    /**
     * 更新人
     */
    private String updatedUser;

}
