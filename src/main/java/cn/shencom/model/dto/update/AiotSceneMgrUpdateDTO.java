package cn.shencom.model.dto.update;

import lombok.*;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 场景管理DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.AiotSceneMgr
 * @since 2024-08-03
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class AiotSceneMgrUpdateDTO implements Serializable {

    /**
     * 主键
     */
    @NotBlank(message = "id不能为空")
    private String id;

    /**
     * 父场景id
     */
    private String pId="0";

    /**
     * 场景编号
     */
    private String sceneCode;

    /**
     * 场景名称
     */
    private String sceneName;

    /**
     * 场景说明
     */
    private String sceneIllustrate;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建人姓名
     */
    private String createdByName;

    /**
     * 修改人
     */
    private String updatedBy;

    /**
     * 修改人姓名
     */
    private String updatedByName;

}
