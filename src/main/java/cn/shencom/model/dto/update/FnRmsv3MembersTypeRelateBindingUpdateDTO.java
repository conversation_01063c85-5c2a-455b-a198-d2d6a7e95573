package cn.shencom.model.dto.update;

import lombok.*;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 小散工程-组织团队成员区域关联关系DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.FnRmsv3MembersTypeRelateBinding
 * @since 2025-06-25
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class FnRmsv3MembersTypeRelateBindingUpdateDTO implements Serializable {

    /**
     * 主键id
     */
    @NotBlank(message = "id不能为空")
    private String id;

    /**
     * fn_rmsv3_members_type_relate id
     */
    private String relateId;

    private Integer level;

    /**
     * 区
     */
    private String regionPid;

    /**
     * 街道
     */
    private String regionId;

    /**
     * 社区
     */
    private String regionCid;

}
