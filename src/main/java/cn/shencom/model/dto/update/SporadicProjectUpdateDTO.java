package cn.shencom.model.dto.update;

import lombok.*;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 小散工程表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.SporadicProject
 * @since 2025-05-29
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SporadicProjectUpdateDTO implements Serializable {

    /**
     * 工程ID
     */
    @NotBlank(message = "id不能为空")
    private String id;

    /**
     * 工程名称
     */
    private String name;

    /**
     * 工程分类ID
     */
    private String catePid;

    /**
     * 工程类别ID
     */
    private String cateId;



    /**
     * 工程分类
     */
    private String pCateName;

    /**
     * 工程类别
     */
    private String cateName;




    /**
     * 工程金额(元)
     */
    private java.math.BigDecimal amount;

    /**
     * 实际施工面积
     */
    private java.math.BigDecimal area;

    /**
     * 工程开始时间
     */
    private java.util.Date startAt;

    /**
     * 工程结束时间
     */
    private java.util.Date endAt;

    /**
     * 所在区
     */
    private String regionPid;

    /**
     * 所在街道
     */
    private String regionId;

    /**
     * 所在社区
     */
    private String regionCid;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 建设单位
     */
    private String constructorName;

    /**
     * 建设单位负责人
     */
    private String constructorCharger;

    /**
     * 业主电话
     */
    private String ownerMobile;

    /**
     * 施工单位ID
     */
    private String contractorId;

    /**
     * 施工单位名称
     */
    private String contractorName;

    /**
     * 施工单位负责人
     */
    private String contractorCharger;

    /**
     * 施工单位负责人电话
     */
    private String contractorChargerMobile;

    /**
     * 施工状态:0-未开始,1-施工中,2-已结束
     */
    private Integer status;

    /**
     *  备案编号
     */
    private String projectNumber;

    /**
     * gis点位id
     */
    private String poiId;

    /**
     * 额外保存经纬度
     */
    private BigDecimal lnt;
    private BigDecimal lat;

}
