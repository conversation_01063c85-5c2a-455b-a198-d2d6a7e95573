package cn.shencom.model.dto.update;

import lombok.*;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 人工智能管理厂商表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.AimFirm
 * @since 2022-08-02
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class AimFirmUpdateDTO implements Serializable {

    /**
     * 
     */
    @NotBlank(message = "id不能为空")
    private String id;

    /**
     * 厂商名称
     */
    private String firmName;

}
