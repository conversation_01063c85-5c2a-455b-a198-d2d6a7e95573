package cn.shencom.model.dto.update;


import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * 事件工单表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.EventOrder
 * @since 2021-06-29
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
public class EventOrderUpdateDTO implements Serializable {

    @NotBlank(message = "id不能为空")
    /**
     * 编号
     */
    private String id;

    /**
     * 辖区id
     */
    private String regionPid;

    /**
     * 街道id
     */
    private String regionId;

    /**
     * 社区id
     */
    private String regionCid;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 摄像头编号
     */
    private String cameraNo;

    /**
     * 事件编号
     */
    private String eventCode;

    /**
     * 事件来源 1-海康 2-海康服务器 3-鲲云 4-深传
     */
    /**
     * 事件来源
     */
    private Integer eventSource;
    
    /**
     * 事件描述
     */
    private String eventDesc;

    /**
     * 违规图片范围
     */
    private String violationAreas;

    /**
     * 图片url列表
     */
    private String pic;

    /**
     * 违规类型编号
     */
    private String typeCode;

    /**
     * 违规类型名称
     */
    private String typeName;

    /**
     * 持续时间/秒
     */
    private Integer durationTime;

    /**
     * 复核状态，1-重新处理，2-识别有误
     */
    private Integer reviewStatus;

    /**
     * 持续时间字符串
     */
    private String durationTimeStr;

    /**
     * 所属运营公司id
     */
    private String companyId;

    /**
     * 来源，1-智能分析
     */
    private Integer origin;

    /**
     * 处理人id
     */
    private String memberId;

    /**
     * 备注
     */
    private String memo;

    /**
     * 处理意见
     */
    private String disposeDesc;

    /**
     * 处理图片id列表
     */
    private String disposePic;

    /**
     * 处理时间
     */
    private java.util.Date disposedAt;

    /**
     * 事件时间
     */
    private java.util.Date eventedAt;

    /**
     * 状态，0-待处理，1-已处理
     */
    private java.util.Date status;

    /**
     * 超级管理员处理人id
     */
    private String userId;

    /**
     * 处理人角色类型，1-运营公司人员，2-超级管理员
     */
    private Integer disposeType;

    /**
     * 是否分派，0-否，1-是
     */
    private Integer isAssign;

    /**
     * 最后指派到的运营公司id
     */
    private String assignCompanyId;

    /**
     * 最后指派人id
     */
    private String assignUserId;

    /**
     * 最后指派时间
     */
    private java.util.Date assignedAt;

    /**
     * id列表
     */
    private List<String> ids;

    /**
     * 违规溯源图片
     */
    private String sourcePic;

    /**
     * 违规溯源视频
     */
    private String sourceVideo;

}
