package cn.shencom.model.dto.update;

import lombok.*;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 小散工程分类DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.SporadicProjectCategory
 * @since 2025-05-29
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SporadicProjectCategoryUpdateDTO implements Serializable {

    /**
     * 
     */
    @NotBlank(message = "id不能为空")
    private String id;

    /**
     * 分类名称
     */
    private String name;

    /**
     * 父分类ID
     */
    private String pId;

    /**
     * 状态:0-关闭,1-开启
     */
    private Integer active;

}
