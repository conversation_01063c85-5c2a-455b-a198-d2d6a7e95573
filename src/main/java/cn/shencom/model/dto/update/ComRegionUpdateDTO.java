package cn.shencom.model.dto.update;

import lombok.*;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 区域表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.ComRegion
 * @since 2025-04-19
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class ComRegionUpdateDTO implements Serializable {

    /**
     * 行政区划
     */
    @NotBlank(message = "id不能为空")
    private String id;

    /**
     * 父级ID
     */
    private String pId;

    /**
     * 类型， province, city, district, subdistrict, community, neighborhood, residential community
     */
    private String type;

    /**
     * 名称
     */
    private String title;

    /**
     * 名称拼音
     */
    private String spell;

    /**
     * 名称拼音首字母
     */
    private String spellShort;

    /**
     * 简述
     */
    private String brief;

    /**
     * 区域块点位id
     */
    private String fenceId;

    /**
     * 图片
     */
    private String cover;

    /**
     * 内容
     */
    private String content;

    /**
     * 图片资源编号
     */
    private String resourceId;

    /**
     * 是否开启
     */
    private Integer status;

    /**
     * 父级ID（未使用）
     */
    private String pid;

    /**
     * 排序值
     */
    private Integer sort;

    /**
     * 点位id
     */
    private String poiId;

    /**
     * 经度
     */
    private java.math.BigDecimal longitude;

    /**
     * 纬度
     */
    private java.math.BigDecimal latitude;

    /**
     * 行政区划在高德的id
     */
    private String gdId;

    /**
     * 行政区划在高德的adcode
     */
    private String gdAdcode;

    /**
     * 区id（未使用）
     */
    private String districtId;

    /**
     * 行政区划编码
     */
    private String regionCode;

}
