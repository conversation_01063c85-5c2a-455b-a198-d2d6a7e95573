package cn.shencom.model.dto.update;

import lombok.*;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * aiot_scene_categoryDTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.AiotSceneCategory
 * @since 2025-06-10
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class AiotSceneCategoryUpdateDTO implements Serializable {

    /**
     * 
     */
    @NotBlank(message = "id不能为空")
    private String id;

    /**
     * 场景一级分类代码
     */
    private String cateCode;

    /**
     * 场景代码
     */
    private String sceneCode;

}
