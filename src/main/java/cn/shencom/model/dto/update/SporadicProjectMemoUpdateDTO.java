package cn.shencom.model.dto.update;

import lombok.*;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 小散工程-工程备注表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.SporadicProjectMemo
 * @since 2025-07-18
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SporadicProjectMemoUpdateDTO implements Serializable {

    /**
     * ID
     */
    @NotBlank(message = "id不能为空")
    private String id;

    /**
     * 工程ID
     */
    private String projectId;

    /**
     * 备注内容
     */
    private String content;

    /**
     * 用户id
     */
    private String userId;

}
