package cn.shencom.model.dto.update;

import lombok.*;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * 摄像头表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.EventCameraPointDevice
 * @since 2025-05-16
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class EventCameraPointDeviceUpdateDTO implements Serializable {

    /**
     * 主键id
     */
    @NotBlank(message = "id不能为空")
    private String id;

    /**
     * 序列号
     */
    private String serialNo;

    /**
     * 国标视频编码
     */
    private String sipUserId;
    /**
     * 监控设备型号
     */
    private String modelNo;

    /**
     * 类型 event_camera_point_type
     */
    private Integer type;

    /**
     * 小散项目id
     */
    private String projectId;

    /**
     * 是否语音播报，0-不是，1-是
     */
    private Integer voiceBroadcast;
    /**
     * 感知类型
     */
    private String perceptionTypes;

    /**
     * 是否健康 0-异常 1-健康
     */
    private Integer isHealthy;

    /**
     * 是否智能，0-不是，1-是
     */
    private Integer smart;

    /**
     * 备注
     */
    private String memo;

    private List<EventCameraPointUpdateDTO> channelList;
}
