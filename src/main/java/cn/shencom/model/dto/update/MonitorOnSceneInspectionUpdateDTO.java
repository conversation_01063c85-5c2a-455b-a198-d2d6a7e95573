package cn.shencom.model.dto.update;

import lombok.*;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 小散工程-监管工单流程-现场勘察详情DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.MonitorOnSceneInspection
 * @since 2025-07-04
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class MonitorOnSceneInspectionUpdateDTO implements Serializable {

    /**
     * id
     */
    @NotBlank(message = "id不能为空")
    private String id;

    /**
     * 工单id，关联monitor_order
     */
    private String orderId;

    /**
     * 联系人电话
     */
    private String contactMobile;

    /**
     * 联系人姓名
     */
    private String contactName;

    /**
     * 勘察说明
     */
    private String memo;

    /**
     * 现场图片
     */
    private String pic;

    /**
     * 勘察时间
     */
    private java.util.Date inspectTime;

    /**
     * 评估需要安装的监控数
     */
    private Integer installCnt;

    /**
     * 创建人
     */
    private String createdUser;

    /**
     * 修改人
     */
    private String updatedUser;

    /**
     * 预约时间
     */
    private java.util.Date reservationTime;

}
