package cn.shencom.model.dto.update;

import lombok.*;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 事件统计分类配置表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.AiotEventStatistics
 * @since 2025-07-14
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class AiotEventStatisticsUpdateDTO implements Serializable {

    /**
     * 
     */
    @NotBlank(message = "id不能为空")
    private String id;

    /**
     * 场景编码
     */
    private String sceneCode;

    /**
     * 事件次数
     */
    private Integer eventCount;

    /**
     * 事件日期
     */
    private java.util.Date eventDate;

}
