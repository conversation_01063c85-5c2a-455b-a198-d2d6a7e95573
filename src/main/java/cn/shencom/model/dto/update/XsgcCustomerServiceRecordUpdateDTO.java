package cn.shencom.model.dto.update;

import lombok.*;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 小散工程-客户服务开通记录DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.XsgcCustomerServiceRecord
 * @since 2025-06-25
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class XsgcCustomerServiceRecordUpdateDTO implements Serializable {

    /**
     * id
     */
    @NotBlank(message = "id不能为空")
    private String id;

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 服务开始日期
     */
    private java.util.Date startDate;

    /**
     * 服务结束日期
     */
    private java.util.Date endDate;

    /**
     * 创建人
     */
    private String createdUser;

    /**
     * 修改人
     */
    private String updatedUser;

    /**
     * 套餐id
     */
    private String optionId;

    /**
     * 组织id
     */
    private String organizationId;

    /**
     * 套餐说明
     */
    private String memo;

    /**
     * 续签标记，0-首次开通,1-续签,2-临时续签
     */
    private Integer renewalMark;

}
