package cn.shencom.model.dto.update;

import lombok.*;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * 小散工程-业务人员客户关联表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.XsgcBusinessMembersRelate
 * @since 2025-06-25
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class XsgcBusinessMembersRelateUpdateDTO implements Serializable {

    /**
     * ID
     */
    @NotBlank(message = "id不能为空")
    private String id;

    /**
     * 客户id
     */
    @NotBlank(message = "memberId不能为空")
    private String memberId;

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 组织id
     */
    @NotBlank(message = "organizationId不能为空")
    private String organizationId;



    /**
     * 职位
     * 1	技术人员
     * 2	商务人员
     * 3	销售人员
     * 4	安装人员
     */
    @NotBlank(message = "type不能为空")
    private Integer type;


    private List<XsgcBusinessMembersRelateUpdateDTO> relateList;

}
