package cn.shencom.model.dto.update;

import cn.shencom.model.dto.SimpleRegionDTO;
import lombok.*;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * 小散工程-组织团队成员关系DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.FnRmsv3MembersTypeRelate
 * @since 2025-06-25
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class FnRmsv3MembersTypeRelateUpdateDTO implements Serializable {

    /**
     * id
     */
    @NotBlank(message = "id不能为空")
    private String id;

    /**
     * 成员id
     */
    private String memberId;

    /**
     * 类型id (字典表：fn_rmsv3_members_type)
     */
    private String typeId;

    /**
     * 是否有效 0-无效 1-有效
     */
    private Integer active;


    /**
     * 0-市级 1-区级 2-街道级 3-社区级
     */
    private Integer level;

    /**
     * 照片id
     */
    private String pic;


    private List<SimpleRegionDTO> regionIds;

}
