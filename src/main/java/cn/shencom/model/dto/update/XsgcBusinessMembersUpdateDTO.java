package cn.shencom.model.dto.update;

import lombok.*;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 小散工程-业务人员DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.XsgcBusinessMembers
 * @since 2025-06-25
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class XsgcBusinessMembersUpdateDTO implements Serializable {

    /**
     * ID
     */
    @NotBlank(message = "id不能为空")
    private String id;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 成员姓名
     */
    private String realname;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 职位类型
     */
    private Integer type;

    /**
     * 描述
     */
    private String desc;

    /**
     * 有效状态，0-无效，1-有效
     */
    private Integer status;

    /**
     * 图片id列表
     */
    private String pic;

}
