package cn.shencom.model.dto.update;

import lombok.*;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * aiot场景分类DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.AiotScene
 * @since 2025-06-10
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class AiotSceneUpdateDTO implements Serializable {

    /**
     * 
     */
    @NotBlank(message = "id不能为空")
    private String id;

    /**
     * 场景名称
     */
    private String name;

    /**
     * 场景代码
     */
    private String code;

}
