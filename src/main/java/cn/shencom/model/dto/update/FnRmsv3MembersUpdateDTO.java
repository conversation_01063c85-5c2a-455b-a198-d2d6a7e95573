package cn.shencom.model.dto.update;

import cn.shencom.model.dto.SimpleRegionDTO;
import lombok.*;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * 小散工程-组织团队成员表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.FnRmsv3Members
 * @since 2025-06-25
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class FnRmsv3MembersUpdateDTO implements Serializable {

    /**
     * id
     */
    @NotBlank(message = "id不能为空")
    private String id;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 组织id
     */
    private String organizationId;


    /**
     * 手机号
     */
    private String mobile;


    /**
     * 姓名
     */
    private String realname;

}
