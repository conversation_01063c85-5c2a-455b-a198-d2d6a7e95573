package cn.shencom.model.dto.update;

import lombok.*;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 小散工程-工程人员关联项目表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.EngineeringMembersProjectRelate
 * @since 2025-07-03
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class EngineeringMembersProjectRelateUpdateDTO implements Serializable {

    /**
     * ID
     */
    @NotBlank(message = "id不能为空")
    private String id;

    /**
     * 成员id，关联engineering_members
     */
    private String memberId;

    /**
     * relateId，engineering_members_relate
     */
    private String relateId;

    /**
     * 组织id,关联xsgc_organization
     */
    private String organizationId;

    /**
     * 项目id,关联sporadic_project
     */
    private String projectId;

}
