package cn.shencom.model.dto.update;

import lombok.*;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 设备型号配置表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.EventModelTypeConfig
 * @since 2025-05-16
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class EventModelTypeConfigUpdateDTO implements Serializable {

    /**
     * 
     */
    @NotBlank(message = "id不能为空")
    private String id;

    /**
     * 深传设备型号
     */
    private String scModelNo;

    /**
     * 海康设备型号
     */
    private String hkModelNo;

    /**
     * 图片,在线图标
     */
    private String pic;

    /**
     * 离线图标
     */
    private String offLinePic;

}
