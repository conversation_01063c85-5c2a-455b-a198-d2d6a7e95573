package cn.shencom.model.dto.update;

import lombok.*;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 用户表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.SysUsers
 * @since 2025-04-20
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SysUsersUpdateDTO implements Serializable {

    /**
     * 
     */
    @NotBlank(message = "id不能为空")
    private String id;

    /**
     * 用户登录名
     */
    private String username;

    /**
     * 用户密码
     */
    private String password;

    /**
     * 用户屏显昵称，可以不同用户登录名
     */
    private String nickname;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 用户真实姓名
     */
    private String realname;

    /**
     * 身份证号
     */
    private String pid;

    /**
     * 身份证证件正面（印有国徽图案、签发机关和有效期限）照片
     */
    private String pidCardThumb1;

    /**
     * 身份证证件反面（印有个人基本信息和照片）照片
     */
    private String pidCardThumb2;

    /**
     * 用户个人图像
     */
    private String avatar;

    /**
     * 手机号码
     */
    private String phone;

    /**
     * 联系地址
     */
    private String address;

    /**
     * 紧急联系人信息
     */
    private String emergencyContact;

    /**
     * 专属客服id，（为0表示其为无专属客服的管理用户）
     */
    private Integer servicerId;

    /**
     * 是否锁定限制用户登录，1锁定,0正常
     */
    private Integer isLock;

    /**
     * 确认码
     */
    private String confirmationCode;

    /**
     * 是否已通过验证 0：未通过 1：通过
     */
    private Integer confirmed;

    /**
     * Laravel 追加的记住令牌
     */
    private String rememberToken;

    /**
     * 备注
     */
    private String info;

    /**
     * 性别：1，男；2，女；3，保密
     */
    private Integer sex;

    /**
     * 工号
     */
    private String jobNumber;

    /**
     * 企业邮箱
     */
    private String enterpriseEmail;

    /**
     * 办公地点
     */
    private String location;

    /**
     * 生日
     */
    private java.util.Date birth;

    /**
     * 个人签名
     */
    private String sign;

    /**
     * 地区
     */
    private String regionId;

    /**
     * 微信二维码
     */
    private String qr;

    /**
     * 用户类型  0.游客 1.后台 2.微信 
     */
    private Integer type;

    /**
     * 实名认证 0.未审核 1.通过 2.未通过 3.取消
     */
    private Integer verified;

    /**
     * 申请实名认证时间
     */
    private java.util.Date verifyTime;

    /**
     * 认证时间
     */
    private java.util.Date verifiedAt;

    /**
     * 审核意见
     */
    private String remark;

    /**
     * 最后一次访问时间
     */
    private java.util.Date lastTime;

    /**
     * 最后一次访问ip
     */
    private String lastIp;

    /**
     * 登录次数
     */
    private Integer loginTimes;

}
