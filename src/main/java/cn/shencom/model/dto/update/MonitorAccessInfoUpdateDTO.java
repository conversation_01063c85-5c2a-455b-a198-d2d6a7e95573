package cn.shencom.model.dto.update;

import lombok.*;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 小散工程-监管工单流程-监控接入详情DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.MonitorAccessInfo
 * @since 2025-07-05
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class MonitorAccessInfoUpdateDTO implements Serializable {

    /**
     * id
     */
    @NotBlank(message = "id不能为空")
    private String id;

    /**
     * 工单id，关联monitor_order
     */
    private String orderId;

    /**
     * 设备id，关联event_camera_point_device表
     */
    private String deviceId;

    /**
     * 监控序列号
     */
    private String serialNo;

    /**
     * 创建人
     */
    private String createdUser;

    /**
     * 修改人
     */
    private String updatedUser;

}
