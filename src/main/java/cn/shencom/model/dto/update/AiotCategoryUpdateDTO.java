package cn.shencom.model.dto.update;

import lombok.*;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * aiot场景分类DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.AiotCategory
 * @since 2025-06-10
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class AiotCategoryUpdateDTO implements Serializable {

    /**
     * 
     */
    @NotBlank(message = "id不能为空")
    private String id;

    /**
     * 上级场景分类ID，顶级为0
     */
    private String pId;

    /**
     * 场景分类级别:0-顶级，1-一级
     */
    private Integer level;

    /**
     * 场景分类名称
     */
    private String name;

    /**
     * 场景分类代码
     */
    private String code;

}
