package cn.shencom.model.dto.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.shencom.log.ops.annotation.LogTag;
import cn.shencom.scloud.common.base.dto.BaseTree;
import cn.shencom.scloud.common.jpa.scpage.ScLink;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;
import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
@ToString
public class SysMenuVO extends BaseTree<SysMenuVO> implements Serializable {
//===========================数据库字段================================

    private static final long serialVersionUID = -1L;

    @LogTag(alias = "编号")
    private String id;
    /**
     * 菜单标识
     */
    @Excel(name = "菜单标识", width = 25)
    @LogTag(alias = "菜单标识")
    private String name;
    /**
     * 菜单展示名称
     */
    @Excel(name = "菜单展示名称", width = 25)
    @LogTag(alias = "菜单展示名称")
    private String displayName;
    /**
     * 图标
     */
    @Excel(name = "图标", width = 25)
    @LogTag(alias = "图标")
    private String icon;
    /**
     * 父节点编号
     */
    @Excel(name = "菜单父id", width = 25)
    @LogTag(alias = "父节点编号")
    private String pid;
    /**
     * 排序
     */
    @Excel(name = "排序", width = 25)
    @LogTag(alias = "排序")
    private Integer sort;
    /**
     * 路由
     */
    @Excel(name = "路由", width = 25)
    @LogTag(alias = "路由")
    private String route;
    /**
     * 备注
     */
    @Excel(name = "备注", width = 25)
    @LogTag(alias = "备注")
    private String memo;
    /**
     * 状态(1:正常,0:停用)
     */
    @Excel(name = "状态(1:正常,0:停用)", width = 25)
    @LogTag(alias = "状态")
    private Integer active;
    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 25)
    @Temporal(TemporalType.TIMESTAMP)
    @LogTag(alias = "创建时间")
    private java.util.Date createdAt;
    /**
     * 删除时间
     */
    @Excel(name = "删除时间", width = 25)
    @Temporal(TemporalType.TIMESTAMP)
    @LogTag(alias = "删除时间")
    private java.util.Date deletedAt;
    /**
     * 修改时间
     */
    @Excel(name = "修改时间", width = 25)
    @Temporal(TemporalType.TIMESTAMP)
    @LogTag(alias = "修改时间")
    private java.util.Date updatedAt;
    /**
     * URL
     */
    @Excel(name = "URL", width = 25)
    @LogTag(alias = "URL")
    private String url;
    /**
     * 菜单版本
     */
    @Excel(name = "菜单版本", width = 25)
    @LogTag(alias = "version")
    private String version;
    /**
     * 是否现在在菜单列表中
     */
    @Excel(name = "是否现在在菜单列表中", width = 25)
    @LogTag(alias = "sidebar")
    private Integer sidebar;
    /**
     * 激活图标
     */
    @Excel(name = "激活图标", width = 25)
    @LogTag(alias = "active_icon")
    private String activeIcon;
    /**
     * 扩展字段
     */
    @Excel(name = "扩展字段", width = 25)
    @LogTag(alias = "payload")
    private String payload;

    //===========================自定义字段=================================
    @Transient
    @JSONField(serialize = false)
    @ScLink(objName = "permissions", filed = "id")
    private String permissionId;

    @Transient
    private int num;

    @Transient
    @ScLink(objName = "parent", filed = "displayName")
    @Excel(name = "父级菜单", width = 25)
    private String parentDisplayName;

    @Transient
    @JSONField(serialize = false)
    private String userId;

}
