package cn.shencom.model;

import cn.shencom.scloud.common.base.snowflake.SnowflakeGenerator;
import cn.shencom.scloud.common.jpa.scpage.ScLink;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.*;
import org.hibernate.annotations.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.List;

/**
 * 小散工程-组织团队成员关系
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Entity
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@DynamicUpdate
@DynamicInsert
@Table(name = "fn_rmsv3_members_type_relate")
@Where(clause = "is_deleted = 0")
@EntityListeners(AuditingEntityListener.class)
public class FnRmsv3MembersTypeRelate implements Serializable {
//===========================数据库字段================================
    /**
     * id
     */
    @Id
    @Column(name = "id")
    @GenericGenerator(name = "snowflake", strategy = SnowflakeGenerator.TYPE)
    @GeneratedValue(generator = "snowflake")
    private String id;

    /**
     * 成员id
     */
    @Column(name = "member_id")
    private String memberId;

    /**
     * 类型id (字典表：fn_rmsv3_members_type)
     */
    @Column(name = "type_id")
    private String typeId;


    /**
     * 0-市级 1-区级 2-街道级 3-社区级
     */
    @Column(name = "level")
    private Integer level;

    /**
     * 创建时间
     */
    @Column(name = "created_at")
    @CreatedDate
    private java.util.Date createdAt;

    /**
     * 修改时间
     */
    @Column(name = "updated_at")
    @LastModifiedDate
    private java.util.Date updatedAt;

    /**
     * 是否删除
     */
    @Column(name = "is_deleted")
    private Integer isDeleted;

    /**
     * 是否有效 0-无效 1-有效
     */
    @Column(name = "active")
    private Integer active;

    /**
     * 照片id
     */
    @Column(name = "pic")
    private String pic;

//=============================表关联==================================
    /**
     * 团队成员
     */
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(
            name = "member_id", referencedColumnName = "id", insertable = false, updatable = false,
            foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT)
    )
    @NotFound(action = NotFoundAction.IGNORE)
    @JSONField(serialize = false)
    private FnRmsv3Members fnRmsv3Members;


    @OneToMany(fetch = FetchType.LAZY)
    @JoinColumn(
            name = "relate_id", referencedColumnName = "id", insertable = false, updatable = false,
            foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT)
    )
    @NotFound(action = NotFoundAction.IGNORE)
    @JSONField(serialize = false)
    private List<FnRmsv3MembersTypeRelateBinding> fnRmsv3MembersTypeRelateBindings;


//===========================自定义字段=================================



    /** 系统用户真实姓名 */
    @ScLink(objName = "fnRmsv3Members", filed = "realname")
    @Transient
    private String realname;

    /** 系统用户昵称 */
    @ScLink(objName = "fnRmsv3Members", filed = "mobile")
    @Transient
    private String mobile;


}
