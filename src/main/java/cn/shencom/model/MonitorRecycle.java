package cn.shencom.model;

import cn.shencom.scloud.common.base.snowflake.SnowflakeGenerator;
import lombok.*;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.GenericGenerator;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 小散工程-监管工单流程-上门回收详情
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@Entity
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@DynamicUpdate
@DynamicInsert
@Table(name = "monitor_recycle")
@EntityListeners(AuditingEntityListener.class)
public class MonitorRecycle implements Serializable {
//===========================数据库字段================================
    /**
     * id
     */
    @Id
    @Column(name = "id")
    @GenericGenerator(name = "snowflake", strategy = SnowflakeGenerator.TYPE)
    @GeneratedValue(generator = "snowflake")
    private String id;

    /**
     * 工单id，关联monitor_order
     */
    @Column(name = "order_id")
    private String orderId;

    /**
     * 联系人电话
     */
    @Column(name = "contact_mobile")
    private String contactMobile;

    /**
     * 联系人姓名
     */
    @Column(name = "contact_name")
    private String contactName;

    /**
     * 回收说明
     */
    @Column(name = "memo")
    private String memo;

    /**
     * 现场图片
     */
    @Column(name = "pic")
    private String pic;

    /**
     * 回收时间
     */
    @Column(name = "recycle_time")
    private java.util.Date recycleTime;

    /**
     * 回收的监控数
     */
    @Column(name = "recycle_cnt")
    private Integer recycleCnt;

    /**
     * 创建时间
     */
    @Column(name = "created_at")
    @CreatedDate
    private java.util.Date createdAt;

    /**
     * 更新时间
     */
    @Column(name = "updated_at")
    @LastModifiedDate
    private java.util.Date updatedAt;

    /**
     * 创建人
     */
    @Column(name = "created_user")
    private String createdUser;

    /**
     * 修改人
     */
    @Column(name = "updated_user")
    private String updatedUser;

//=============================表关联==================================


//===========================自定义字段=================================

}
