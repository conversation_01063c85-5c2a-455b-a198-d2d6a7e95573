package cn.shencom.model;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import cn.shencom.scloud.common.base.snowflake.SnowflakeGenerator;
import cn.shencom.scloud.common.jpa.scpage.ScLink;
import cn.shencom.scloud.common.jpa.util.query.ScBaseBean;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.*;
import java.io.Serializable;
import java.util.List;

/**
 * sys_permissions实体类
 *
 * <AUTHOR>
@Entity
@Table(name = "sys_permissions")
@Getter
@Setter
@NoArgsConstructor
@DynamicUpdate
@ExcelTarget("SysPermissions")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class SysPermissions extends ScBaseBean implements Serializable {
//===========================数据库字段================================
    /**
     * id
     */
    @Id
    @Column(name = "id")
    @GenericGenerator(name = "snowflake", strategy = SnowflakeGenerator.TYPE)
    @GeneratedValue(generator = "snowflake")
    @Excel(name = "id", needMerge = false, width = 25)
    private String id;
    /**
     * 权限名
     */
    @Column(name = "name")
    @Excel(name = "权限名", needMerge = false, width = 25)
    private String name;
    /**
     * 权限展示名
     */
    @Column(name = "display_name")
    @Excel(name = "权限展示名", needMerge = false, width = 25)
    private String displayName;
    /**
     * 详情
     */
    @Column(name = "description")
    @Excel(name = "详情", needMerge = false, width = 25)
    private String description;
    /**
     * 创建时间
     */
    @Column(name = "created_at")
    @Excel(name = "创建时间", needMerge = false, width = 25)
    @Temporal(TemporalType.TIMESTAMP)
    private java.util.Date createdAt;
    /**
     * 更新时间
     */
    @Column(name = "updated_at")
    @Excel(name = "更新时间", needMerge = false, width = 25)
    @Temporal(TemporalType.TIMESTAMP)
    private java.util.Date updatedAt;
    /**
     * 删除时间
     */
    @Column(name = "deleted_at")
    @Excel(name = "删除时间", needMerge = false, width = 25)
    @Temporal(TemporalType.TIMESTAMP)
    private java.util.Date deletedAt;
    /**
     * 是否开启
     */
    @Column(name = "active")
    @Excel(name = "是否开启", needMerge = false, width = 25)
    private Integer active;
    //===========================表关联====================================
    @ManyToMany(targetEntity = SysRoles.class,
            fetch = FetchType.LAZY)
    @JoinTable(
            name = "sys_permission_role",
            joinColumns = {@JoinColumn(name = "permission_id", referencedColumnName = "id", insertable = false, updatable = false)},
            inverseJoinColumns = {@JoinColumn(name = "role_id", referencedColumnName = "id", insertable = false, updatable = false)},
            foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT)
    )
    @NotFound(action = NotFoundAction.IGNORE)
    @JSONField(serialize = false)
    private List<SysRoles> roles;

    @ManyToMany(targetEntity = SysMenu.class,
            fetch = FetchType.LAZY)
    @JoinTable(
            name = "sys_menu_permission",
            joinColumns = {@JoinColumn(name = "permission_id", referencedColumnName = "id", insertable = false, updatable = false)},
            inverseJoinColumns = {@JoinColumn(name = "menu_id", referencedColumnName = "id", insertable = false, updatable = false)},
            foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT)
    )
    @NotFound(action = NotFoundAction.IGNORE)
    @JSONField(serialize = false)
    private List<SysMenu> menus;

    //===========================自定义字段=================================
    @Transient
    @JSONField(serialize = false)
    @ScLink(objName = "menus", filed = "id")
    private String menuId;
    @Transient
    @JSONField(serialize = false)
    private String nameLike;
    @Transient
    @JSONField(serialize = false)
    @ScLink(objName = "roles", filed = "id")
    private String roleId;
}
