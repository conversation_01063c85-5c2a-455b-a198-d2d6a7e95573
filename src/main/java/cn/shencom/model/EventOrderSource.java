package cn.shencom.model;

import cn.shencom.scloud.common.base.snowflake.SnowflakeGenerator;
import lombok.*;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Where;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 事件来源表
 *
 * <AUTHOR>
 * @since 2023-11-15
 */
@Entity
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@DynamicUpdate
@DynamicInsert
@Table(name = "event_order_source")
@Where(clause = "is_deleted = 0")
@EntityListeners(AuditingEntityListener.class)
public class EventOrderSource implements Serializable {
//===========================数据库字段================================
    /**
     * 主键自增
     */
    @Id
    @Column(name = "id")
    @GenericGenerator(name = "snowflake", strategy = SnowflakeGenerator.TYPE)
    @GeneratedValue(generator = "snowflake")
    private String id;

    /**
     * 事件来源code
     */
    @Column(name = "code")
    private Integer code;

    /**
     * 事件来源名称
     */
    @Column(name = "name")
    private String name;

    /**
     * 是否有效  0-无效 1-有效
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 是否删除 0-未删除 1-已删除
     */
    @Column(name = "is_deleted")
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @Column(name = "created_at")
    @CreatedDate
    private java.util.Date createdAt;

    /**
     * 更新时间
     */
    @Column(name = "updated_at")
    @LastModifiedDate
    private java.util.Date updatedAt;

//=============================表关联==================================


//===========================自定义字段=================================

}
