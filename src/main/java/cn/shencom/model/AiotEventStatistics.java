package cn.shencom.model;

import cn.shencom.scloud.common.base.snowflake.SnowflakeGenerator;
import lombok.*;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Where;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import com.alibaba.fastjson.annotation.JSONField;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 事件统计分类配置表
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Entity
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@DynamicUpdate
@DynamicInsert
@Table(name = "aiot_event_statistics")
@Where(clause = "is_deleted = 0")
@EntityListeners(AuditingEntityListener.class)
public class AiotEventStatistics implements Serializable {
    // ===========================数据库字段================================
    /**
     * 
     */
    @Id
    @Column(name = "id")
    @GenericGenerator(name = "snowflake", strategy = SnowflakeGenerator.TYPE)
    @GeneratedValue(generator = "snowflake")
    private String id;

    /**
     * 场景编码
     */
    @Column(name = "scene_code")
    private String sceneCode;

    /**
     * 项目id
     */
    @Column(name = "project_id")
    private String projectId;

    /**
     * 事件次数
     */
    @Column(name = "event_count")
    private Integer eventCount;

    /**
     * 是否删除:0-否,1-是
     */
    @Column(name = "is_deleted")
    private Integer isDeleted;

    /**
     * 事件日期
     */
    @Column(name = "event_date")
    @JSONField(format = "yyyy-MM-dd")
    private java.util.Date eventDate;

    /**
     * 创建时间
     */
    @Column(name = "created_at")
    @CreatedDate
    private java.util.Date createdAt;

    /**
     * 更新时间
     */
    @Column(name = "updated_at")
    @LastModifiedDate
    private java.util.Date updatedAt;

    /**
     * 删除时间
     */
    @Column(name = "deleted_at")
    private java.util.Date deletedAt;

    // =============================表关联==================================

    // ===========================自定义字段=================================
}
