package cn.shencom.model;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import cn.shencom.scloud.common.base.snowflake.SnowflakeGenerator;
import cn.shencom.scloud.common.jpa.scpage.ScLink;
import cn.shencom.scloud.common.jpa.util.query.ScBaseTreeBean;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.OrderBy;
import org.hibernate.annotations.*;

import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.Table;
import javax.persistence.*;
import java.io.Serializable;
import java.util.List;

/**
 * sys_menu实体类
 *
 * <AUTHOR>
@Entity
@Table(name = "sys_menu")
@Getter
@Setter
@NoArgsConstructor
@DynamicUpdate
@ExcelTarget("sys_menu")
@Where(clause = "deleted_at is null")
public class SysMenu extends ScBaseTreeBean<SysMenu> implements Serializable {
//===========================数据库字段================================

    private static final long serialVersionUID = -1L;
    /**
     * 编号
     */
    @Id
    @Column(name = "id")
    @GenericGenerator(name = "snowflake", strategy = SnowflakeGenerator.TYPE)
    @GeneratedValue(generator = "snowflake")
    @Excel(name = "编号", width = 25)
    private String id;
    /**
     * 菜单标识
     */
    @Column(name = "name")
    @Excel(name = "菜单标识", width = 25)
    private String name;
    /**
     * 菜单展示名称
     */
    @Column(name = "display_name")
    @Excel(name = "菜单展示名称", width = 25)
    private String displayName;
    /**
     * 图标
     */
    @Column(name = "icon")
    @Excel(name = "图标", width = 25)
    private String icon;
    /**
     * 父节点编号
     */
    @Column(name = "parent_id")
    @Excel(name = "菜单父id", width = 25)
    private String pid;
    /**
     * 排序
     */
    @Column(name = "sort")
    @Excel(name = "排序", width = 25)
    private Integer sort;
    /**
     * 路由
     */
    @Column(name = "route")
    @Excel(name = "路由", width = 25)
    private String route;
    /**
     * 备注
     */
    @Column(name = "memo")
    @Excel(name = "备注", width = 25)
    private String memo;
    /**
     * 状态(1:正常,0:停用)
     */
    @Column(name = "active")
    @Excel(name = "状态(1:正常,0:停用)", width = 25)
    private Integer active;
    /**
     * 创建时间
     */
    @Column(name = "created_at")
    @Excel(name = "创建时间", width = 25)
    @Temporal(TemporalType.TIMESTAMP)
    private java.util.Date createdAt;
    /**
     * 删除时间
     */
    @Column(name = "deleted_at")
    @Excel(name = "删除时间", width = 25)
    @Temporal(TemporalType.TIMESTAMP)
    private java.util.Date deletedAt;
    /**
     * 修改时间
     */
    @Column(name = "updated_at")
    @Excel(name = "修改时间", width = 25)
    @Temporal(TemporalType.TIMESTAMP)
    private java.util.Date updatedAt;
    /**
     * URL
     */
    @Column(name = "url")
    @Excel(name = "URL", width = 25)
    private String url;
    /**
     * 菜单版本
     */
    @Column(name = "version")
    @Excel(name = "菜单版本", width = 25)
    private String version;
    /**
     * 是否现在在菜单列表中
     */
    @Column(name = "sidebar")
    @Excel(name = "是否现在在菜单列表中", width = 25)
    private Integer sidebar;
    /**
     * 激活图标
     */
    @Column(name = "active_icon")
    @Excel(name = "激活图标", width = 25)
    private String activeIcon;
    /**
     * 扩展字段
     */
    @Column(name = "payload")
    @Excel(name = "扩展字段", width = 25)
    private String payload;

    //===========================表关联====================================
    @ManyToOne(targetEntity = SysMenu.class, fetch = FetchType.LAZY)
    @JoinColumn(
            name = "parent_id", referencedColumnName = "id", insertable = false, updatable = false,
            foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT)
    )
    @NotFound(action = NotFoundAction.IGNORE)
    @JSONField(serialize = false)
    @Where(clause = "deleted_at is null")
    private SysMenu parent;
    @ManyToMany(targetEntity = SysPermissions.class,
            cascade = CascadeType.REFRESH,
            fetch = FetchType.LAZY)
    @JoinTable(
            name = "sys_menu_permission",
            joinColumns = {@JoinColumn(name = "menu_id", referencedColumnName = "id", insertable = false, updatable = false)},
            inverseJoinColumns = {@JoinColumn(name = "permission_id", referencedColumnName = "id", insertable = false, updatable = false)},
            foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT)
    )
    @JSONField(serialize = false)
    @NotFound(action = NotFoundAction.IGNORE)
    @Where(clause = "deleted_at is null")
    private List<SysPermissions> permissions;

    @OneToMany(targetEntity = SysMenu.class, fetch = FetchType.LAZY)
    @JoinColumn(
            name = "parent_id", referencedColumnName = "id", insertable = false, updatable = false,
            foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT)
    )
    @NotFound(action = NotFoundAction.IGNORE)
    @OrderBy(clause = "sort asc")
    @Where(clause = "deleted_at is null")
    private List<SysMenu> children;
    //===========================自定义字段=================================
    @Transient
    @JSONField(serialize = false)
    @ScLink(objName = "permissions", filed = "id")
    private String permissionId;

    @Transient
    private int num;

    @Transient
    @ScLink(objName = "parent", filed = "displayName")
    @Excel(name = "父级菜单", width = 25)
    private String parentDisplayName;

    @Transient
    @JSONField(serialize = false)
    private String userId;

}
