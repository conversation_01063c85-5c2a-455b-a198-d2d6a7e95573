package cn.shencom.model;

import cn.shencom.scloud.common.base.snowflake.SnowflakeGenerator;
import cn.shencom.scloud.common.jpa.scpage.ScLink;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.*;
import org.hibernate.annotations.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 摄像头信息表
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Entity
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@DynamicUpdate
@DynamicInsert
@Table(name = "event_camera_point")
@Where(clause = "is_deleted = 0")
@EntityListeners(AuditingEntityListener.class)
public class EventCameraPoint implements Serializable {
//===========================数据库字段================================
    /**
     * 主键id
     */
    @Id
    @Column(name = "id")
    @GenericGenerator(name = "snowflake", strategy = SnowflakeGenerator.TYPE)
    @GeneratedValue(generator = "snowflake")
    private String id;

    /**
     * device_id
     */
    @Column(name = "device_id")
    private String deviceId;

    /**
     * 序列号
     */
    @Column(name = "serial_no")
    private String serialNo;

    /**
     * 设备号
     */
    @Column(name = "monitor_no")
    private String monitorNo;

    /**
     * FLV协议播放地址（流畅）
     */
    @Column(name = "flv_address")
    private String flvAddress;

    /**
     * FLV协议直播地址（高清）
     */
    @Column(name = "hd_flv_address")
    private String hdFlvAddress;

    /**
     * 
     */
    @Column(name = "model_no")
    private String modelNo;

    /**
     * 项目ID
     */
    @Column(name = "project_id")
    private String projectId;

    /**
     * 感知类型
     */
    @Column(name = "perception_types")
    private String perceptionTypes;

    /**
     * 是否语音播报，0-不是，1-是
     */
    @Column(name = "voice_broadcast")
    private Integer voiceBroadcast;

    /**
     * 是否智能，0-不是，1-是
     */
    @Column(name = "smart")
    private Integer smart;

    /**
     * 类型，1-自建，2-环卫云，3-车载监控，4-萤石云，5-车载视频API, 6-锐明，7-海康车载视频API
     */
    @Column(name = "type")
    private Integer type;

    /**
     * 在线状态，0-离线，1-在线
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 真实状态 0-离线 1-在线
     */
    @Column(name = "real_status")
    private Integer realStatus;

    /**
     * 直播状态，0-未使用或直播已关闭，1-使用中，2-已过期，3-直播已暂停
     */
    @Column(name = "open_live")
    private Integer openLive;
    /**
     * 通道号
     */
    @Column(name = "sip_user_id")
    private String sipUserId;

    /**
     * 通道号
     */
    @Column(name = "channel")
    private String channel;

    /**
     * 备注
     */
    @Column(name = "memo")
    private String memo;

    /**
     * 是否删除，0-未删除，1-已删除
     */
    @Column(name = "is_deleted")
    private Integer isDeleted;

    /**
     * 在线时间
     */
    @Column(name = "online_at")
    private java.util.Date onlineAt;

    /**
     * 创建时间
     */
    @Column(name = "created_at")
    @CreatedDate
    private java.util.Date createdAt;

    /**
     * 最后更新时间
     */
    @Column(name = "updated_at")
    @LastModifiedDate
    private java.util.Date updatedAt;

    /**
     * 删除时间
     */
    @Column(name = "deleted_at")
    private java.util.Date deletedAt;

    /**
     * 物联网卡id
     */
    @Column(name = "card_id")
    private String cardId;

    /**
     * 出入库状态：1-已入库，2-已出库
     */
    @Column(name = "stock_status")
    private Integer stockStatus;

    /**
     * 是否锁定 0-正常 1-锁定
     */
    @Column(name = "is_lock")
    private Integer isLock;

    /**
     * 是否健康 0-异常 1-健康
     */
    @Column(name = "is_healthy")
    private Integer isHealthy;

    /**
     * 原始序列号
     */
    @Column(name = "original_serial_no")
    private String originalSerialNo;

    /**
     * 通道名称
     */
    @Column(name = "monitor_name")
    private String monitorName;

    /**
     * 通道id
     */
    @Column(name = "channel_id")
    private String channelId;

    /**
     * 真实在线时间
     */
    @Column(name = "real_online_at")
    private java.util.Date realOnlineAt;

//=============================表关联==================================

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(
            name = "model_no", referencedColumnName = "hk_model_no", insertable = false, updatable = false,
            foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
    @NotFound(action = NotFoundAction.IGNORE)
    @JSONField(serialize = false)
    private EventModelTypeConfig modelTypeConfig;

//===========================自定义字段=================================

    @Transient
    @ScLink(objName = "modelTypeConfig", filed = "scModelNo")
    private String scModelNo;
}
