package cn.shencom.model;

import cn.shencom.scloud.common.base.snowflake.SnowflakeGenerator;
import cn.shencom.scloud.common.jpa.scpage.ScLink;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.*;
import org.hibernate.annotations.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 小散工程-监管工单流程
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@Entity
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@DynamicUpdate
@DynamicInsert
@Table(name = "monitor_flow")
@Where(clause = "is_deleted = 0")
@EntityListeners(AuditingEntityListener.class)
public class MonitorFlow implements Serializable {
//===========================数据库字段================================
    /**
     * id
     */
    @Id
    @Column(name = "id")
    @GenericGenerator(name = "snowflake", strategy = SnowflakeGenerator.TYPE)
    @GeneratedValue(generator = "snowflake")
    private String id;

    /**
     * 工单id，关联monitor_order
     */
    @Column(name = "order_id")
    private String orderId;



    /**
     * 项目id，关联sporadic_project
     */
    @Column(name = "project_id")
    private String projectId;



    /**
     * 详情id ，关联
     */
    @Column(name = "relate_id")
    private String relateId;

    /**
     * 当前流程, 0-工程创建，1-安装预约，2-现场勘察，3-上门安装，4-接入监管， 5-施工完成，6-回收预约，7-上门回收， 8-监管结束
     */
    @Column(name = "flow")
    private Integer flow;


    /**
     * 当前节点状态，0-未完成，1-已完成
     */
    @Column(name = "state")
    private Integer state;


    /**
     * 当前节点结束时间
     */
    @Column(name = "finish_time")
    private java.util.Date finishTime;



    /**
     * 删除标记 1-已删除 0-默认
     */
    @Column(name = "is_deleted")
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @Column(name = "created_at")
    @CreatedDate
    private java.util.Date createdAt;

    /**
     * 更新时间
     */
    @Column(name = "updated_at")
    @LastModifiedDate
    private java.util.Date updatedAt;

    /**
     * 创建人
     */
    @Column(name = "created_user")
    private String createdUser;

    /**
     * 修改人
     */
    @Column(name = "updated_user")
    private String updatedUser;

//=============================表关联==================================


    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "created_user", referencedColumnName = "id", insertable = false, updatable = false,
            foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
    @NotFound(action = NotFoundAction.IGNORE)
    @JSONField(serialize = false)
    private SysUsers createdSysUser;



//===========================自定义字段=================================



    /** 系统用户真实姓名 */
    @ScLink(objName = "createdSysUser", filed = "realname")
    @Transient
    private String createdUserName;



    /** 系统用户联系方式 */
    @ScLink(objName = "createdSysUser", filed = "phone")
    @Transient
    private String createdUserPhone;



    @Transient
    private Integer messageType;
}
