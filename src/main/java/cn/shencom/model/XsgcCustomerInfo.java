package cn.shencom.model;

import cn.shencom.scloud.common.base.snowflake.SnowflakeGenerator;
import cn.shencom.scloud.common.jpa.scpage.ScLink;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.*;
import org.hibernate.annotations.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 小散工程-客户信息表
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Entity
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@DynamicUpdate
@DynamicInsert
@Table(name = "xsgc_customer_info")
@Where(clause = "is_deleted = 0")
@EntityListeners(AuditingEntityListener.class)
public class XsgcCustomerInfo implements Serializable {
//===========================数据库字段================================
    /**
     * id
     */
    @Id
    @Column(name = "id")
    @GenericGenerator(name = "snowflake", strategy = SnowflakeGenerator.TYPE)
    @GeneratedValue(generator = "snowflake")
    private String id;

    /**
     * 客户编码
     */
    @Column(name = "number")
    private String number;

    /**
     * 客户名称
     */
    @Column(name = "name")
    private String name;

    /**
     * 当前状态,0无效，1-有效
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 服务开始日期
     */
    @Column(name = "start_date")
    private java.util.Date startDate;

    /**
     * 服务结束日期
     */
    @Column(name = "end_date")
    private java.util.Date endDate;

    /**
     * 创建时间
     */
    @Column(name = "created_at")
    @CreatedDate
    private java.util.Date createdAt;

    /**
     * 更新时间
     */
    @Column(name = "updated_at")
    @LastModifiedDate
    private java.util.Date updatedAt;

    /**
     * 创建人
     */
    @Column(name = "created_user")
    private String createdUser;

    /**
     * 修改人
     */
    @Column(name = "updated_user")
    private String updatedUser;

    /**
     * 套餐id
     */
    @Column(name = "option_id")
    private String optionId;

    /**
     * 组织id
     */
    @Column(name = "organization_id")
    private String organizationId;

    /**
     * 联系人
     */
    @Column(name = "contact_user")
    private String contactUser;

    /**
     * 联系人电话
     */
    @Column(name = "contact_phone")
    private String contactPhone;

    /**
     * 联系人邮箱
     */
    @Column(name = "contact_email")
    private String contactEmail;

    /**
     * 备注
     */
    @Column(name = "memo")
    private String memo;

    /**
     * logo
     */
    @Column(name = "logo")
    private String logo;

    /**
     * 续签标记，0-首次开通,1-续签,2-临时续签
     */
    @Column(name = "renewal_mark")
    private Integer renewalMark;

    /**
     * 是否删除
     */
    @Column(name = "is_deleted")
    private Integer isDeleted;

    /**
     * 删除时间
     */
    @Column(name = "deleted_at")
    private java.util.Date deletedAt;

//=============================表关联==================================



    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "option_id", referencedColumnName = "id", insertable = false, updatable = false,
            foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
    @NotFound(action = NotFoundAction.IGNORE)
    @JSONField(serialize = false)
    private XsgcSubscription xsgcSubscription;


    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "created_user", referencedColumnName = "id", insertable = false, updatable = false,
            foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
    @NotFound(action = NotFoundAction.IGNORE)
    @JSONField(serialize = false)
    private SysUsers createdSysUser;



//===========================自定义字段=================================



    /** 套餐名称 */
    @ScLink(objName = "xsgcSubscription", filed = "name")
    @Transient
    private String subscriptionName;


    /** 系统用户真实姓名 */
    @ScLink(objName = "createdSysUser", filed = "realname")
    @Transient
    private String createdUserName;
}
