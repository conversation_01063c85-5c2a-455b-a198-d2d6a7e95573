package cn.shencom.model;

import cn.shencom.scloud.common.base.snowflake.SnowflakeGenerator;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.*;
import org.hibernate.annotations.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.List;

/**
 * 小散工程-组织团队成员表
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Entity
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@DynamicUpdate
@DynamicInsert
@Table(name = "fn_rmsv3_members")
@Where(clause = "is_deleted = 0")
@EntityListeners(AuditingEntityListener.class)
public class FnRmsv3Members implements Serializable {
//===========================数据库字段================================
    /**
     * id
     */
    @Id
    @Column(name = "id")
    @GenericGenerator(name = "snowflake", strategy = SnowflakeGenerator.TYPE)
    @GeneratedValue(generator = "snowflake")
    private String id;

    /**
     * 用户id
     */
    @Column(name = "user_id")
    private String userId;

    /**
     * 姓名
     */
    @Column(name = "realname")
    private String realname;

    /**
     * 手机号
     */
    @Column(name = "mobile")
    private String mobile;

    /**
     * 组织id
     */
    @Column(name = "organization_id")
    private String organizationId;

    /**
     * 创建时间
     */
    @Column(name = "created_at")
    @CreatedDate
    private java.util.Date createdAt;

    /**
     * 最后更新时间
     */
    @Column(name = "updated_at")
    @LastModifiedDate
    private java.util.Date updatedAt;

    /**
     * 删除时间
     */
    @Column(name = "deleted_at")
    private java.util.Date deletedAt;


    /**
     * 是否删除
     */
    @Column(name = "is_deleted")
    private Integer isDeleted;

//=============================表关联==================================

    @OneToMany(mappedBy = "fnRmsv3Members", fetch = FetchType.LAZY)
    @NotFound(action = NotFoundAction.IGNORE)
    @JSONField(serialize = false)
    private List<FnRmsv3MembersTypeRelate> fnRmsv3MembersTypeRelateList;



//===========================自定义字段=================================

}
