package cn.shencom.model;

import cn.shencom.scloud.common.base.snowflake.SnowflakeGenerator;
import cn.shencom.scloud.common.jpa.scpage.ScLink;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.*;
import org.hibernate.annotations.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.Table;
import javax.persistence.*;
import java.io.Serializable;
import java.util.List;

/**
 * 厂商场景管理
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Entity
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@DynamicUpdate
@DynamicInsert
@Table(name = "aim_firm_scene_management")
@Where(clause = "is_deleted = 0")
@EntityListeners(AuditingEntityListener.class)
public class AimFirmSceneManagement implements Serializable {
//===========================数据库字段================================
    /**
     * 
     */
    @Id
    @Column(name = "id")
    @GenericGenerator(name = "snowflake", strategy = SnowflakeGenerator.TYPE)
    @GeneratedValue(generator = "snowflake")
    private String id;

    /**
     * 厂商名称
     */
    @Column(name = "firm_id")
    private String firmId;

    /**
     * 厂商场景编号
     */
    @Column(name = "code")
    private String code;

    /**
     * 厂商场景名称
     */
    @Column(name = "scene_name")
    private String sceneName;

    /**
     * 关联AI场景数
     */
    @Column(name = "scene_num")
    private Integer sceneNum;

    /**
     * 场景介绍
     */
    @Column(name = "directions")
    private String directions;

    /**
     * 图片（逗号分隔）
     */
    @Column(name = "pics")
    private String pics;

    /**
     * 是否删除：1-是 0-否
     */
    @Column(name = "is_deleted")
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @Column(name = "created_at")
    @CreatedDate
    private java.util.Date createdAt;

    /**
     * 更新时间
     */
    @Column(name = "updated_at")
    @LastModifiedDate
    private java.util.Date updatedAt;

//=============================表关联==================================

    @OneToMany(targetEntity = AimSceneFirmRelationship.class, fetch = FetchType.LAZY)
    @JoinColumn(name = "firm_id", referencedColumnName = "id", insertable = false, updatable = false,
            foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
    private List<AimSceneFirmRelationship> aimSceneFirmRelationships;


    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "firm_id", insertable = false, updatable = false)
    @NotFound(action = NotFoundAction.IGNORE)
    @JSONField(serialize = false)
    private AimFirm aimFirm;
//===========================自定义字段=================================

    @Transient
    @ScLink(objName = "aimFirm",filed = "firmName")
    private String firmName;
}
