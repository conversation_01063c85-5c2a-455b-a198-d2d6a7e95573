package cn.shencom.model;

import cn.shencom.scloud.common.base.snowflake.SnowflakeGenerator;
import lombok.*;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Where;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 施工单位组织关联表
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Entity
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@DynamicUpdate
@DynamicInsert
@Table(name = "construction_unit_organization_relate")
@Where(clause = "is_deleted = 0")
@EntityListeners(AuditingEntityListener.class)
public class ConstructionUnitOrganizationRelate implements Serializable {
//===========================数据库字段================================
    /**
     * 主键ID
     */
    @Id
    @Column(name = "id")
    @GenericGenerator(name = "snowflake", strategy = SnowflakeGenerator.TYPE)
    @GeneratedValue(generator = "snowflake")
    private String id;

    /**
     * 施工单位ID
     */
    @Column(name = "construction_unit_id")
    private String constructionUnitId;

    /**
     * 组织ID
     */
    @Column(name = "organization_id")
    private String organizationId;

    /**
     * 信用分数，每个组织独立计算
     */
    @Column(name = "credit_score")
    private java.math.BigDecimal creditScore;

    /**
     * 是否黑名单：0-否，1-是
     */
    @Column(name = "is_blacklist")
    private Integer isBlacklist;

    /**
     * 黑名单原因
     */
    @Column(name = "blacklist_reason")
    private String blacklistReason;

    /**
     * 加入黑名单时间
     */
    @Column(name = "blacklist_time")
    private java.util.Date blacklistTime;

    /**
     * 创建时间
     */
    @Column(name = "created_at")
    @CreatedDate
    private java.util.Date createdAt;

    /**
     * 更新时间
     */
    @Column(name = "updated_at")
    @LastModifiedDate
    private java.util.Date updatedAt;

    /**
     * 创建人
     */
    @Column(name = "created_user")
    private String createdUser;

    /**
     * 更新人
     */
    @Column(name = "updated_user")
    private String updatedUser;

    /**
     * 是否删除：0-否，1-是
     */
    @Column(name = "is_deleted")
    private Integer isDeleted;

    /**
     * 删除时间
     */
    @Column(name = "deleted_at")
    private java.util.Date deletedAt;

//=============================表关联==================================


//===========================自定义字段=================================

}
