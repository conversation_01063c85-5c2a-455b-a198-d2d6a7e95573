package cn.shencom.filter;

import cn.shencom.scloud.common.base.constant.GlobalConstant;
import cn.shencom.scloud.common.util.ScidContext;
import cn.shencom.utils.XsgcContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Slf4j
@Component("multiOrganizationFilter")
@Order(Integer.MIN_VALUE + 2)
public class MultiOrganizationFilter extends OncePerRequestFilter {



    @Override
    public void destroy() {
        log.trace("multiOrganizationFilter destroyed");
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String organizationId = getOrganizationId(request);
        log.trace("organizationId:{},注入XsgcContext", organizationId);
        XsgcContext.setOrganizationId(organizationId);
        filterChain.doFilter(request, response);
    }

    protected String getOrganizationId(HttpServletRequest request) {
        String organizationId = request.getParameter("organizationId");
        if (null == organizationId) {
            organizationId = request.getHeader("organizationId");
        }
        return organizationId;
    }

}
