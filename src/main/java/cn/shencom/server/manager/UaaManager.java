package cn.shencom.server.manager;

import cn.hutool.core.util.ObjectUtil;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.base.constant.RespCode;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.scloud.common.jpa.util.query.ScQueryUtil;
import cn.shencom.scloud.common.util.Base64Util;
import cn.shencom.scloud.common.util.RSAUtil;
import cn.shencom.scloud.common.util.ScidContext;
import cn.shencom.scloud.scloudapiuaa.dto.SysRoleUserDTO;
import cn.shencom.scloud.scloudapiuaa.dto.SysUsersDTO;
import cn.shencom.scloud.scloudapiuaa.service.UaaService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/1/10 17:38
 */
@Component
@Slf4j
public class UaaManager {

    @Autowired
    private UaaService uaaService;

    @Value("init.password:Sc123456")
    private String initPassword;

    /**
     * 校验当前用户是否有权限
     *
     * @param permission 权限名
     * @return
     */
    public boolean hasPermission(String permission) {
        boolean hasPermission = false;
        Result result = uaaService.allPermission(ScidContext.getScid());
        if (result != null && result.getData() != null) {
            List<String> permissionList = ScQueryUtil.getFeiginList(result, String.class);
            if (CollectionUtils.isNotEmpty(permissionList)) {
                if (permissionList.contains(permission)) {
                    hasPermission = true;
                }
            }
        }
        return hasPermission;
    }

    /**
     * 用户初始化
     *
     * @param name  名字
     * @param phone 电话
     * @return {@link SysUsersDTO}
     */
    public SysUsersDTO userInit(String name, String phone) {
        SysUsersDTO sysUsersDTO = new SysUsersDTO();
        sysUsersDTO.setRealname(name);
        sysUsersDTO.setPhone(phone);
        try {
            byte[] encrypt = RSAUtil.encrypt(RSAUtil.getPublicKey()
                    , initPassword.getBytes());
            sysUsersDTO.setPassword(Base64Util.encode(encrypt));
        } catch (Exception e) {
            e.printStackTrace();
        }
        Result<SysUsersDTO> sysUsersDtoResult = uaaService.userInit(ScidContext.getScid(), sysUsersDTO);
        if (sysUsersDtoResult == null) {
            throw new ScException(RespCode.GET_USER_ERROR);
        }
        SysUsersDTO data = sysUsersDtoResult.getData();
        if (data == null) {
            throw new ScException(RespCode.GET_USER_ERROR);
        }
        return data;
    }

    /**
     * 用户初始化
     *
     * @param sysUsersDTO
     * @return {@link SysUsersDTO}
     */
    public SysUsersDTO userInit(SysUsersDTO sysUsersDTO) {
        try {
            byte[] encrypt = RSAUtil.encrypt(RSAUtil.getPublicKey()
                    , initPassword.getBytes());
            sysUsersDTO.setPassword(Base64Util.encode(encrypt));
        } catch (Exception e) {
            e.printStackTrace();
        }
        Result<SysUsersDTO> sysUsersDtoResult = uaaService.userInit(ScidContext.getScid(), sysUsersDTO);
        if (sysUsersDtoResult == null) {
            throw new ScException(RespCode.GET_USER_ERROR);
        }
        SysUsersDTO data = sysUsersDtoResult.getData();
        if (data == null) {
            throw new ScException(RespCode.GET_USER_ERROR);
        }
        return data;
    }

    /**
     * 更新系统用户
     *
     * @param id       id
     * @param realName 真实姓名
     * @param phone    手机号
     * @return {@link SysUsersDTO}
     */
    public SysUsersDTO update(String id, String realName, String phone) {
        SysUsersDTO sysUsersDTO = new SysUsersDTO();
        sysUsersDTO.setId(id);
        sysUsersDTO.setRealname(realName);
        sysUsersDTO.setPhone(phone);
        return update(sysUsersDTO);
    }

    /**
     * 更新系统用户
     *
     * @param sysUsersDTO 系统用户dto
     * @return {@link SysUsersDTO}
     */
    public SysUsersDTO update(SysUsersDTO sysUsersDTO) {
        Result<SysUsersDTO> sysUsersDtoResult = uaaService.update(ScidContext.getScid(), sysUsersDTO);
        if (sysUsersDtoResult == null) {
            throw new ScException(RespCode.GET_USER_ERROR);
        }
        SysUsersDTO data = sysUsersDtoResult.getData();
        if (data == null) {
            throw new ScException(RespCode.GET_USER_ERROR);
        }
        return data;
    }

    /**
     * 校验验证码
     *
     * @param sysUsersDTO 系统用户dto
     */
    public void msgCodeVerify(SysUsersDTO sysUsersDTO) {
        Result<SysUsersDTO> msgResult = uaaService.msgCodeVerify(ScidContext.getScid(), sysUsersDTO);
        if (msgResult == null) {
            throw new ScException(RespCode.INVALID_VERIFY_CODE);
        }
        if (!RespCode.SUCCESS.code().equals(msgResult.getErrcode())) {
            throw new ScException(msgResult.getErrcode(), msgResult.getErrmsg());
        }
    }

    /**
     * 根据用户id查询系统用户
     *
     * @param userId 用户id
     * @return {@link SysUsersDTO}
     */
    public SysUsersDTO findById(String userId) {
        SysUsersDTO sysUsersDTO = new SysUsersDTO();
        sysUsersDTO.setId(userId);
        return findBySysUsersDTO(sysUsersDTO);
    }

    /**
     * 根据手机查询系统用户
     *
     * @param phone 电话
     * @return {@link SysUsersDTO}
     */
    public SysUsersDTO findByPhone(String phone) {
        SysUsersDTO sysUsersDTO = new SysUsersDTO();
        sysUsersDTO.setPhone(phone);
        return findBySysUsersDTO(sysUsersDTO);
    }

    /**
     * 查询系统用户
     *
     * @param sysUsersDTO 系统用户dto
     * @return {@link SysUsersDTO}
     */
    public SysUsersDTO findBySysUsersDTO(SysUsersDTO sysUsersDTO) {
        Result<SysUsersDTO> sysUsersDtoResult = uaaService.findById(ScidContext.getScid(), sysUsersDTO);
        if (sysUsersDtoResult == null) {
            return null;
        }
        return sysUsersDtoResult.getData();
    }

    /**
     * 创建/更新系统用户
     */
    public String createOrUpdateUser(SysUsersDTO sysUsersDTO) {
        if (StringUtils.isBlank(sysUsersDTO.getPhone())) {
            throw new ScException("手机号不能为空");
        }
        SysUsersDTO sysUsers = findByPhone(sysUsersDTO.getPhone());
        if (ObjectUtil.isNotEmpty(sysUsers)) {
            sysUsersDTO.setId(sysUsers.getId());
            SysUsersDTO update = update(sysUsersDTO);
            return update.getId();
        } else {
            SysUsersDTO create = userInit(sysUsersDTO);
            return create.getId();
        }
    }

    /**
     * 获取用户是否有某种角色
     *
     * @param userId   用户id
     * @param roleName 角色名称
     * @return {@link SysUsersDTO}
     */
    public Boolean hasRole(String userId, String roleName) {
        SysRoleUserDTO sysRoleUserDTO = new SysRoleUserDTO();
        sysRoleUserDTO.setUserId(userId);
        sysRoleUserDTO.setRoleName(roleName);
        return hasRole(sysRoleUserDTO);
    }

    /**
     * 获取用户是否有某种角色
     *
     * @param sysRoleUserDTO 系统用户角色dto
     * @return {@link SysRoleUserDTO}
     */
    public Boolean hasRole(SysRoleUserDTO sysRoleUserDTO) {
        Result<Boolean> hasRoleResult = uaaService.hasRole(ScidContext.getScid(), sysRoleUserDTO);
        if (hasRoleResult == null) {
            throw new ScException(RespCode.GET_USER_ERROR);
        }
        Boolean data = hasRoleResult.getData();
        if (data == null) {
            throw new ScException(RespCode.GET_USER_ERROR);
        }
        return data;
    }

    /**
     * 给用户批量添加角色
     *
     * @param sysRoleUserDTO 系统用户角色dto
     * @return {@link SysRoleUserDTO}
     */
    public void createUserRoles(SysRoleUserDTO sysRoleUserDTO) {
        Result result = uaaService.createUserRoles(ScidContext.getScid(), sysRoleUserDTO);
        if (result == null) {
            throw new ScException("用户服务错误");
        }
        String errcode = result.getErrcode();
        if (!"0000".equals(errcode)) {
            throw new ScException("用户服务错误");
        }
    }

    /**
     * 给用户批量删除角色
     *
     * @param sysRoleUserDTO 系统用户角色dto
     * @return {@link SysRoleUserDTO}
     */
    public void deleteUserRoles(SysRoleUserDTO sysRoleUserDTO) {
        Result result = uaaService.deleteUserRoles(ScidContext.getScid(), sysRoleUserDTO);
        if (result == null) {
            throw new ScException("用户服务错误");
        }
        String errcode = result.getErrcode();
        if (!"0000".equals(errcode)) {
            throw new ScException("用户服务错误");
        }
    }

}
