package cn.shencom.server.timer;

import cn.shencom.common.util.DBContext;
import cn.shencom.server.service.IXsgcCustomerInfoService;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;


/**
 * 小散工程定时任务
 */
@Component
public class XsgcCustomerTimer {


    @Value("${scid}")
    private String scid;

    @Autowired
    private IXsgcCustomerInfoService customerInfoService;

    /**
     * 服务到期时关闭服务
     */
    @XxlJob("autoCloseCustomerService")
    public void autoCloseCustomerService() {
        DBContext.setDbType(scid);
        // 服务到期时关闭服务
        customerInfoService.autoCloseService();
    }

}
