package cn.shencom.server.timer;

import cn.shencom.common.util.DBContext;
import cn.shencom.server.service.IEventCameraPointService;
import cn.shencom.server.service.ISporadicProjectService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SporadicProjectTimer {

    @Value("${scid}")
    private String scid;


    @Autowired
    private ISporadicProjectService sporadicProjectService;

    /**
     * 更新施工状态
     */
    @XxlJob("updateSporadicProjectStatus")
    public void updateSporadicProjectStatus() {
        DBContext.setDbType(scid);
        sporadicProjectService.autoUpdateStatus();
    }

//
//    /**
//     * 更新处于移除监控环节监管状态
//     */
//    @XxlJob("autoUpdateMonitorFlag")
//    public void autoUpdateMonitorFlag() {
//        DBContext.setDbType(scid);
//        sporadicProjectService.autoUpdateMonitorFlag();
//    }

}
