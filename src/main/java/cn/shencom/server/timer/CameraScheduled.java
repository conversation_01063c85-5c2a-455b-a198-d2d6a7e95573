package cn.shencom.server.timer;


import cn.shencom.common.util.DBContext;
import cn.shencom.server.service.*;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @date 2023/8/2 10:37
 */
@Slf4j
@Component
public class CameraScheduled {

    @Value("${scid}")
    private String scid;


    @Autowired
    private IEventCameraPointService eventCameraPointService;

    /**
     * 更新Aiot的状态信息
     */
    @XxlJob("updateAiotDeviceStatus")
    public void updateAiotDeviceStatus() {
        DBContext.setDbType(scid);
        // 查询AIot的设备
        eventCameraPointService.updateAiotDeviceStatus();
    }


}
