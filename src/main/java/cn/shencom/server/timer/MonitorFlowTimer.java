package cn.shencom.server.timer;

import cn.shencom.server.service.IMonitorFlowService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class MonitorFlowTimer {

    @Value("${scid}")
    private String scid;


    @Autowired
    private IMonitorFlowService monitorFlowService;


    /**
     * 更新监管状态
     */




}
