package cn.shencom.server.controller;


import cn.shencom.model.SysMenu;
import cn.shencom.model.dto.vo.SysMenuVO;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.server.controller.BaseController;
import cn.shencom.scloud.security.core.ScContext;
import cn.shencom.scloud.security.core.SecurityUser;
import cn.shencom.server.service.ISysMenuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * SysMenu的路由接口服务
 *
 * <AUTHOR>
@RestController
@RequestMapping("/sys/menu")
public class SysMenuController extends BaseController {

    /**
     * SysMenuService服务
     */
    @Autowired
    private ISysMenuService iSysMenuService;


    /**
     * 根据个人权限获取树形菜单
     *
     * @param bean
     * @return
     */
    @PostMapping(value = {"/tree/my"}, produces = {MediaType.APPLICATION_JSON_VALUE})
    @ResponseBody
    public Result<List<SysMenuVO>> getTree(@RequestBody(required = false) SysMenu bean) {
        SecurityUser currentUser = ScContext.getCurrentUserThrow();
        bean.setUserId(currentUser.getUid());
        return iSysMenuService.getMenuTree(bean);
    }
}
