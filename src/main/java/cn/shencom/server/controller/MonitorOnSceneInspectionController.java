package cn.shencom.server.controller;

import cn.shencom.model.dto.create.MonitorOnSceneInspectionCreateDTO;
import cn.shencom.model.dto.query.MonitorOnSceneInspectionQueryDTO;
import cn.shencom.model.dto.resp.MonitorOnSceneInspectionRespDTO;
import cn.shencom.model.dto.update.MonitorOnSceneInspectionUpdateDTO;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.dto.ScDeleteDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.server.controller.BaseController;
import cn.shencom.server.service.IMonitorOnSceneInspectionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * 小散工程-监管工单流程-现场勘察详情 的接口服务
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@RestController
@RequestMapping("/monitor/on/scene/inspection")
public class MonitorOnSceneInspectionController extends BaseController {

    @Autowired
    private IMonitorOnSceneInspectionService iMonitorOnSceneInspectionService;

    /**
     * 查询小散工程-监管工单流程-现场勘察详情列表
     *
     * @param bean 条件
     * @return {@link Result}<{@link Page}<{@link MonitorOnSceneInspectionRespDTO}>>
     */
    @PostMapping(value = {"/index"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Page<MonitorOnSceneInspectionRespDTO>> index(@RequestBody @Validated MonitorOnSceneInspectionQueryDTO bean) {
        return success(iMonitorOnSceneInspectionService.query(bean));
    }

    /**
     * 根据id查询小散工程-监管工单流程-现场勘察详情
     *
     * @param bean 条件
     * @return {@link Result<MonitorOnSceneInspectionRespDTO>}
     */
    @PostMapping(value = {"/show"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<MonitorOnSceneInspectionRespDTO> show(@RequestBody @Validated ScShowDTO bean) {
        return success(iMonitorOnSceneInspectionService.show(bean));
    }

    /**
     * 新建小散工程-监管工单流程-现场勘察详情
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/create"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> create(@RequestBody @Validated MonitorOnSceneInspectionCreateDTO bean) {
        return success(Objects.nonNull(iMonitorOnSceneInspectionService.create(bean)));
    }

    /**
     * 修改小散工程-监管工单流程-现场勘察详情
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/update"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> update(@RequestBody @Validated MonitorOnSceneInspectionUpdateDTO bean) {
        return success(Objects.nonNull(iMonitorOnSceneInspectionService.update(bean)));
    }

    /**
     * 删除小散工程-监管工单流程-现场勘察详情
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/delete"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result delete(@RequestBody @Validated ScDeleteDTO bean) {
        iMonitorOnSceneInspectionService.delete(bean.getIds());
        return success();
    }

    /**
     * 导出小散工程-监管工单流程-现场勘察详情
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/export"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result export(@RequestBody @Validated MonitorOnSceneInspectionQueryDTO bean) {
        iMonitorOnSceneInspectionService.export(bean);
        return success();
    }

}
