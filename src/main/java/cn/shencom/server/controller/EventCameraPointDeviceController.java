package cn.shencom.server.controller;

import cn.shencom.model.dto.create.EventCameraPointDeviceCreateDTO;
import cn.shencom.model.dto.query.EventCameraPointDeviceQueryDTO;
import cn.shencom.model.dto.query.EventCameraPointDeviceMobileQueryDTO;
import cn.shencom.model.dto.resp.EventCameraPointDeviceRespDTO;
import cn.shencom.model.dto.resp.EventCameraPointDeviceMobileRespDTO;
import cn.shencom.model.dto.update.EventCameraPointDeviceUpdateDTO;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.dto.ScDeleteDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.server.controller.BaseController;
import cn.shencom.server.service.IEventCameraPointDeviceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * 监控设备表 的接口服务
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@RestController
@RequestMapping("/event/camera/point/device")
public class EventCameraPointDeviceController extends BaseController {

    @Autowired
    private IEventCameraPointDeviceService iEventCameraPointDeviceService;

    /**
     * 查询监控设备表列表
     *
     * @param bean 条件
     * @return {@link Result}<{@link Page}<{@link EventCameraPointDeviceRespDTO}>>
     */
    @PostMapping(value = {"/index"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Page<EventCameraPointDeviceRespDTO>> index(@RequestBody @Validated EventCameraPointDeviceQueryDTO bean) {
        return success(iEventCameraPointDeviceService.query(bean));
    }

    /**
     * 根据id查询监控设备表
     *
     * @param bean 条件
     * @return {@link Result<EventCameraPointDeviceRespDTO>}
     */
    @PostMapping(value = {"/show"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<EventCameraPointDeviceRespDTO> show(@RequestBody @Validated ScShowDTO bean) {
        return success(iEventCameraPointDeviceService.show(bean));
    }

    /**
     * 新建监控设备表
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/create"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> create(@RequestBody @Validated EventCameraPointDeviceCreateDTO bean) {
        return success(Objects.nonNull(iEventCameraPointDeviceService.create(bean)));
    }

    /**
     * 修改监控设备表
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/update"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> update(@RequestBody @Validated EventCameraPointDeviceUpdateDTO bean) {
        return success(Objects.nonNull(iEventCameraPointDeviceService.update(bean)));
    }

    /**
     * 删除监控设备表
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/delete"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result delete(@RequestBody @Validated ScDeleteDTO bean) {
        iEventCameraPointDeviceService.delete(bean.getIds());
        return success();
    }

    /**
     * 导出监控设备表
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/export"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result export(@RequestBody @Validated EventCameraPointDeviceQueryDTO bean) {
        iEventCameraPointDeviceService.export(bean);
        return success();
    }

    /**
     * 查询监控设备表列表 - 移动端专用
     *
     * @param bean 条件
     * @return {@link Result}<{@link Page}<{@link EventCameraPointDeviceMobileRespDTO}>>
     */
    @PostMapping(value = {"/mobile/index"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Page<EventCameraPointDeviceMobileRespDTO>> mobileIndex(@RequestBody @Validated EventCameraPointDeviceMobileQueryDTO bean) {
        return success(iEventCameraPointDeviceService.mobileIndex(bean));
    }




    /**
     * 查询未关联的设备id - 移动端专用
     */
    @PostMapping(value = {"/not/relevance/index"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Page<EventCameraPointDeviceRespDTO>> mobileIndex(@RequestBody @Validated EventCameraPointDeviceQueryDTO bean) {
        return success(iEventCameraPointDeviceService.notRelevance(bean));
    }

}
