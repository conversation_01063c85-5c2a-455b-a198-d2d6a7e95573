package cn.shencom.server.controller;

import cn.shencom.model.dto.AiotEventPushDTO;
import cn.shencom.model.dto.create.AiotEventCreateDTO;
import cn.shencom.model.dto.query.AiotEventMobileQueryDTO;
import cn.shencom.model.dto.query.AiotEventQueryDTO;
import cn.shencom.model.dto.resp.AiotEventMobileRespDTO;
import cn.shencom.model.dto.resp.AiotEventRespDTO;
import cn.shencom.model.dto.update.AiotEventUpdateDTO;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.dto.ScDeleteDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.server.controller.BaseController;
import cn.shencom.server.service.IAiotEventService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * 监控事件 的接口服务
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@RestController
@RequestMapping("/aiot/event")
public class AiotEventController extends BaseController {

    @Autowired
    private IAiotEventService iAiotEventService;

    /**
     * 查询监控事件列表
     *
     * @param bean 条件
     * @return {@link Result}<{@link Page}<{@link AiotEventRespDTO}>>
     */
    @PostMapping(value = {"/index"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Page<AiotEventRespDTO>> index(@RequestBody @Validated AiotEventQueryDTO bean) {
        return success(iAiotEventService.query(bean));
    }

    /**
     * 根据id查询监控事件
     *
     * @param bean 条件
     * @return {@link Result<AiotEventRespDTO>}
     */
    @PostMapping(value = {"/show"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<AiotEventRespDTO> show(@RequestBody @Validated ScShowDTO bean) {
        return success(iAiotEventService.show(bean));
    }

    /**
     * 新建监控事件
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/create"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> create(@RequestBody @Validated AiotEventCreateDTO bean) {
        return success(Objects.nonNull(iAiotEventService.create(bean)));
    }

    /**
     * 修改监控事件
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/update"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> update(@RequestBody @Validated AiotEventUpdateDTO bean) {
        return success(Objects.nonNull(iAiotEventService.update(bean)));
    }

    /**
     * 删除监控事件
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/delete"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<?> delete(@RequestBody @Validated ScDeleteDTO bean) {
        iAiotEventService.delete(bean.getIds());
        return success();
    }

    /**
     * 导出监控事件
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/export"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<?> export(@RequestBody @Validated AiotEventQueryDTO bean) {
        iAiotEventService.export(bean);
        return success();
    }

    /**
     * 接收监控事件推送
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/push"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<?> push(@RequestBody @Validated AiotEventPushDTO bean) {
        iAiotEventService.push(bean);
        return success();
    }

    /**
     * 查询监控事件列表 - 移动端专用
     *
     * @param bean 条件
     * @return {@link Result}<{@link Page}<{@link AiotEventRespDTO}>>
     */
    @PostMapping(value = {"/mobile/index"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Page<AiotEventMobileRespDTO>> mobileIndex(@RequestBody @Validated AiotEventMobileQueryDTO bean) {
        return success(iAiotEventService.mobileIndex(bean));
    }

}
