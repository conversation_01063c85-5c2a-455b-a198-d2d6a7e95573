package cn.shencom.server.controller;

import cn.shencom.model.dto.CameraTreeRegionDTO;
import cn.shencom.model.dto.query.EventCameraPointQueryDTO;
import cn.shencom.model.dto.resp.EventCameraPointDeviceRespDTO;
import cn.shencom.model.dto.resp.EventCameraPointRespDTO;
import cn.shencom.model.dto.resp.ExApiDevicePlaybackListRespDTO;
import cn.shencom.model.dto.resp.LiveRespDTO;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.dto.ScDeleteDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.server.controller.BaseController;
import cn.shencom.server.service.IEventCameraPointService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 摄像头信息表 的接口服务
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@RestController
@RequestMapping("/event/camera/point")
public class EventCameraPointController extends BaseController {

    @Autowired
    private IEventCameraPointService iEventCameraPointService;

    /**
     * 查询摄像头信息表列表
     *
     * @param bean 条件
     * @return {@link Result}<{@link Page}<{@link EventCameraPointRespDTO}>>
     */
    @PostMapping(value = {"/index"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Page<EventCameraPointRespDTO>> index(@RequestBody @Validated EventCameraPointQueryDTO bean) {
        return success(iEventCameraPointService.query(bean));
    }

    /**
     * 根据id查询摄像头信息表
     *
     * @param bean 条件
     * @return {@link Result<EventCameraPointRespDTO>}
     */
    @PostMapping(value = {"/show"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<EventCameraPointRespDTO> show(@RequestBody @Validated ScShowDTO bean) {
        return success(iEventCameraPointService.show(bean));
    }

//    /**
//     * 新建摄像头信息表
//     *
//     * @param bean 条件
//     * @return {@link Result<Boolean>}
//     */
//    @PostMapping(value = {"/create"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
//    public Result<Boolean> create(@RequestBody @Validated EventCameraPointCreateDTO bean) {
//        return success(Objects.nonNull(iEventCameraPointService.create(bean)));
//    }

    /**
     * 摄像头类型
     * @param bean
     * @return {@link Result}<{@link Map}<{@link String,String}>>
     */

    @PostMapping(value = {"/camera/type"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    @ResponseBody
    public Result cameraType(@RequestBody @Validated EventCameraPointQueryDTO bean){
        return success(iEventCameraPointService.getCameraType(bean));
    }

    /**
     * 删除摄像头信息表
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/delete"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result delete(@RequestBody @Validated ScDeleteDTO bean) {
        iEventCameraPointService.delete(bean.getIds());
        return success();
    }

    /**
     * 导出摄像头信息表
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/export"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result export(@RequestBody @Validated EventCameraPointQueryDTO bean) {
        iEventCameraPointService.export(bean);
        return success();
    }

    /**
     * 查询监控设备区域树
     * @param bean
     * @return {@link Result}<{@link List}<{@link CameraTreeRegionDTO}>>
     */
    @ResponseBody
    @PostMapping(value = {"/tree"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<List<CameraTreeRegionDTO>> tree(@RequestBody @Validated EventCameraPointQueryDTO bean, HttpServletResponse response) {
        return iEventCameraPointService.treeQueryAll(bean, response);
    }

    /**
     * 开启单个摄像头直播并刷新直播链接
     */
    @PostMapping(value = {"/camera/open"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result hikyunCameraOpen(@RequestBody EventCameraPointQueryDTO bean) {
        return iEventCameraPointService.cameraOpen(bean);
    }

    /**
     * 同步摄像头在线状态
     * @param
     * @return {@link Result}
     */
    @PostMapping(value = {"/sync/camera/online"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result syncCameraOnline() {
        iEventCameraPointService.syncCameraOnline();
        return success();
    }

    /**
     * 查询摄像头直播链接
     * @param bean 请求
     * @return {@link Result}<{@link LiveRespDTO}>
     */
    @ResponseBody
    @PostMapping(value = {"/live"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<LiveRespDTO> getLive(@RequestBody @Validated EventCameraPointQueryDTO bean) {
        return success(iEventCameraPointService.getLive(bean));
    }

    /**
     * 查询摄像头音视频回放
     *
     * @param bean 请求
     * @return {@link Result}<{@link LiveRespDTO}>
     */
    @ResponseBody
    @PostMapping(value = {"/history"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<LiveRespDTO> getHistory(@RequestBody EventCameraPointQueryDTO bean) {
        return success(iEventCameraPointService.getHistory(bean));
    }

    /**
     * 音视频回放(列表)
     *
     * @param bean 请求
     * @return 音视频回放
     */
    @ResponseBody
    @PostMapping(value = {"/history/list"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<List<ExApiDevicePlaybackListRespDTO>> getHistoryList(@RequestBody EventCameraPointQueryDTO bean) {
        return success(iEventCameraPointService.getHistoryList(bean));
    }

    /**
     * 查询摄像头直播链接信息列表
     */
    @PostMapping(value = {"/tree/index"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    @ResponseBody
    public Result treeIndex(@RequestBody EventCameraPointQueryDTO bean) {
        return iEventCameraPointService.treeQuery(bean);
    }
}
