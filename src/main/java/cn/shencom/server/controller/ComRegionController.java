package cn.shencom.server.controller;

import cn.hutool.core.lang.tree.Tree;
import cn.shencom.model.dto.query.ComRegionQueryDTO;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.server.controller.BaseController;
import cn.shencom.server.service.IComRegionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 查询区域
 * @date 2022/6/16 14:17
 */
@RestController
@RequestMapping("/com/region")
public class ComRegionController extends BaseController {

    @Autowired
    private IComRegionService comRegionService;

    // 区域相关接口 ------------------------------开始----------------------------------

    /**
     * 区域树形接口 - 无权限
     *
     * @param bean 条件
     * @return {@link Result}<{@link List}<{@link Tree<String>}>>
     */
    @PostMapping(value = {"/tree"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    @ResponseBody
    public Result<List<Tree<String>>> tree(@RequestBody @Validated ComRegionQueryDTO bean) {
        return success(comRegionService.comRegionTree(bean));
    }



    /**
     * 如果后台改数据库请调用此接口清理redis缓存
     * 手动更新区域缓存redis
     * key-value
     *
     */
    @PostMapping(value = {"/clear/redis"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    @ResponseBody
    public Result<Boolean> clearRegionRedis() {
        comRegionService.clearRegionRedis();
        return success();
    }

        /**
     * 区域树形接口 - 权限(市,区,街道,社区)
     * pId 根节点id
     *
     * @param bean
     */
    @PostMapping(value = {"/role/tree"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    @ResponseBody
    public Result<List<Tree<String>>> roleTree(@RequestBody @Validated ComRegionQueryDTO bean) {
        return success(comRegionService.roleTree(bean));
    }

}
