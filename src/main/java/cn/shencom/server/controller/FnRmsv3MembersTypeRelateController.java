package cn.shencom.server.controller;

import cn.shencom.model.dto.MemberRelateStatics;
import cn.shencom.model.dto.create.FnRmsv3MembersTypeRelateCreateDTO;
import cn.shencom.model.dto.query.FnRmsv3MembersTypeRelateQueryDTO;
import cn.shencom.model.dto.resp.FnRmsv3MembersTypeRelateRespDTO;
import cn.shencom.model.dto.resp.XsgcOrganizationRespDTO;
import cn.shencom.model.dto.update.FnRmsv3MembersTypeRelateUpdateDTO;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.dto.ScDeleteDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.server.controller.BaseController;
import cn.shencom.server.service.IFnRmsv3MembersTypeRelateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 小散工程-组织团队成员关系 的接口服务
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@RestController
@RequestMapping("/fn/rmsv3/members/type/relate")
public class FnRmsv3MembersTypeRelateController extends BaseController {

    @Autowired
    private IFnRmsv3MembersTypeRelateService iFnRmsv3MembersTypeRelateService;

    /**
     * 查询小散工程-组织团队成员关系列表
     *
     * @param bean 条件
     * @return {@link Result}<{@link Page}<{@link FnRmsv3MembersTypeRelateRespDTO}>>
     */
    @PostMapping(value = {"/index"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Page<FnRmsv3MembersTypeRelateRespDTO>> index(@RequestBody @Validated FnRmsv3MembersTypeRelateQueryDTO bean) {
        return success(iFnRmsv3MembersTypeRelateService.query(bean));
    }

    /**
     * 根据id查询小散工程-组织团队成员关系
     *
     * @param bean 条件
     * @return {@link Result<FnRmsv3MembersTypeRelateRespDTO>}
     */
    @PostMapping(value = {"/show"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<FnRmsv3MembersTypeRelateRespDTO> show(@RequestBody @Validated ScShowDTO bean) {
        return success(iFnRmsv3MembersTypeRelateService.show(bean));
    }

    /**
     * 新建小散工程-组织团队成员关系
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/create"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> create(@RequestBody @Validated FnRmsv3MembersTypeRelateCreateDTO bean) {
        return success(Objects.nonNull(iFnRmsv3MembersTypeRelateService.create(bean)));
    }

    /**
     * 修改小散工程-组织团队成员关系
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/update"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> update(@RequestBody @Validated FnRmsv3MembersTypeRelateUpdateDTO bean) {
        return success(Objects.nonNull(iFnRmsv3MembersTypeRelateService.update(bean)));
    }



    /**
     * 修改小散工程-单独更新组织成员有效状态
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/update/status"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> updateStatus(@RequestBody @Validated FnRmsv3MembersTypeRelateUpdateDTO bean) {
        return success(Objects.nonNull(iFnRmsv3MembersTypeRelateService.updateStatus(bean)));
    }



    /**
     * 删除小散工程-组织团队成员关系
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/delete"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result delete(@RequestBody @Validated ScDeleteDTO bean) {
        iFnRmsv3MembersTypeRelateService.delete(bean.getIds());
        return success();
    }

    /**
     * 导出小散工程-组织团队成员关系
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/export"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result export(@RequestBody @Validated FnRmsv3MembersTypeRelateQueryDTO bean) {
        iFnRmsv3MembersTypeRelateService.export(bean);
        return success();
    }








    @PostMapping(value = {"/more/create"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> moreCreate(@RequestBody @Validated FnRmsv3MembersTypeRelateCreateDTO bean) {
        Assert.notNull(bean.getMemberIds(),"成员id为空");
        Assert.hasText(bean.getTypeId(),"类型id为空");
        iFnRmsv3MembersTypeRelateService.createMore(bean);
        return success();
    }


    /**
     * 获取当前用户能管理的所有组织
     * @return
     */
    @PostMapping(value = {"/show/all/organization"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<List<XsgcOrganizationRespDTO>> allOrganization() {
        return success(iFnRmsv3MembersTypeRelateService.allOrganization());
    }



    /**
     * 统计用户数量的接口
     * @return
     */
    @PostMapping(value = {"/statics"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<MemberRelateStatics> statics() {
        return success(iFnRmsv3MembersTypeRelateService.statics());
    }


    /**
     * 查询当前组织用户的关联区域
     */
    @PostMapping(value = {"/region"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<FnRmsv3MembersTypeRelateRespDTO> region() {
        return success(iFnRmsv3MembersTypeRelateService.getMemberRelateByUserAndOrganization());
    }



}
