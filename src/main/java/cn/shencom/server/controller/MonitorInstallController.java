package cn.shencom.server.controller;

import cn.shencom.model.dto.create.MonitorInstallCreateDTO;
import cn.shencom.model.dto.query.MonitorInstallQueryDTO;
import cn.shencom.model.dto.resp.MonitorInstallRespDTO;
import cn.shencom.model.dto.update.MonitorInstallUpdateDTO;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.dto.ScDeleteDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.server.controller.BaseController;
import cn.shencom.server.service.IMonitorInstallService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * 小散工程-监管工单流程-上门安装详情 的接口服务
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@RestController
@RequestMapping("/monitor/install")
public class MonitorInstallController extends BaseController {

    @Autowired
    private IMonitorInstallService iMonitorInstallService;

    /**
     * 查询小散工程-监管工单流程-上门安装详情列表
     *
     * @param bean 条件
     * @return {@link Result}<{@link Page}<{@link MonitorInstallRespDTO}>>
     */
    @PostMapping(value = {"/index"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Page<MonitorInstallRespDTO>> index(@RequestBody @Validated MonitorInstallQueryDTO bean) {
        return success(iMonitorInstallService.query(bean));
    }

    /**
     * 根据id查询小散工程-监管工单流程-上门安装详情
     *
     * @param bean 条件
     * @return {@link Result<MonitorInstallRespDTO>}
     */
    @PostMapping(value = {"/show"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<MonitorInstallRespDTO> show(@RequestBody @Validated ScShowDTO bean) {
        return success(iMonitorInstallService.show(bean));
    }

    /**
     * 新建小散工程-监管工单流程-上门安装详情
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/create"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> create(@RequestBody @Validated MonitorInstallCreateDTO bean) {
        return success(Objects.nonNull(iMonitorInstallService.create(bean)));
    }

    /**
     * 修改小散工程-监管工单流程-上门安装详情
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/update"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> update(@RequestBody @Validated MonitorInstallUpdateDTO bean) {
        return success(Objects.nonNull(iMonitorInstallService.update(bean)));
    }

    /**
     * 删除小散工程-监管工单流程-上门安装详情
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/delete"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result delete(@RequestBody @Validated ScDeleteDTO bean) {
        iMonitorInstallService.delete(bean.getIds());
        return success();
    }

    /**
     * 导出小散工程-监管工单流程-上门安装详情
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/export"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result export(@RequestBody @Validated MonitorInstallQueryDTO bean) {
        iMonitorInstallService.export(bean);
        return success();
    }

}
