package cn.shencom.server.controller;

import cn.shencom.model.dto.create.MonitorInstallReservationCreateDTO;
import cn.shencom.model.dto.query.MonitorInstallReservationQueryDTO;
import cn.shencom.model.dto.resp.MonitorInstallReservationRespDTO;
import cn.shencom.model.dto.update.MonitorInstallReservationUpdateDTO;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.dto.ScDeleteDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.server.controller.BaseController;
import cn.shencom.server.service.IMonitorInstallReservationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * 小散工程-监管工单流程-安装预约详情 的接口服务
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@RestController
@RequestMapping("/monitor/install/reservation")
public class MonitorInstallReservationController extends BaseController {

    @Autowired
    private IMonitorInstallReservationService iMonitorInstallReservationService;

    /**
     * 查询小散工程-监管工单流程-安装预约详情列表
     *
     * @param bean 条件
     * @return {@link Result}<{@link Page}<{@link MonitorInstallReservationRespDTO}>>
     */
    @PostMapping(value = {"/index"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Page<MonitorInstallReservationRespDTO>> index(@RequestBody @Validated MonitorInstallReservationQueryDTO bean) {
        return success(iMonitorInstallReservationService.query(bean));
    }

    /**
     * 根据id查询小散工程-监管工单流程-安装预约详情
     *
     * @param bean 条件
     * @return {@link Result<MonitorInstallReservationRespDTO>}
     */
    @PostMapping(value = {"/show"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<MonitorInstallReservationRespDTO> show(@RequestBody @Validated ScShowDTO bean) {
        return success(iMonitorInstallReservationService.show(bean));
    }

    /**
     * 新建小散工程-监管工单流程-安装预约详情
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/create"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> create(@RequestBody @Validated MonitorInstallReservationCreateDTO bean) {
        return success(Objects.nonNull(iMonitorInstallReservationService.create(bean)));
    }

    /**
     * 修改小散工程-监管工单流程-安装预约详情
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/update"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> update(@RequestBody @Validated MonitorInstallReservationUpdateDTO bean) {
        return success(Objects.nonNull(iMonitorInstallReservationService.update(bean)));
    }

    /**
     * 删除小散工程-监管工单流程-安装预约详情
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/delete"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result delete(@RequestBody @Validated ScDeleteDTO bean) {
        iMonitorInstallReservationService.delete(bean.getIds());
        return success();
    }

    /**
     * 导出小散工程-监管工单流程-安装预约详情
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/export"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result export(@RequestBody @Validated MonitorInstallReservationQueryDTO bean) {
        iMonitorInstallReservationService.export(bean);
        return success();
    }

}
