package cn.shencom.server.controller;

import cn.shencom.model.dto.create.EventOrderCreateDTO;
import cn.shencom.model.dto.query.EventOrderQueryDTO;
import cn.shencom.model.dto.query.EventOrderTypeQueryDTO;
import cn.shencom.model.dto.resp.ESEventOrderRespDTO;
import cn.shencom.model.dto.resp.EventOrderRespDTO;
import cn.shencom.model.dto.resp.EventOrderTypeRespDTO;
import cn.shencom.model.dto.update.EventOrderUpdateDTO;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.dto.ScDeleteDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.server.controller.BaseController;
import cn.shencom.server.service.IEventOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 事件工单表 的接口服务
 *
 * <AUTHOR>
 * @since 2021-06-29
 */
@RestController
@RequestMapping("/event/order")
public class EventOrderController extends BaseController {

    @Autowired
    private IEventOrderService iEventOrderService;

    /**
     * 查询事件工单表列表
     */
    @PostMapping(value = {"/index"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    @ResponseBody
    public Result<Page<ESEventOrderRespDTO>> index(@RequestBody @Validated EventOrderQueryDTO bean) throws Exception {
        return success(iEventOrderService.esQuery(bean));
    }

    /**
     * 查询事件工单表列表
     */
    @PostMapping(value = {"/es/index"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    @ResponseBody
    public Result<Page<ESEventOrderRespDTO>> esQuery(@RequestBody @Validated EventOrderQueryDTO bean) throws Exception {
        return success(iEventOrderService.esQuery(bean));
    }

    /**
     * 根据id查询事件工单表
     */
    @PostMapping(value = {"/show"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    @ResponseBody
    public Result<EventOrderRespDTO> show(@RequestBody @Validated ScShowDTO bean) {
        return success(iEventOrderService.show(bean));
    }

    /**
     * 新建事件工单表
     */
    @PostMapping(value = {"/create"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    @ResponseBody
    public Result<Boolean> create(@RequestBody @Validated EventOrderCreateDTO bean) {
        return success(Objects.nonNull(iEventOrderService.create(bean)));
    }

    /**
     * 修改事件工单表
     */
    @PostMapping(value = {"/update"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    @ResponseBody
    public Result<Boolean> update(@RequestBody @Validated EventOrderUpdateDTO bean) {
        return success(Objects.nonNull(iEventOrderService.update(bean)));
    }

    /**
     * 删除事件工单表
     */
    @PostMapping(value = {"/delete"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    @ResponseBody
    public Result delete(@RequestBody @Validated ScDeleteDTO bean) {
        iEventOrderService.delete(bean.getIds());
        return success();
    }

    /**
     * 导出事件工单表
     */
    @PostMapping(value = {"/export"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result export(@RequestBody @Validated EventOrderQueryDTO bean) {
        return success(iEventOrderService.export(bean));
    }



    /**
     * 查询监控事件类型列表
     *
     * @param bean 条件
     * @return {@link Result}<{@link List}<{@link EventOrderTypeRespDTO}>>
     */
    @PostMapping(value = {"/type/list"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<List<EventOrderTypeRespDTO>> index(@RequestBody @Validated EventOrderTypeQueryDTO bean) {
        Assert.notNull(bean.getTag(), "标记不能为空");
        return success(iEventOrderService.typeList(bean));
    }
}
