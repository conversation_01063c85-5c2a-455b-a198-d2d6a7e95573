package cn.shencom.server.controller;

import cn.shencom.common.util.DSConnection;
import cn.shencom.model.dto.create.AimSceneCategoryManagementCreateDTO;
import cn.shencom.model.dto.query.AimSceneCategoryManagementQueryDTO;
import cn.shencom.model.dto.resp.AimSceneCategoryDTO;
import cn.shencom.model.dto.resp.AimSceneCategoryManagementRespDTO;
import cn.shencom.model.dto.update.AimSceneCategoryManagementUpdateDTO;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.dto.ScDeleteDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.server.controller.BaseController;
import cn.shencom.server.service.IAimSceneCategoryManagementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * 场景类别管理表 的接口服务
 *
 * <AUTHOR>
 * @since 2022-07-26
 */
@RestController
@RequestMapping("/aim/scene/category/management")
public class AimSceneCategoryManagementController extends BaseController {

    @Autowired
    private IAimSceneCategoryManagementService iAimSceneCategoryManagementService;

    /**
     * 查询场景类别管理表列表
     *
     * @param bean 条件
     * @return {@link Result}<{@link Page}<{@link AimSceneCategoryManagementRespDTO}>>
     */
    @PostMapping(value = {"/index"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Page<AimSceneCategoryManagementRespDTO>> index(@RequestBody @Validated AimSceneCategoryManagementQueryDTO bean) {
        return success(iAimSceneCategoryManagementService.query(bean));
    }

    /**
     * 根据id查询场景类别管理表
     *
     * @param bean 条件
     * @return {@link Result<AimSceneCategoryManagementRespDTO>}
     */
    @PostMapping(value = {"/show"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<AimSceneCategoryManagementRespDTO> show(@RequestBody @Validated ScShowDTO bean) {
        return success(iAimSceneCategoryManagementService.show(bean));
    }

    /**
     * 新建场景类别管理表
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/create"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> create(@RequestBody @Validated AimSceneCategoryManagementCreateDTO bean) {
        return success(Objects.nonNull(iAimSceneCategoryManagementService.create(bean)));
    }

    /**
     * 修改场景类别管理表
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/update"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> update(@RequestBody @Validated AimSceneCategoryManagementUpdateDTO bean) {
        return success(Objects.nonNull(iAimSceneCategoryManagementService.update(bean)));
    }

    /**
     * 删除场景类别管理表
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/delete"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result delete(@RequestBody @Validated ScDeleteDTO bean) {
        iAimSceneCategoryManagementService.delete(bean.getIds());
        return success();
    }

    /**
     * 导出场景类别管理表
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/export"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result export(@RequestBody @Validated AimSceneCategoryManagementQueryDTO bean) {
        iAimSceneCategoryManagementService.export(bean);
        return success();
    }

    /**
     * 获取所有场景类别
     *
     * @return {@link Result}
     */
    @PostMapping(value = {"/category/all"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    @DSConnection(onlyRead = true)
    public Result<List<AimSceneCategoryDTO>> getAllCategory() {
        return success(iAimSceneCategoryManagementService.getAllCategory());
    }

}
