package cn.shencom.server.controller;

import cn.shencom.model.dto.create.SysRoleUserOrganizationCreateDTO;
import cn.shencom.model.dto.query.SysRoleUserOrganizationQueryDTO;
import cn.shencom.model.dto.resp.SysRoleUserOrganizationRespDTO;
import cn.shencom.model.dto.resp.XsgcOrganizationRespDTO;
import cn.shencom.model.dto.update.SysRoleUserOrganizationUpdateDTO;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.dto.ScDeleteDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.server.controller.BaseController;
import cn.shencom.server.service.ISysRoleUserOrganizationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 系统角色，权限，组织关联表 的接口服务
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@RestController
@RequestMapping("/sys/role/user/organization")
public class SysRoleUserOrganizationController extends BaseController {

    @Autowired
    private ISysRoleUserOrganizationService iSysRoleUserOrganizationService;

    /**
     * 查询系统角色，权限，组织关联表列表
     *
     * @param bean 条件
     * @return {@link Result}<{@link Page}<{@link SysRoleUserOrganizationRespDTO}>>
     */
    @PostMapping(value = {"/index"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Page<SysRoleUserOrganizationRespDTO>> index(@RequestBody @Validated SysRoleUserOrganizationQueryDTO bean) {
        return success(iSysRoleUserOrganizationService.query(bean));
    }

    /**
     * 根据id查询系统角色，权限，组织关联表
     *
     * @param bean 条件
     * @return {@link Result<SysRoleUserOrganizationRespDTO>}
     */
    @PostMapping(value = {"/show"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<SysRoleUserOrganizationRespDTO> show(@RequestBody @Validated ScShowDTO bean) {
        return success(iSysRoleUserOrganizationService.show(bean));
    }

    /**
     * 新建系统角色，权限，组织关联表
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/create"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> create(@RequestBody @Validated SysRoleUserOrganizationCreateDTO bean) {
        return success(Objects.nonNull(iSysRoleUserOrganizationService.create(bean)));
    }

    /**
     * 修改系统角色，权限，组织关联表
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/update"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> update(@RequestBody @Validated SysRoleUserOrganizationUpdateDTO bean) {
        return success(Objects.nonNull(iSysRoleUserOrganizationService.update(bean)));
    }

    /**
     * 删除系统角色，权限，组织关联表
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/delete"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result delete(@RequestBody @Validated ScDeleteDTO bean) {
        iSysRoleUserOrganizationService.delete(bean.getIds());
        return success();
    }

    /**
     * 导出系统角色，权限，组织关联表
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/export"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result export(@RequestBody @Validated SysRoleUserOrganizationQueryDTO bean) {
        iSysRoleUserOrganizationService.export(bean);
        return success();
    }



    /**
     *  切换组织
     *  并且记录用户上一次选择的组织
     */
    @PostMapping(value = {"/switch"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<XsgcOrganizationRespDTO> switchOrganization(@RequestBody SysRoleUserOrganizationQueryDTO bean) {
        return success(iSysRoleUserOrganizationService.switchOrganization(bean));
    }


    /**
     * 获取当前用户关联的角色
     */
    @PostMapping(value = {"/all/roles"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<List<SysRoleUserOrganizationRespDTO>> getAllRoles(@RequestBody SysRoleUserOrganizationQueryDTO bean) {
        return success(iSysRoleUserOrganizationService.getAllRoles(bean));
    }


}
