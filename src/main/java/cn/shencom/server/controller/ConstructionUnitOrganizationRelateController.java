package cn.shencom.server.controller;

import cn.shencom.model.dto.create.ConstructionUnitOrganizationRelateCreateDTO;
import cn.shencom.model.dto.query.ConstructionUnitOrganizationRelateQueryDTO;
import cn.shencom.model.dto.resp.ConstructionUnitOrganizationRelateRespDTO;
import cn.shencom.model.dto.update.ConstructionUnitOrganizationRelateUpdateDTO;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.dto.ScDeleteDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.server.controller.BaseController;
import cn.shencom.server.service.IConstructionUnitOrganizationRelateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * 施工单位组织关联表 的接口服务
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@RestController
@RequestMapping("/construction/unit/organization/relate")
public class ConstructionUnitOrganizationRelateController extends BaseController {

    @Autowired
    private IConstructionUnitOrganizationRelateService iConstructionUnitOrganizationRelateService;

    /**
     * 查询施工单位组织关联表列表
     *
     * @param bean 条件
     * @return {@link Result}<{@link Page}<{@link ConstructionUnitOrganizationRelateRespDTO}>>
     */
    @PostMapping(value = { "/index" }, produces = { MediaType.APPLICATION_JSON_UTF8_VALUE })
    public Result<Page<ConstructionUnitOrganizationRelateRespDTO>> index(
            @RequestBody @Validated ConstructionUnitOrganizationRelateQueryDTO bean) {
        return success(iConstructionUnitOrganizationRelateService.query(bean));
    }

    /**
     * 根据id查询施工单位组织关联表
     *
     * @param bean 条件
     * @return {@link Result<ConstructionUnitOrganizationRelateRespDTO>}
     */
    @PostMapping(value = { "/show" }, produces = { MediaType.APPLICATION_JSON_UTF8_VALUE })
    public Result<ConstructionUnitOrganizationRelateRespDTO> show(@RequestBody @Validated ScShowDTO bean) {
        return success(iConstructionUnitOrganizationRelateService.show(bean));
    }

    /**
     * 新建施工单位组织关联表
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = { "/create" }, produces = { MediaType.APPLICATION_JSON_UTF8_VALUE })
    public Result<Boolean> create(@RequestBody @Validated ConstructionUnitOrganizationRelateCreateDTO bean) {
        return success(Objects.nonNull(iConstructionUnitOrganizationRelateService.create(bean)));
    }

    /**
     * 修改施工单位组织关联表
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = { "/update" }, produces = { MediaType.APPLICATION_JSON_UTF8_VALUE })
    public Result<Boolean> update(@RequestBody @Validated ConstructionUnitOrganizationRelateUpdateDTO bean) {
        return success(Objects.nonNull(iConstructionUnitOrganizationRelateService.update(bean)));
    }

    /**
     * 删除施工单位组织关联表
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = { "/delete" }, produces = { MediaType.APPLICATION_JSON_UTF8_VALUE })
    public Result delete(@RequestBody @Validated ScDeleteDTO bean) {
        iConstructionUnitOrganizationRelateService.delete(bean.getIds());
        return success();
    }

    /**
     * 导出施工单位组织关联表
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = { "/export" }, produces = { MediaType.APPLICATION_JSON_UTF8_VALUE })
    public Result export(@RequestBody @Validated ConstructionUnitOrganizationRelateQueryDTO bean) {
        iConstructionUnitOrganizationRelateService.export(bean);
        return success();
    }

}
