package cn.shencom.server.controller;

import cn.shencom.model.dto.create.EventModelTypeConfigCreateDTO;
import cn.shencom.model.dto.query.EventModelTypeConfigQueryDTO;
import cn.shencom.model.dto.resp.EventModelTypeConfigRespDTO;
import cn.shencom.model.dto.update.EventModelTypeConfigUpdateDTO;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.dto.ScDeleteDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.server.controller.BaseController;
import cn.shencom.server.service.IEventModelTypeConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * 设备型号配置表 的接口服务
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@RestController
@RequestMapping("/event/model/type/config")
public class EventModelTypeConfigController extends BaseController {

    @Autowired
    private IEventModelTypeConfigService iEventModelTypeConfigService;

    /**
     * 查询设备型号配置表列表
     *
     * @param bean 条件
     * @return {@link Result}<{@link Page}<{@link EventModelTypeConfigRespDTO}>>
     */
    @PostMapping(value = {"/index"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Page<EventModelTypeConfigRespDTO>> index(@RequestBody @Validated EventModelTypeConfigQueryDTO bean) {
        return success(iEventModelTypeConfigService.query(bean));
    }

    /**
     * 根据id查询设备型号配置表
     *
     * @param bean 条件
     * @return {@link Result<EventModelTypeConfigRespDTO>}
     */
    @PostMapping(value = {"/show"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<EventModelTypeConfigRespDTO> show(@RequestBody @Validated ScShowDTO bean) {
        return success(iEventModelTypeConfigService.show(bean));
    }

    /**
     * 新建设备型号配置表
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/create"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> create(@RequestBody @Validated EventModelTypeConfigCreateDTO bean) {
        return success(Objects.nonNull(iEventModelTypeConfigService.create(bean)));
    }

    /**
     * 修改设备型号配置表
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/update"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> update(@RequestBody @Validated EventModelTypeConfigUpdateDTO bean) {
        return success(Objects.nonNull(iEventModelTypeConfigService.update(bean)));
    }

    /**
     * 删除设备型号配置表
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/delete"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result delete(@RequestBody @Validated ScDeleteDTO bean) {
        iEventModelTypeConfigService.delete(bean.getIds());
        return success();
    }

    /**
     * 导出设备型号配置表
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/export"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result export(@RequestBody @Validated EventModelTypeConfigQueryDTO bean) {
        iEventModelTypeConfigService.export(bean);
        return success();
    }

}
