package cn.shencom.server.controller;

import cn.shencom.model.dto.create.FnRmsv3MembersTypeRelateBindingCreateDTO;
import cn.shencom.model.dto.query.FnRmsv3MembersTypeRelateBindingQueryDTO;
import cn.shencom.model.dto.resp.FnRmsv3MembersTypeRelateBindingRespDTO;
import cn.shencom.model.dto.update.FnRmsv3MembersTypeRelateBindingUpdateDTO;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.dto.ScDeleteDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.server.controller.BaseController;
import cn.shencom.server.service.IFnRmsv3MembersTypeRelateBindingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * 小散工程-组织团队成员区域关联关系 的接口服务
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@RestController
@RequestMapping("/fn/rmsv3/members/type/relate/binding")
public class FnRmsv3MembersTypeRelateBindingController extends BaseController {

    @Autowired
    private IFnRmsv3MembersTypeRelateBindingService iFnRmsv3MembersTypeRelateBindingService;

    /**
     * 查询小散工程-组织团队成员区域关联关系列表
     *
     * @param bean 条件
     * @return {@link Result}<{@link Page}<{@link FnRmsv3MembersTypeRelateBindingRespDTO}>>
     */
    @PostMapping(value = {"/index"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Page<FnRmsv3MembersTypeRelateBindingRespDTO>> index(@RequestBody @Validated FnRmsv3MembersTypeRelateBindingQueryDTO bean) {
        return success(iFnRmsv3MembersTypeRelateBindingService.query(bean));
    }

    /**
     * 根据id查询小散工程-组织团队成员区域关联关系
     *
     * @param bean 条件
     * @return {@link Result<FnRmsv3MembersTypeRelateBindingRespDTO>}
     */
    @PostMapping(value = {"/show"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<FnRmsv3MembersTypeRelateBindingRespDTO> show(@RequestBody @Validated ScShowDTO bean) {
        return success(iFnRmsv3MembersTypeRelateBindingService.show(bean));
    }

    /**
     * 新建小散工程-组织团队成员区域关联关系
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/create"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> create(@RequestBody @Validated FnRmsv3MembersTypeRelateBindingCreateDTO bean) {
        return success(Objects.nonNull(iFnRmsv3MembersTypeRelateBindingService.create(bean)));
    }

    /**
     * 修改小散工程-组织团队成员区域关联关系
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/update"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> update(@RequestBody @Validated FnRmsv3MembersTypeRelateBindingUpdateDTO bean) {
        return success(Objects.nonNull(iFnRmsv3MembersTypeRelateBindingService.update(bean)));
    }

    /**
     * 删除小散工程-组织团队成员区域关联关系
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/delete"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result delete(@RequestBody @Validated ScDeleteDTO bean) {
        iFnRmsv3MembersTypeRelateBindingService.delete(bean.getIds());
        return success();
    }

    /**
     * 导出小散工程-组织团队成员区域关联关系
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/export"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result export(@RequestBody @Validated FnRmsv3MembersTypeRelateBindingQueryDTO bean) {
        iFnRmsv3MembersTypeRelateBindingService.export(bean);
        return success();
    }

}
