package cn.shencom.server.controller;

import cn.shencom.model.dto.create.EngineeringMembersCreateDTO;
import cn.shencom.model.dto.query.EngineeringMembersQueryDTO;
import cn.shencom.model.dto.resp.EngineeringMembersRespDTO;
import cn.shencom.model.dto.resp.SporadicProjectRespDTO;
import cn.shencom.model.dto.update.EngineeringMembersUpdateDTO;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.dto.ScDeleteDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.server.controller.BaseController;
import cn.shencom.server.service.IEngineeringMembersService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * 小散工程-工程人员 的接口服务
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@RestController
@RequestMapping("/engineering/members")
public class EngineeringMembersController extends BaseController {

    @Autowired
    private IEngineeringMembersService iEngineeringMembersService;

    /**
     * 查询小散工程-工程人员列表
     *
     * @param bean 条件
     * @return {@link Result}<{@link Page}<{@link EngineeringMembersRespDTO}>>
     */
    @PostMapping(value = {"/index"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Page<EngineeringMembersRespDTO>> index(@RequestBody @Validated EngineeringMembersQueryDTO bean) {
        return success(iEngineeringMembersService.query(bean));
    }

    /**
     * 根据id查询小散工程-工程人员
     *
     * @param bean 条件
     * @return {@link Result<EngineeringMembersRespDTO>}
     */
    @PostMapping(value = {"/show"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<EngineeringMembersRespDTO> show(@RequestBody @Validated ScShowDTO bean) {
        return success(iEngineeringMembersService.show(bean));
    }

    /**
     * 新建小散工程-工程人员
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/create"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> create(@RequestBody @Validated EngineeringMembersCreateDTO bean) {
        return success(Objects.nonNull(iEngineeringMembersService.create(bean)));
    }

    /**
     * 修改小散工程-工程人员
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/update"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> update(@RequestBody @Validated EngineeringMembersUpdateDTO bean) {
        return success(Objects.nonNull(iEngineeringMembersService.update(bean)));
    }

    /**
     * 删除小散工程-工程人员
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/delete"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result delete(@RequestBody @Validated ScDeleteDTO bean) {
        iEngineeringMembersService.delete(bean.getIds());
        return success();
    }

    /**
     * 导出小散工程-工程人员
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/export"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result export(@RequestBody @Validated EngineeringMembersQueryDTO bean) {
        iEngineeringMembersService.export(bean);
        return success();
    }



    /**
     * 查询当前成员关联的项目列表
     */
    @PostMapping(value = {"/project/page"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Page<SporadicProjectRespDTO>> projectPage(@RequestBody @Validated EngineeringMembersQueryDTO bean) {
        return success(iEngineeringMembersService.projectPage(bean));
    }

}
