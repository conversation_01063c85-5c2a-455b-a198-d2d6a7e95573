package cn.shencom.server.controller;

import cn.shencom.model.dto.create.XsgcBusinessMembersRelateCreateDTO;
import cn.shencom.model.dto.query.XsgcBusinessMembersRelateQueryDTO;
import cn.shencom.model.dto.resp.XsgcBusinessMembersRelateRespDTO;
import cn.shencom.model.dto.update.XsgcBusinessMembersRelateUpdateDTO;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.dto.ScDeleteDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.server.controller.BaseController;
import cn.shencom.server.service.IXsgcBusinessMembersRelateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 小散工程-业务人员客户关联表 的接口服务
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@RestController
@RequestMapping("/xsgc/business/members/relate")
public class XsgcBusinessMembersRelateController extends BaseController {

    @Autowired
    private IXsgcBusinessMembersRelateService iXsgcBusinessMembersRelateService;

    /**
     * 查询小散工程-业务人员客户关联表列表
     *
     * @param bean 条件
     * @return {@link Result}<{@link Page}<{@link XsgcBusinessMembersRelateRespDTO}>>
     */
    @PostMapping(value = {"/index"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Page<XsgcBusinessMembersRelateRespDTO>> index(@RequestBody @Validated XsgcBusinessMembersRelateQueryDTO bean) {
        return success(iXsgcBusinessMembersRelateService.query(bean));
    }

    /**
     * 根据id查询小散工程-业务人员客户关联表
     *
     * @param bean 条件
     * @return {@link Result<XsgcBusinessMembersRelateRespDTO>}
     */
    @PostMapping(value = {"/show"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<XsgcBusinessMembersRelateRespDTO> show(@RequestBody @Validated ScShowDTO bean) {
        return success(iXsgcBusinessMembersRelateService.show(bean));
    }

    /**
     * 新建小散工程-业务人员客户关联表
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/create"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> create(@RequestBody @Validated XsgcBusinessMembersRelateCreateDTO bean) {
        return success(Objects.nonNull(iXsgcBusinessMembersRelateService.create(bean)));
    }

    /**
     * 修改小散工程-业务人员客户关联表
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/update"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> update(@RequestBody @Validated XsgcBusinessMembersRelateUpdateDTO bean) {
        return success(Objects.nonNull(iXsgcBusinessMembersRelateService.update(bean)));
    }

    /**
     * 删除小散工程-业务人员客户关联表
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/delete"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result delete(@RequestBody @Validated ScDeleteDTO bean) {
        iXsgcBusinessMembersRelateService.delete(bean.getIds());
        return success();
    }

    /**
     * 导出小散工程-业务人员客户关联表
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/export"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result export(@RequestBody @Validated XsgcBusinessMembersRelateQueryDTO bean) {
        iXsgcBusinessMembersRelateService.export(bean);
        return success();
    }



    /**
     *   关联业务人员
     */
    @PostMapping(value = {"/relevance"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result relevance(@RequestBody XsgcBusinessMembersRelateUpdateDTO bean) {
        iXsgcBusinessMembersRelateService.relevance(bean);
        return success();
    }



    /**
     *   关联业务人员
     *   同时关联多种角色
     */
    @PostMapping(value = {"/more/relevance"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result moreRelevance(@RequestBody List<XsgcBusinessMembersRelateUpdateDTO> bean) {
        iXsgcBusinessMembersRelateService.moreRelevance(bean);
        return success();
    }




    /**
     * 查询已关联的业务人员
     */
    @PostMapping(value = {"/relevance/index"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Page<XsgcBusinessMembersRelateRespDTO>> relevanceIndex(@RequestBody @Validated XsgcBusinessMembersRelateQueryDTO bean) {
        return success(iXsgcBusinessMembersRelateService.relevanceIndex(bean));
    }


    /**
     * 查询未关联的业务人员
     */
    @PostMapping(value = {"/not/relevance/index"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Page<XsgcBusinessMembersRelateRespDTO>> notRelevanceIndex(@RequestBody @Validated XsgcBusinessMembersRelateQueryDTO bean) {
        return success(iXsgcBusinessMembersRelateService.notRelevanceIndex(bean));
    }

}
