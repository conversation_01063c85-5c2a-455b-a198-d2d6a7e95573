package cn.shencom.server.controller;

import cn.shencom.model.dto.create.XsgcBusinessMembersCreateDTO;
import cn.shencom.model.dto.query.XsgcBusinessMembersQueryDTO;
import cn.shencom.model.dto.resp.XsgcBusinessMembersRespDTO;
import cn.shencom.model.dto.update.XsgcBusinessMembersUpdateDTO;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.dto.ScDeleteDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.server.controller.BaseController;
import cn.shencom.server.service.IXsgcBusinessMembersService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * 小散工程-业务人员 的接口服务
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@RestController
@RequestMapping("/xsgc/business/members")
public class XsgcBusinessMembersController extends BaseController {

    @Autowired
    private IXsgcBusinessMembersService iXsgcBusinessMembersService;

    /**
     * 查询小散工程-业务人员列表
     *
     * @param bean 条件
     * @return {@link Result}<{@link Page}<{@link XsgcBusinessMembersRespDTO}>>
     */
    @PostMapping(value = {"/index"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Page<XsgcBusinessMembersRespDTO>> index(@RequestBody @Validated XsgcBusinessMembersQueryDTO bean) {
        return success(iXsgcBusinessMembersService.query(bean));
    }

    /**
     * 根据id查询小散工程-业务人员
     *
     * @param bean 条件
     * @return {@link Result<XsgcBusinessMembersRespDTO>}
     */
    @PostMapping(value = {"/show"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<XsgcBusinessMembersRespDTO> show(@RequestBody @Validated ScShowDTO bean) {
        return success(iXsgcBusinessMembersService.show(bean));
    }

    /**
     * 新建小散工程-业务人员
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/create"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> create(@RequestBody @Validated XsgcBusinessMembersCreateDTO bean) {
        return success(Objects.nonNull(iXsgcBusinessMembersService.create(bean)));
    }

    /**
     * 修改小散工程-业务人员
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/update"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> update(@RequestBody @Validated XsgcBusinessMembersUpdateDTO bean) {
        return success(Objects.nonNull(iXsgcBusinessMembersService.update(bean)));
    }

    /**
     * 删除小散工程-业务人员
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/delete"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result delete(@RequestBody @Validated ScDeleteDTO bean) {
        iXsgcBusinessMembersService.delete(bean.getIds());
        return success();
    }

    /**
     * 导出小散工程-业务人员
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/export"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result export(@RequestBody @Validated XsgcBusinessMembersQueryDTO bean) {
        iXsgcBusinessMembersService.export(bean);
        return success();
    }

}
