package cn.shencom.server.controller;

import cn.shencom.model.dto.create.MonitorRecycleCreateDTO;
import cn.shencom.model.dto.query.MonitorRecycleQueryDTO;
import cn.shencom.model.dto.resp.MonitorRecycleRespDTO;
import cn.shencom.model.dto.update.MonitorRecycleUpdateDTO;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.dto.ScDeleteDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.server.controller.BaseController;
import cn.shencom.server.service.IMonitorRecycleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * 小散工程-监管工单流程-上门回收详情 的接口服务
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@RestController
@RequestMapping("/monitor/recycle")
public class MonitorRecycleController extends BaseController {

    @Autowired
    private IMonitorRecycleService iMonitorRecycleService;

    /**
     * 查询小散工程-监管工单流程-上门回收详情列表
     *
     * @param bean 条件
     * @return {@link Result}<{@link Page}<{@link MonitorRecycleRespDTO}>>
     */
    @PostMapping(value = {"/index"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Page<MonitorRecycleRespDTO>> index(@RequestBody @Validated MonitorRecycleQueryDTO bean) {
        return success(iMonitorRecycleService.query(bean));
    }

    /**
     * 根据id查询小散工程-监管工单流程-上门回收详情
     *
     * @param bean 条件
     * @return {@link Result<MonitorRecycleRespDTO>}
     */
    @PostMapping(value = {"/show"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<MonitorRecycleRespDTO> show(@RequestBody @Validated ScShowDTO bean) {
        return success(iMonitorRecycleService.show(bean));
    }

    /**
     * 新建小散工程-监管工单流程-上门回收详情
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/create"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> create(@RequestBody @Validated MonitorRecycleCreateDTO bean) {
        return success(Objects.nonNull(iMonitorRecycleService.create(bean)));
    }

    /**
     * 修改小散工程-监管工单流程-上门回收详情
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/update"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> update(@RequestBody @Validated MonitorRecycleUpdateDTO bean) {
        return success(Objects.nonNull(iMonitorRecycleService.update(bean)));
    }

    /**
     * 删除小散工程-监管工单流程-上门回收详情
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/delete"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result delete(@RequestBody @Validated ScDeleteDTO bean) {
        iMonitorRecycleService.delete(bean.getIds());
        return success();
    }

    /**
     * 导出小散工程-监管工单流程-上门回收详情
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/export"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result export(@RequestBody @Validated MonitorRecycleQueryDTO bean) {
        iMonitorRecycleService.export(bean);
        return success();
    }

}
