package cn.shencom.server.controller;

import cn.shencom.model.dto.create.MonitorAccessInfoCreateDTO;
import cn.shencom.model.dto.query.MonitorAccessInfoQueryDTO;
import cn.shencom.model.dto.resp.MonitorAccessInfoRespDTO;
import cn.shencom.model.dto.update.MonitorAccessInfoUpdateDTO;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.dto.ScDeleteDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.server.controller.BaseController;
import cn.shencom.server.service.IMonitorAccessInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * 小散工程-监管工单流程-监控接入详情 的接口服务
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@RestController
@RequestMapping("/monitor/access/info")
public class MonitorAccessInfoController extends BaseController {

    @Autowired
    private IMonitorAccessInfoService iMonitorAccessInfoService;

    /**
     * 查询小散工程-监管工单流程-监控接入详情列表
     *
     * @param bean 条件
     * @return {@link Result}<{@link Page}<{@link MonitorAccessInfoRespDTO}>>
     */
    @PostMapping(value = {"/index"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Page<MonitorAccessInfoRespDTO>> index(@RequestBody @Validated MonitorAccessInfoQueryDTO bean) {
        return success(iMonitorAccessInfoService.query(bean));
    }

    /**
     * 根据id查询小散工程-监管工单流程-监控接入详情
     *
     * @param bean 条件
     * @return {@link Result<MonitorAccessInfoRespDTO>}
     */
    @PostMapping(value = {"/show"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<MonitorAccessInfoRespDTO> show(@RequestBody @Validated ScShowDTO bean) {
        return success(iMonitorAccessInfoService.show(bean));
    }

    /**
     * 新建小散工程-监管工单流程-监控接入详情
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/create"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> create(@RequestBody @Validated MonitorAccessInfoCreateDTO bean) {
        return success(Objects.nonNull(iMonitorAccessInfoService.create(bean)));
    }

    /**
     * 修改小散工程-监管工单流程-监控接入详情
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/update"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> update(@RequestBody @Validated MonitorAccessInfoUpdateDTO bean) {
        return success(Objects.nonNull(iMonitorAccessInfoService.update(bean)));
    }

    /**
     * 删除小散工程-监管工单流程-监控接入详情
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/delete"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result delete(@RequestBody @Validated ScDeleteDTO bean) {
        iMonitorAccessInfoService.delete(bean.getIds());
        return success();
    }

    /**
     * 导出小散工程-监管工单流程-监控接入详情
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/export"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result export(@RequestBody @Validated MonitorAccessInfoQueryDTO bean) {
        iMonitorAccessInfoService.export(bean);
        return success();
    }

}
