package cn.shencom.server.controller;

import cn.shencom.model.dto.create.XsgcMessageRemindCreateDTO;
import cn.shencom.model.dto.query.XsgcMessageRemindQueryDTO;
import cn.shencom.model.dto.resp.XsgcMessageRemindRespDTO;
import cn.shencom.model.dto.update.XsgcMessageRemindUpdateDTO;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.dto.ScDeleteDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.server.controller.BaseController;
import cn.shencom.server.service.IXsgcMessageRemindService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * 小散工程-消息提醒表 的接口服务
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@RestController
@RequestMapping("/xsgc/message/remind")
public class XsgcMessageRemindController extends BaseController {

    @Autowired
    private IXsgcMessageRemindService iXsgcMessageRemindService;

    /**
     * 查询小散工程-消息提醒表列表
     *
     * @param bean 条件
     * @return {@link Result}<{@link Page}<{@link XsgcMessageRemindRespDTO}>>
     */
    @PostMapping(value = {"/index"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Page<XsgcMessageRemindRespDTO>> index(@RequestBody @Validated XsgcMessageRemindQueryDTO bean) {
        return success(iXsgcMessageRemindService.query(bean));
    }

    /**
     * 根据id查询小散工程-消息提醒表
     *
     * @param bean 条件
     * @return {@link Result<XsgcMessageRemindRespDTO>}
     */
    @PostMapping(value = {"/show"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<XsgcMessageRemindRespDTO> show(@RequestBody @Validated ScShowDTO bean) {
        return success(iXsgcMessageRemindService.show(bean));
    }

    /**
     * 新建小散工程-消息提醒表
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/create"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> create(@RequestBody @Validated XsgcMessageRemindCreateDTO bean) {
        return success(Objects.nonNull(iXsgcMessageRemindService.create(bean)));
    }

    /**
     * 修改小散工程-消息提醒表
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/update"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> update(@RequestBody @Validated XsgcMessageRemindUpdateDTO bean) {
        return success(Objects.nonNull(iXsgcMessageRemindService.update(bean)));
    }

    /**
     * 删除小散工程-消息提醒表
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/delete"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result delete(@RequestBody @Validated ScDeleteDTO bean) {
        iXsgcMessageRemindService.delete(bean.getIds());
        return success();
    }

    /**
     * 导出小散工程-消息提醒表
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/export"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result export(@RequestBody @Validated XsgcMessageRemindQueryDTO bean) {
        iXsgcMessageRemindService.export(bean);
        return success();
    }

}
