package cn.shencom.server.controller;

import cn.shencom.model.dto.create.FnRmsv3MembersCreateDTO;
import cn.shencom.model.dto.query.FnRmsv3MembersQueryDTO;
import cn.shencom.model.dto.resp.FnRmsv3MembersRespDTO;
import cn.shencom.model.dto.update.FnRmsv3MembersUpdateDTO;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.dto.ScDeleteDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.server.controller.BaseController;
import cn.shencom.server.service.IFnRmsv3MembersService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * 小散工程-组织团队成员表 的接口服务
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@RestController
@RequestMapping("/fn/rmsv3/members")
public class FnRmsv3MembersController extends BaseController {

    @Autowired
    private IFnRmsv3MembersService iFnRmsv3MembersService;

    /**
     * 查询小散工程-组织团队成员表列表
     *
     * @param bean 条件
     * @return {@link Result}<{@link Page}<{@link FnRmsv3MembersRespDTO}>>
     */
    @PostMapping(value = {"/index"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Page<FnRmsv3MembersRespDTO>> index(@RequestBody @Validated FnRmsv3MembersQueryDTO bean) {
        return success(iFnRmsv3MembersService.query(bean));
    }

    /**
     * 根据id查询小散工程-组织团队成员表
     *
     * @param bean 条件
     * @return {@link Result<FnRmsv3MembersRespDTO>}
     */
    @PostMapping(value = {"/show"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<FnRmsv3MembersRespDTO> show(@RequestBody @Validated ScShowDTO bean) {
        return success(iFnRmsv3MembersService.show(bean));
    }

    /**
     * 新建小散工程-组织团队成员表
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/create"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> create(@RequestBody @Validated FnRmsv3MembersCreateDTO bean) {
        return success(Objects.nonNull(iFnRmsv3MembersService.create(bean)));
    }

    /**
     * 修改小散工程-组织团队成员表
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/update"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> update(@RequestBody @Validated FnRmsv3MembersUpdateDTO bean) {
        return success(Objects.nonNull(iFnRmsv3MembersService.update(bean)));
    }

    /**
     * 删除小散工程-组织团队成员表
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/delete"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result delete(@RequestBody @Validated ScDeleteDTO bean) {
        iFnRmsv3MembersService.delete(bean.getIds());
        return success();
    }

    /**
     * 导出小散工程-组织团队成员表
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/export"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result export(@RequestBody @Validated FnRmsv3MembersQueryDTO bean) {
        iFnRmsv3MembersService.export(bean);
        return success();
    }

}
