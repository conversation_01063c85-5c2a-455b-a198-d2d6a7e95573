package cn.shencom.server.controller;

import cn.shencom.model.dto.create.ContractingUnitCreateDTO;
import cn.shencom.model.dto.query.ContractingUnitQueryDTO;
import cn.shencom.model.dto.resp.ContractingUnitRespDTO;
import cn.shencom.model.dto.update.ContractingUnitUpdateDTO;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.dto.ScDeleteDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.server.controller.BaseController;
import cn.shencom.server.service.IContractingUnitService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * 施工单位表 的接口服务
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@RestController
@RequestMapping("/contracting/unit")
public class ContractingUnitController extends BaseController {

    @Autowired
    private IContractingUnitService iContractingUnitService;

    /**
     * 查询施工单位表列表
     *
     * @param bean 条件
     * @return {@link Result}<{@link Page}<{@link ContractingUnitRespDTO}>>
     */
    @PostMapping(value = { "/index" }, produces = { MediaType.APPLICATION_JSON_UTF8_VALUE })
    public Result<Page<ContractingUnitRespDTO>> index(@RequestBody @Validated ContractingUnitQueryDTO bean) {
        return success(iContractingUnitService.query(bean));
    }

    /**
     * 根据id查询施工单位表
     *
     * @param bean 条件
     * @return {@link Result<ContractingUnitRespDTO>}
     */
    @PostMapping(value = { "/show" }, produces = { MediaType.APPLICATION_JSON_UTF8_VALUE })
    public Result<ContractingUnitRespDTO> show(@RequestBody @Validated ScShowDTO bean) {
        return success(iContractingUnitService.show(bean));
    }

    /**
     * 新建施工单位表
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = { "/create" }, produces = { MediaType.APPLICATION_JSON_UTF8_VALUE })
    public Result<Boolean> create(@RequestBody @Validated ContractingUnitCreateDTO bean) {
        return success(Objects.nonNull(iContractingUnitService.create(bean)));
    }

    /**
     * 修改施工单位表
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = { "/update" }, produces = { MediaType.APPLICATION_JSON_UTF8_VALUE })
    public Result<Boolean> update(@RequestBody @Validated ContractingUnitUpdateDTO bean) {
        return success(Objects.nonNull(iContractingUnitService.update(bean)));
    }

    /**
     * 删除施工单位表
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = { "/delete" }, produces = { MediaType.APPLICATION_JSON_UTF8_VALUE })
    public Result delete(@RequestBody @Validated ScDeleteDTO bean) {
        iContractingUnitService.delete(bean.getIds());
        return success();
    }

    /**
     * 导出施工单位表
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = { "/export" }, produces = { MediaType.APPLICATION_JSON_UTF8_VALUE })
    public Result export(@RequestBody @Validated ContractingUnitQueryDTO bean) {
        iContractingUnitService.export(bean);
        return success();
    }

}
