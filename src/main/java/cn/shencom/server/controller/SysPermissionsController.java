package cn.shencom.server.controller;

import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.server.controller.BaseController;
import cn.shencom.server.service.ISysPermissionsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

/**
 * SysPermissions的路由接口服务
 *
 * <AUTHOR>
@RestController
@RequestMapping("/sys/permission")
public class SysPermissionsController extends BaseController {

    /**
     * SysPermissionsService服务
     */
    @Autowired
    private ISysPermissionsService iSysPermissionsService;

    @GetMapping(value = {"/allPermission"},
            produces = {MediaType.APPLICATION_JSON_VALUE})
    public Result allPermission() {
        return iSysPermissionsService.allPermission();
    }
}
