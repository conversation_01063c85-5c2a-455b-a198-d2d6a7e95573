package cn.shencom.server.controller;

import cn.shencom.model.dto.create.AiotSceneCategoryCreateDTO;
import cn.shencom.model.dto.query.AiotSceneCategoryQueryDTO;
import cn.shencom.model.dto.resp.AiotSceneCategoryRespDTO;
import cn.shencom.model.dto.update.AiotSceneCategoryUpdateDTO;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.dto.ScDeleteDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.server.controller.BaseController;
import cn.shencom.server.service.IAiotSceneCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * aiot_scene_category 的接口服务
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@RestController
@RequestMapping("/aiot/scene/category")
public class AiotSceneCategoryController extends BaseController {

    @Autowired
    private IAiotSceneCategoryService iAiotSceneCategoryService;

    /**
     * 查询aiot_scene_category列表
     *
     * @param bean 条件
     * @return {@link Result}<{@link Page}<{@link AiotSceneCategoryRespDTO}>>
     */
    @PostMapping(value = {"/index"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Page<AiotSceneCategoryRespDTO>> index(@RequestBody @Validated AiotSceneCategoryQueryDTO bean) {
        return success(iAiotSceneCategoryService.query(bean));
    }

    /**
     * 根据id查询aiot_scene_category
     *
     * @param bean 条件
     * @return {@link Result<AiotSceneCategoryRespDTO>}
     */
    @PostMapping(value = {"/show"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<AiotSceneCategoryRespDTO> show(@RequestBody @Validated ScShowDTO bean) {
        return success(iAiotSceneCategoryService.show(bean));
    }

    /**
     * 新建aiot_scene_category
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/create"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> create(@RequestBody @Validated AiotSceneCategoryCreateDTO bean) {
        return success(Objects.nonNull(iAiotSceneCategoryService.create(bean)));
    }

    /**
     * 修改aiot_scene_category
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/update"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> update(@RequestBody @Validated AiotSceneCategoryUpdateDTO bean) {
        return success(Objects.nonNull(iAiotSceneCategoryService.update(bean)));
    }

    /**
     * 删除aiot_scene_category
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/delete"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result delete(@RequestBody @Validated ScDeleteDTO bean) {
        iAiotSceneCategoryService.delete(bean.getIds());
        return success();
    }

    /**
     * 导出aiot_scene_category
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/export"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result export(@RequestBody @Validated AiotSceneCategoryQueryDTO bean) {
        iAiotSceneCategoryService.export(bean);
        return success();
    }

}
