package cn.shencom.server.controller;

import cn.shencom.model.dto.create.MonitorFlowCreateDTO;
import cn.shencom.model.dto.query.MonitorFlowQueryDTO;
import cn.shencom.model.dto.resp.MonitorFlowRespDTO;
import cn.shencom.model.dto.update.MonitorFlowUpdateDTO;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.dto.ScDeleteDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.server.controller.BaseController;
import cn.shencom.server.service.IMonitorFlowService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * 小散工程-监管工单流程 的接口服务
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@RestController
@RequestMapping("/monitor/flow")
public class MonitorFlowController extends BaseController {

    @Autowired
    private IMonitorFlowService iMonitorFlowService;

    /**
     * 查询小散工程-监管工单流程列表
     *
     * @param bean 条件
     * @return {@link Result}<{@link Page}<{@link MonitorFlowRespDTO}>>
     */
    @PostMapping(value = {"/index"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Page<MonitorFlowRespDTO>> index(@RequestBody @Validated MonitorFlowQueryDTO bean) {
        return success(iMonitorFlowService.query(bean));
    }

    /**
     * 根据id查询小散工程-监管工单流程
     *
     * @param bean 条件
     * @return {@link Result<MonitorFlowRespDTO>}
     */
    @PostMapping(value = {"/show"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<MonitorFlowRespDTO> show(@RequestBody @Validated ScShowDTO bean) {
        return success(iMonitorFlowService.show(bean));
    }

    /**
     * 新建小散工程-监管工单流程
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/create"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> create(@RequestBody @Validated MonitorFlowCreateDTO bean) {
        return success(Objects.nonNull(iMonitorFlowService.create(bean)));
    }

    /**
     * 修改小散工程-监管工单流程
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/update"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> update(@RequestBody @Validated MonitorFlowUpdateDTO bean) {
        return success(Objects.nonNull(iMonitorFlowService.update(bean)));
    }

    /**
     * 删除小散工程-监管工单流程
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/delete"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result delete(@RequestBody @Validated ScDeleteDTO bean) {
        iMonitorFlowService.delete(bean.getIds());
        return success();
    }

    /**
     * 导出小散工程-监管工单流程
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/export"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result export(@RequestBody @Validated MonitorFlowQueryDTO bean) {
        iMonitorFlowService.export(bean);
        return success();
    }

}
