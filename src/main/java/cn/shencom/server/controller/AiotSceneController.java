package cn.shencom.server.controller;

import cn.shencom.model.dto.create.AiotSceneCreateDTO;
import cn.shencom.model.dto.query.AiotSceneQueryDTO;
import cn.shencom.model.dto.resp.AiotSceneRespDTO;
import cn.shencom.model.dto.update.AiotSceneUpdateDTO;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.dto.ScDeleteDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.server.controller.BaseController;
import cn.shencom.server.service.IAiotSceneService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * aiot违规类型场景 的接口服务
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@RestController
@RequestMapping("/aiot/scene")
public class AiotSceneController extends BaseController {

    @Autowired
    private IAiotSceneService iAiotSceneService;

    /**
     * 查询aiot违规类型场景列表
     *
     * @param bean 条件
     * @return {@link Result}<{@link Page}<{@link AiotSceneRespDTO}>>
     */
    @PostMapping(value = {"/index"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Page<AiotSceneRespDTO>> index(@RequestBody @Validated AiotSceneQueryDTO bean) {
        return success(iAiotSceneService.query(bean));
    }

    /**
     * 根据id查询aiot违规类型场景
     *
     * @param bean 条件
     * @return {@link Result<AiotSceneRespDTO>}
     */
    @PostMapping(value = {"/show"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<AiotSceneRespDTO> show(@RequestBody @Validated ScShowDTO bean) {
        return success(iAiotSceneService.show(bean));
    }

    /**
     * 新建aiot违规类型场景
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/create"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> create(@RequestBody @Validated AiotSceneCreateDTO bean) {
        return success(Objects.nonNull(iAiotSceneService.create(bean)));
    }

    /**
     * 修改aiot违规类型场景
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/update"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> update(@RequestBody @Validated AiotSceneUpdateDTO bean) {
        return success(Objects.nonNull(iAiotSceneService.update(bean)));
    }

    /**
     * 删除aiot违规类型场景
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/delete"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result delete(@RequestBody @Validated ScDeleteDTO bean) {
        iAiotSceneService.delete(bean.getIds());
        return success();
    }

    /**
     * 导出aiot违规类型场景
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/export"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result export(@RequestBody @Validated AiotSceneQueryDTO bean) {
        iAiotSceneService.export(bean);
        return success();
    }

}
