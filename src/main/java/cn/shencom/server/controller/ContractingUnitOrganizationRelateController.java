package cn.shencom.server.controller;

import cn.shencom.model.dto.create.ContractingUnitOrganizationRelateCreateDTO;
import cn.shencom.model.dto.query.ContractingUnitOrganizationRelateQueryDTO;
import cn.shencom.model.dto.resp.ContractingUnitOrganizationRelateRespDTO;
import cn.shencom.model.dto.update.ContractingUnitOrganizationRelateUpdateDTO;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.dto.ScDeleteDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.server.controller.BaseController;
import cn.shencom.server.service.IContractingUnitOrganizationRelateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * 施工单位组织关联表 的接口服务
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@RestController
@RequestMapping("/contracting/unit/organization/relate")
public class ContractingUnitOrganizationRelateController extends BaseController {

    @Autowired
    private IContractingUnitOrganizationRelateService iContractingUnitOrganizationRelateService;

    /**
     * 查询施工单位组织关联表列表
     *
     * @param bean 条件
     * @return {@link Result}<{@link Page}<{@link ContractingUnitOrganizationRelateRespDTO}>>
     */
    @PostMapping(value = {"/index"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Page<ContractingUnitOrganizationRelateRespDTO>> index(@RequestBody @Validated ContractingUnitOrganizationRelateQueryDTO bean) {
        return success(iContractingUnitOrganizationRelateService.query(bean));
    }

    /**
     * 根据id查询施工单位组织关联表
     *
     * @param bean 条件
     * @return {@link Result<ContractingUnitOrganizationRelateRespDTO>}
     */
    @PostMapping(value = {"/show"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<ContractingUnitOrganizationRelateRespDTO> show(@RequestBody @Validated ScShowDTO bean) {
        return success(iContractingUnitOrganizationRelateService.show(bean));
    }

    /**
     * 新建施工单位组织关联表
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/create"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> create(@RequestBody @Validated ContractingUnitOrganizationRelateCreateDTO bean) {
        return success(Objects.nonNull(iContractingUnitOrganizationRelateService.create(bean)));
    }

    /**
     * 修改施工单位组织关联表
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/update"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> update(@RequestBody @Validated ContractingUnitOrganizationRelateUpdateDTO bean) {
        return success(Objects.nonNull(iContractingUnitOrganizationRelateService.update(bean)));
    }

    /**
     * 删除施工单位组织关联表
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/delete"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result delete(@RequestBody @Validated ScDeleteDTO bean) {
        iContractingUnitOrganizationRelateService.delete(bean.getIds());
        return success();
    }

    /**
     * 导出施工单位组织关联表
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/export"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result export(@RequestBody @Validated ContractingUnitOrganizationRelateQueryDTO bean) {
        iContractingUnitOrganizationRelateService.export(bean);
        return success();
    }

}
