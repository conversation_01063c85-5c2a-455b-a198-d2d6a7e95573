package cn.shencom.server.controller;

import cn.shencom.model.dto.create.AiotCategoryCreateDTO;
import cn.shencom.model.dto.query.AiotCategoryQueryDTO;
import cn.shencom.model.dto.resp.AiotCategoryRespDTO;
import cn.shencom.model.dto.update.AiotCategoryUpdateDTO;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.dto.ScDeleteDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.server.controller.BaseController;
import cn.shencom.server.service.IAiotCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * aiot场景分类 的接口服务
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@RestController
@RequestMapping("/aiot/category")
public class AiotCategoryController extends BaseController {

    @Autowired
    private IAiotCategoryService iAiotCategoryService;

    /**
     * 查询aiot场景分类列表
     *
     * @param bean 条件
     * @return {@link Result}<{@link Page}<{@link AiotCategoryRespDTO}>>
     */
    @PostMapping(value = {"/index"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Page<AiotCategoryRespDTO>> index(@RequestBody @Validated AiotCategoryQueryDTO bean) {
        return success(iAiotCategoryService.query(bean));
    }

    /**
     * 根据id查询aiot场景分类
     *
     * @param bean 条件
     * @return {@link Result<AiotCategoryRespDTO>}
     */
    @PostMapping(value = {"/show"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<AiotCategoryRespDTO> show(@RequestBody @Validated ScShowDTO bean) {
        return success(iAiotCategoryService.show(bean));
    }

    /**
     * 新建aiot场景分类
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/create"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> create(@RequestBody @Validated AiotCategoryCreateDTO bean) {
        return success(Objects.nonNull(iAiotCategoryService.create(bean)));
    }

    /**
     * 修改aiot场景分类
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/update"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> update(@RequestBody @Validated AiotCategoryUpdateDTO bean) {
        return success(Objects.nonNull(iAiotCategoryService.update(bean)));
    }

    /**
     * 删除aiot场景分类
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/delete"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result delete(@RequestBody @Validated ScDeleteDTO bean) {
        iAiotCategoryService.delete(bean.getIds());
        return success();
    }

    /**
     * 导出aiot场景分类
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/export"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result export(@RequestBody @Validated AiotCategoryQueryDTO bean) {
        iAiotCategoryService.export(bean);
        return success();
    }

}
