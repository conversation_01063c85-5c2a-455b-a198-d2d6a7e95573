package cn.shencom.server.controller;

import cn.shencom.common.util.DSConnection;
import cn.shencom.model.dto.create.AimFirmCreateDTO;
import cn.shencom.model.dto.query.AimFirmQueryDTO;
import cn.shencom.model.dto.resp.AimFirmRespDTO;
import cn.shencom.model.dto.update.AimFirmUpdateDTO;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.dto.ScDeleteDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.server.controller.BaseController;
import cn.shencom.server.service.IAimFirmService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * 人工智能管理厂商表 的接口服务
 *
 * <AUTHOR>
 * @since 2022-08-02
 */
@RestController
@RequestMapping("/aim/firm")
public class AimFirmController extends BaseController {

    @Autowired
    private IAimFirmService iAimFirmService;

    /**
     * 查询人工智能管理厂商表列表
     *
     * @param bean 条件
     * @return {@link Result}<{@link Page}<{@link AimFirmRespDTO}>>
     */
    @PostMapping(value = {"/index"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Page<AimFirmRespDTO>> index(@RequestBody @Validated AimFirmQueryDTO bean) {
        return success(iAimFirmService.query(bean));
    }

    /**
     * 根据id查询人工智能管理厂商表
     *
     * @param bean 条件
     * @return {@link Result<AimFirmRespDTO>}
     */
    @PostMapping(value = {"/show"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<AimFirmRespDTO> show(@RequestBody @Validated ScShowDTO bean) {
        return success(iAimFirmService.show(bean));
    }

    /**
     * 新建人工智能管理厂商表
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/create"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> create(@RequestBody @Validated AimFirmCreateDTO bean) {
        return success(Objects.nonNull(iAimFirmService.create(bean)));
    }

    /**
     * 修改人工智能管理厂商表
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/update"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> update(@RequestBody @Validated AimFirmUpdateDTO bean) {
        return success(Objects.nonNull(iAimFirmService.update(bean)));
    }

    /**
     * 删除人工智能管理厂商表
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/delete"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result delete(@RequestBody @Validated ScDeleteDTO bean) {
        iAimFirmService.delete(bean.getIds());
        return success();
    }

    /**
     * 导出人工智能管理厂商表
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/export"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result export(@RequestBody @Validated AimFirmQueryDTO bean) {
        iAimFirmService.export(bean);
        return success();
    }

    /**
     * 获取全部厂商列表
     *
     * @return {@link Result}
     */
    @PostMapping(value = {"/all"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    @DSConnection(onlyRead = true)
    public Result<List<AimFirmRespDTO>> getAllFirm() {
        return success(iAimFirmService.getAllFirm());
    }
}
