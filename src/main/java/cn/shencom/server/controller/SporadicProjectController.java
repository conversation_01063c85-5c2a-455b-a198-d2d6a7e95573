package cn.shencom.server.controller;

import cn.shencom.model.dto.create.SporadicProjectCreateDTO;
import cn.shencom.model.dto.query.InviteCodeQueryDTO;
import cn.shencom.model.dto.query.SporadicProjectMobileQueryDTO;
import cn.shencom.model.dto.query.SporadicProjectQueryDTO;
import cn.shencom.model.dto.resp.InviteCodeRespDTO;
import cn.shencom.model.dto.resp.SporadicProjectRespDTO;
import cn.shencom.model.dto.resp.SporadicProjectRegionStatisticsRespDTO;
import cn.shencom.model.dto.update.SporadicProjectUpdateDTO;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.dto.ScDeleteDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.server.controller.BaseController;
import cn.shencom.server.service.ISporadicProjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Objects;

/**
 * 小散工程表 的接口服务
 *
 * <AUTHOR>
 * @since 2025-05-29
 */
@RestController
@RequestMapping("/sporadic/project")
public class SporadicProjectController extends BaseController {

    @Autowired
    private ISporadicProjectService iSporadicProjectService;

    /**
     * 查询小散工程表列表
     *
     * @param bean 条件
     * @return {@link Result}<{@link Page}<{@link SporadicProjectRespDTO}>>
     */
    @PostMapping(value = {"/index"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Page<SporadicProjectRespDTO>> index(@RequestBody @Validated SporadicProjectQueryDTO bean) {
        return success(iSporadicProjectService.query(bean));
    }

    /**
     * 根据id查询小散工程表
     *
     * @param bean 条件
     * @return {@link Result<SporadicProjectRespDTO>}
     */
    @PostMapping(value = {"/show"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<SporadicProjectRespDTO> show(@RequestBody @Validated ScShowDTO bean) {
        return success(iSporadicProjectService.show(bean));
    }

    /**
     * 新建小散工程表
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/create"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> create(@RequestBody @Validated SporadicProjectCreateDTO bean) {
        return success(Objects.nonNull(iSporadicProjectService.create(bean)));
    }

    /**
     * 修改小散工程表
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/update"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> update(@RequestBody @Validated SporadicProjectUpdateDTO bean) {
        return success(Objects.nonNull(iSporadicProjectService.update(bean)));
    }



    /**
     *  更新施工状态
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/update/status"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> updateStatus(@RequestBody @Validated SporadicProjectUpdateDTO bean) {
        iSporadicProjectService.updateStatus(bean);
        return success();
    }




    /**
     * 删除小散工程表
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/delete"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result delete(@RequestBody @Validated ScDeleteDTO bean) {
        iSporadicProjectService.delete(bean.getIds());
        return success();
    }

    /**
     * 导出小散工程表
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/export"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result export(@RequestBody @Validated SporadicProjectQueryDTO bean) {
        iSporadicProjectService.export(bean);
        return success();
    }

    /**
     * 导入小散工程表
     *
     * @param file 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/import"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<?> importExcel(@RequestParam("file") MultipartFile file) {
        return iSporadicProjectService.importExcel(file);
    }





    /**
     * 查询小散工程表列表 -移动端
     *
     * @param bean 条件
     * @return {@link Result}<{@link Page}<{@link SporadicProjectRespDTO}>>
     */
    @PostMapping(value = {"/mobile/index"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Page<SporadicProjectRespDTO>> mobileIndex(@RequestBody @Validated SporadicProjectMobileQueryDTO bean) {
        return success(iSporadicProjectService.mobileIndex(bean));
    }

    /**
     * 获取工程区域分布统计
     *
     * @param bean 查询条件
     * @return {@link Result}<{@link List}<{@link SporadicProjectRegionStatisticsRespDTO}>>
     */
    @PostMapping(value = {"/region/statistics"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<List<SporadicProjectRegionStatisticsRespDTO>> getRegionStatistics(
            @RequestBody @Validated SporadicProjectQueryDTO bean) {
        return success(iSporadicProjectService.getRegionStatistics(bean));
    }

    /**
     * 生成工程邀请码
     * 只有施工负责人和建设方（业主）有权限生成
     *
     * @param bean 工程ID
     * @return {@link Result}<{@link InviteCodeRespDTO}>
     */
    @PostMapping(value = {"/invite-code/generate"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<InviteCodeRespDTO> generateInviteCode(@RequestBody @Validated ScShowDTO bean) {
        return success(iSporadicProjectService.generateInviteCode(bean.getId()));
    }

    /**
     * 通过邀请码获取工程信息
     *
     * @param bean 邀请码查询条件
     * @return {@link Result}<{@link SporadicProjectRespDTO}>
     */
    @PostMapping(value = {"/invite-code/project"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<SporadicProjectRespDTO> getProjectByInviteCode(@RequestBody @Validated InviteCodeQueryDTO bean) {
        return success(iSporadicProjectService.getProjectByInviteCode(bean.getInviteCode()));
    }

}
