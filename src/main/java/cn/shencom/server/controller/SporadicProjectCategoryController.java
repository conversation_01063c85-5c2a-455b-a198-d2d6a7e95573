package cn.shencom.server.controller;

import cn.shencom.model.dto.SporadicProjectCategoryTreeDTO;
import cn.shencom.model.dto.create.SporadicProjectCategoryCreateDTO;
import cn.shencom.model.dto.query.SporadicProjectCategoryQueryDTO;
import cn.shencom.model.dto.resp.SporadicProjectCategoryRespDTO;
import cn.shencom.model.dto.update.SporadicProjectCategoryUpdateDTO;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.dto.ScDeleteDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.server.controller.BaseController;
import cn.shencom.server.service.ISporadicProjectCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 小散工程分类 的接口服务
 *
 * <AUTHOR>
 * @since 2025-05-29
 */
@RestController
@RequestMapping("/sporadic/project/category")
public class SporadicProjectCategoryController extends BaseController {

    @Autowired
    private ISporadicProjectCategoryService iSporadicProjectCategoryService;

    /**
     * 查询小散工程分类列表
     *
     * @param bean 条件
     * @return {@link Result}<{@link Page}<{@link SporadicProjectCategoryRespDTO}>>
     */
    @PostMapping(value = {"/index"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Page<SporadicProjectCategoryRespDTO>> index(@RequestBody @Validated SporadicProjectCategoryQueryDTO bean) {
        return success(iSporadicProjectCategoryService.query(bean));
    }
    /**
     * 查询小散工程分类树
     *
     * @return {@link Result}<{@link List}<{@link SporadicProjectCategoryTreeDTO}>>
     */
    @PostMapping(value = {"/tree"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<List<SporadicProjectCategoryTreeDTO>> tree() {
        return success(iSporadicProjectCategoryService.tree());
    }

    /**
     * 根据id查询小散工程分类
     *
     * @param bean 条件
     * @return {@link Result<SporadicProjectCategoryRespDTO>}
     */
    @PostMapping(value = {"/show"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<SporadicProjectCategoryRespDTO> show(@RequestBody @Validated ScShowDTO bean) {
        return success(iSporadicProjectCategoryService.show(bean));
    }

    /**
     * 新建小散工程分类
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/create"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> create(@RequestBody @Validated SporadicProjectCategoryCreateDTO bean) {
        return success(Objects.nonNull(iSporadicProjectCategoryService.create(bean)));
    }

    /**
     * 修改小散工程分类
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/update"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> update(@RequestBody @Validated SporadicProjectCategoryUpdateDTO bean) {
        return success(Objects.nonNull(iSporadicProjectCategoryService.update(bean)));
    }

    /**
     * 删除小散工程分类
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/delete"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result delete(@RequestBody @Validated ScDeleteDTO bean) {
        iSporadicProjectCategoryService.delete(bean.getIds());
        return success();
    }

    /**
     * 导出小散工程分类
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/export"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result export(@RequestBody @Validated SporadicProjectCategoryQueryDTO bean) {
        iSporadicProjectCategoryService.export(bean);
        return success();
    }

}
