package cn.shencom.server.controller;

import cn.shencom.model.dto.create.EngineeringMembersProjectRelateCreateDTO;
import cn.shencom.model.dto.query.EngineeringMembersProjectRelateQueryDTO;
import cn.shencom.model.dto.resp.EngineeringMembersProjectRelateRespDTO;
import cn.shencom.model.dto.update.EngineeringMembersProjectRelateUpdateDTO;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.dto.ScDeleteDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.server.controller.BaseController;
import cn.shencom.server.service.IEngineeringMembersProjectRelateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * 小散工程-工程人员关联项目表 的接口服务
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@RestController
@RequestMapping("/engineering/members/project/relate")
public class EngineeringMembersProjectRelateController extends BaseController {

    @Autowired
    private IEngineeringMembersProjectRelateService iEngineeringMembersProjectRelateService;

    /**
     * 查询小散工程-工程人员关联项目表列表
     *
     * @param bean 条件
     * @return {@link Result}<{@link Page}<{@link EngineeringMembersProjectRelateRespDTO}>>
     */
    @PostMapping(value = {"/index"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Page<EngineeringMembersProjectRelateRespDTO>> index(@RequestBody @Validated EngineeringMembersProjectRelateQueryDTO bean) {
        return success(iEngineeringMembersProjectRelateService.query(bean));
    }

    /**
     * 根据id查询小散工程-工程人员关联项目表
     *
     * @param bean 条件
     * @return {@link Result<EngineeringMembersProjectRelateRespDTO>}
     */
    @PostMapping(value = {"/show"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<EngineeringMembersProjectRelateRespDTO> show(@RequestBody @Validated ScShowDTO bean) {
        return success(iEngineeringMembersProjectRelateService.show(bean));
    }

    /**
     * 新建小散工程-工程人员关联项目表
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/create"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> create(@RequestBody @Validated EngineeringMembersProjectRelateCreateDTO bean) {
        return success(Objects.nonNull(iEngineeringMembersProjectRelateService.create(bean)));
    }

    /**
     * 修改小散工程-工程人员关联项目表
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/update"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> update(@RequestBody @Validated EngineeringMembersProjectRelateUpdateDTO bean) {
        return success(Objects.nonNull(iEngineeringMembersProjectRelateService.update(bean)));
    }

    /**
     * 删除小散工程-工程人员关联项目表
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/delete"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result delete(@RequestBody @Validated ScDeleteDTO bean) {
        iEngineeringMembersProjectRelateService.delete(bean.getIds());
        return success();
    }

    /**
     * 导出小散工程-工程人员关联项目表
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/export"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result export(@RequestBody @Validated EngineeringMembersProjectRelateQueryDTO bean) {
        iEngineeringMembersProjectRelateService.export(bean);
        return success();
    }

}
