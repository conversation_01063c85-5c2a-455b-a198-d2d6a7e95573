package cn.shencom.server.controller;

import cn.shencom.model.dto.create.SporadicProjectMemoCreateDTO;
import cn.shencom.model.dto.query.SporadicProjectMemoQueryDTO;
import cn.shencom.model.dto.resp.SporadicProjectMemoRespDTO;
import cn.shencom.model.dto.update.SporadicProjectMemoUpdateDTO;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.dto.ScDeleteDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.server.controller.BaseController;
import cn.shencom.server.service.ISporadicProjectMemoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * 小散工程-工程备注表 的接口服务
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@RestController
@RequestMapping("/sporadic/project/memo")
public class SporadicProjectMemoController extends BaseController {

    @Autowired
    private ISporadicProjectMemoService iSporadicProjectMemoService;

    /**
     * 查询小散工程-工程备注表列表
     *
     * @param bean 条件
     * @return {@link Result}<{@link Page}<{@link SporadicProjectMemoRespDTO}>>
     */
    @PostMapping(value = {"/index"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Page<SporadicProjectMemoRespDTO>> index(@RequestBody @Validated SporadicProjectMemoQueryDTO bean) {
        return success(iSporadicProjectMemoService.query(bean));
    }

    /**
     * 根据id查询小散工程-工程备注表
     *
     * @param bean 条件
     * @return {@link Result<SporadicProjectMemoRespDTO>}
     */
    @PostMapping(value = {"/show"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<SporadicProjectMemoRespDTO> show(@RequestBody @Validated ScShowDTO bean) {
        return success(iSporadicProjectMemoService.show(bean));
    }

    /**
     * 新建小散工程-工程备注表
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/create"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> create(@RequestBody @Validated SporadicProjectMemoCreateDTO bean) {
        return success(Objects.nonNull(iSporadicProjectMemoService.create(bean)));
    }

    /**
     * 修改小散工程-工程备注表
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/update"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> update(@RequestBody @Validated SporadicProjectMemoUpdateDTO bean) {
        return success(Objects.nonNull(iSporadicProjectMemoService.update(bean)));
    }

    /**
     * 删除小散工程-工程备注表
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/delete"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result delete(@RequestBody @Validated ScDeleteDTO bean) {
        iSporadicProjectMemoService.delete(bean.getIds());
        return success();
    }

    /**
     * 导出小散工程-工程备注表
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/export"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result export(@RequestBody @Validated SporadicProjectMemoQueryDTO bean) {
        iSporadicProjectMemoService.export(bean);
        return success();
    }

}
