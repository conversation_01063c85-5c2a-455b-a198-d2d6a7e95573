package cn.shencom.server.controller;

import cn.shencom.model.XsgcCustomerServiceRecord;
import cn.shencom.model.dto.create.XsgcCustomerServiceRecordCreateDTO;
import cn.shencom.model.dto.query.XsgcCustomerServiceRecordQueryDTO;
import cn.shencom.model.dto.resp.XsgcCustomerServiceRecordRespDTO;
import cn.shencom.model.dto.update.XsgcCustomerServiceRecordUpdateDTO;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.dto.ScDeleteDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.server.controller.BaseController;
import cn.shencom.server.service.IXsgcCustomerServiceRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * 小散工程-客户服务开通记录 的接口服务
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@RestController
@RequestMapping("/xsgc/customer/service/record")
public class XsgcCustomerServiceRecordController extends BaseController {

    @Autowired
    private IXsgcCustomerServiceRecordService iXsgcCustomerServiceRecordService;

    /**
     * 查询小散工程-客户服务开通记录列表
     *
     * @param bean 条件
     * @return {@link Result}<{@link Page}<{@link XsgcCustomerServiceRecordRespDTO}>>
     */
    @PostMapping(value = {"/index"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Page<XsgcCustomerServiceRecordRespDTO>> index(@RequestBody @Validated XsgcCustomerServiceRecordQueryDTO bean) {
        return success(iXsgcCustomerServiceRecordService.query(bean));
    }

    /**
     * 根据id查询小散工程-客户服务开通记录
     *
     * @param bean 条件
     * @return {@link Result<XsgcCustomerServiceRecordRespDTO>}
     */
    @PostMapping(value = {"/show"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<XsgcCustomerServiceRecordRespDTO> show(@RequestBody @Validated ScShowDTO bean) {
        return success(iXsgcCustomerServiceRecordService.show(bean));
    }

    /**
     * 新建小散工程-客户服务开通记录
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/create"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> create(@RequestBody @Validated XsgcCustomerServiceRecordCreateDTO bean) {
        return success(Objects.nonNull(iXsgcCustomerServiceRecordService.create(bean)));
    }

    /**
     * 修改小散工程-客户服务开通记录
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/update"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> update(@RequestBody @Validated XsgcCustomerServiceRecordUpdateDTO bean) {
        return success(Objects.nonNull(iXsgcCustomerServiceRecordService.update(bean)));
    }

    /**
     * 删除小散工程-客户服务开通记录
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/delete"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result delete(@RequestBody @Validated ScDeleteDTO bean) {
        iXsgcCustomerServiceRecordService.delete(bean.getIds());
        return success();
    }

    /**
     * 导出小散工程-客户服务开通记录
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/export"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result export(@RequestBody @Validated XsgcCustomerServiceRecordQueryDTO bean) {
        iXsgcCustomerServiceRecordService.export(bean);
        return success();
    }



    /**
     * 查询小散工程-客户服务最新的一次开通记录列表
     *
     * @param bean 条件
     * @return {@link Result}<{@link Page}<{@link XsgcCustomerServiceRecordRespDTO}>>
     */
    @PostMapping(value = {"/last/record"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<XsgcCustomerServiceRecord> lastRecord(@RequestBody @Validated XsgcCustomerServiceRecordQueryDTO bean) {
        return success(iXsgcCustomerServiceRecordService.lastRecord(bean));
    }

}
