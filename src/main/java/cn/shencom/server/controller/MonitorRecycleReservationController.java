package cn.shencom.server.controller;

import cn.shencom.model.dto.create.MonitorRecycleReservationCreateDTO;
import cn.shencom.model.dto.query.MonitorRecycleReservationQueryDTO;
import cn.shencom.model.dto.resp.MonitorRecycleReservationRespDTO;
import cn.shencom.model.dto.update.MonitorRecycleReservationUpdateDTO;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.dto.ScDeleteDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.server.controller.BaseController;
import cn.shencom.server.service.IMonitorRecycleReservationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * 小散工程-监管工单流程-回收预约详情 的接口服务
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@RestController
@RequestMapping("/monitor/recycle/reservation")
public class MonitorRecycleReservationController extends BaseController {

    @Autowired
    private IMonitorRecycleReservationService iMonitorRecycleReservationService;

    /**
     * 查询小散工程-监管工单流程-回收预约详情列表
     *
     * @param bean 条件
     * @return {@link Result}<{@link Page}<{@link MonitorRecycleReservationRespDTO}>>
     */
    @PostMapping(value = {"/index"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Page<MonitorRecycleReservationRespDTO>> index(@RequestBody @Validated MonitorRecycleReservationQueryDTO bean) {
        return success(iMonitorRecycleReservationService.query(bean));
    }

    /**
     * 根据id查询小散工程-监管工单流程-回收预约详情
     *
     * @param bean 条件
     * @return {@link Result<MonitorRecycleReservationRespDTO>}
     */
    @PostMapping(value = {"/show"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<MonitorRecycleReservationRespDTO> show(@RequestBody @Validated ScShowDTO bean) {
        return success(iMonitorRecycleReservationService.show(bean));
    }

    /**
     * 新建小散工程-监管工单流程-回收预约详情
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/create"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> create(@RequestBody @Validated MonitorRecycleReservationCreateDTO bean) {
        return success(Objects.nonNull(iMonitorRecycleReservationService.create(bean)));
    }

    /**
     * 修改小散工程-监管工单流程-回收预约详情
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/update"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> update(@RequestBody @Validated MonitorRecycleReservationUpdateDTO bean) {
        return success(Objects.nonNull(iMonitorRecycleReservationService.update(bean)));
    }

    /**
     * 删除小散工程-监管工单流程-回收预约详情
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/delete"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result delete(@RequestBody @Validated ScDeleteDTO bean) {
        iMonitorRecycleReservationService.delete(bean.getIds());
        return success();
    }

    /**
     * 导出小散工程-监管工单流程-回收预约详情
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/export"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result export(@RequestBody @Validated MonitorRecycleReservationQueryDTO bean) {
        iMonitorRecycleReservationService.export(bean);
        return success();
    }

}
