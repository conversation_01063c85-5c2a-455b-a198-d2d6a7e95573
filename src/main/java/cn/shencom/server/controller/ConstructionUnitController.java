package cn.shencom.server.controller;

import cn.shencom.model.dto.create.ConstructionUnitCreateDTO;
import cn.shencom.model.dto.query.ConstructionUnitQueryDTO;
import cn.shencom.model.dto.resp.ConstructionUnitRespDTO;
import cn.shencom.model.dto.update.ConstructionUnitUpdateDTO;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.dto.ScDeleteDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.server.controller.BaseController;
import cn.shencom.server.service.IConstructionUnitService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * 施工单位表 的接口服务
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@RestController
@RequestMapping("/construction/unit")
public class ConstructionUnitController extends BaseController {

    @Autowired
    private IConstructionUnitService iConstructionUnitService;

    /**
     * 查询施工单位表列表
     *
     * @param bean 条件
     * @return {@link Result}<{@link Page}<{@link ConstructionUnitRespDTO}>>
     */
    @PostMapping(value = { "/index" }, produces = { MediaType.APPLICATION_JSON_UTF8_VALUE })
    public Result<Page<ConstructionUnitRespDTO>> index(@RequestBody @Validated ConstructionUnitQueryDTO bean) {
        return success(iConstructionUnitService.query(bean));
    }

    /**
     * 根据id查询施工单位表
     *
     * @param bean 条件
     * @return {@link Result<ConstructionUnitRespDTO>}
     */
    @PostMapping(value = { "/show" }, produces = { MediaType.APPLICATION_JSON_UTF8_VALUE })
    public Result<ConstructionUnitRespDTO> show(@RequestBody @Validated ScShowDTO bean) {
        return success(iConstructionUnitService.show(bean));
    }

    /**
     * 新建施工单位表
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = { "/create" }, produces = { MediaType.APPLICATION_JSON_UTF8_VALUE })
    public Result<Boolean> create(@RequestBody @Validated ConstructionUnitCreateDTO bean) {
        return success(Objects.nonNull(iConstructionUnitService.create(bean)));
    }

    /**
     * 修改施工单位表
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = { "/update" }, produces = { MediaType.APPLICATION_JSON_UTF8_VALUE })
    public Result<Boolean> update(@RequestBody @Validated ConstructionUnitUpdateDTO bean) {
        return success(Objects.nonNull(iConstructionUnitService.update(bean)));
    }

    /**
     * 删除施工单位表
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = { "/delete" }, produces = { MediaType.APPLICATION_JSON_UTF8_VALUE })
    public Result delete(@RequestBody @Validated ScDeleteDTO bean) {
        iConstructionUnitService.delete(bean.getIds());
        return success();
    }

    /**
     * 导出施工单位表
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = { "/export" }, produces = { MediaType.APPLICATION_JSON_UTF8_VALUE })
    public Result export(@RequestBody @Validated ConstructionUnitQueryDTO bean) {
        iConstructionUnitService.export(bean);
        return success();
    }

}
