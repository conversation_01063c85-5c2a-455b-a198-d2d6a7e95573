package cn.shencom.server.controller;

import cn.shencom.model.dto.create.XsgcSubscriptionCreateDTO;
import cn.shencom.model.dto.query.XsgcSubscriptionQueryDTO;
import cn.shencom.model.dto.resp.XsgcSubscriptionRespDTO;
import cn.shencom.model.dto.update.XsgcSubscriptionUpdateDTO;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.dto.ScDeleteDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.server.controller.BaseController;
import cn.shencom.server.service.IXsgcSubscriptionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * 小散工程-套餐 的接口服务
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@RestController
@RequestMapping("/xsgc/subscription")
public class XsgcSubscriptionController extends BaseController {

    @Autowired
    private IXsgcSubscriptionService iXsgcSubscriptionService;

    /**
     * 查询小散工程-套餐列表
     *
     * @param bean 条件
     * @return {@link Result}<{@link Page}<{@link XsgcSubscriptionRespDTO}>>
     */
    @PostMapping(value = {"/index"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Page<XsgcSubscriptionRespDTO>> index(@RequestBody @Validated XsgcSubscriptionQueryDTO bean) {
        return success(iXsgcSubscriptionService.query(bean));
    }

    /**
     * 根据id查询小散工程-套餐
     *
     * @param bean 条件
     * @return {@link Result<XsgcSubscriptionRespDTO>}
     */
    @PostMapping(value = {"/show"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<XsgcSubscriptionRespDTO> show(@RequestBody @Validated ScShowDTO bean) {
        return success(iXsgcSubscriptionService.show(bean));
    }

    /**
     * 新建小散工程-套餐
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/create"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> create(@RequestBody @Validated XsgcSubscriptionCreateDTO bean) {
        return success(Objects.nonNull(iXsgcSubscriptionService.create(bean)));
    }

    /**
     * 修改小散工程-套餐
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/update"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> update(@RequestBody @Validated XsgcSubscriptionUpdateDTO bean) {
        return success(Objects.nonNull(iXsgcSubscriptionService.update(bean)));
    }

    /**
     * 删除小散工程-套餐
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/delete"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result delete(@RequestBody @Validated ScDeleteDTO bean) {
        iXsgcSubscriptionService.delete(bean.getIds());
        return success();
    }

    /**
     * 导出小散工程-套餐
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/export"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result export(@RequestBody @Validated XsgcSubscriptionQueryDTO bean) {
        iXsgcSubscriptionService.export(bean);
        return success();
    }

}
