package cn.shencom.server.controller;

import cn.shencom.common.util.DSConnection;
import cn.shencom.model.dto.AimSceneRelevanceFirmDTO;
import cn.shencom.model.dto.create.AimSceneManagementCreateDTO;
import cn.shencom.model.dto.query.AimSceneManagementQueryDTO;
import cn.shencom.model.dto.resp.AimSceneCategoryDTO;
import cn.shencom.model.dto.resp.AimSceneManagementRespDTO;
import cn.shencom.model.dto.resp.AimSceneStatisticsDTO;
import cn.shencom.model.dto.update.AimSceneManagementUpdateDTO;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.dto.ScDeleteDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.server.controller.BaseController;
import cn.shencom.server.service.IAimSceneManagementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * AI场景管理 的接口服务
 *
 * <AUTHOR>
 * @since 2022-07-26
 */
@RestController
@RequestMapping("/aim/scene/management")
public class AimSceneManagementController extends BaseController {

    @Autowired
    private IAimSceneManagementService iAimSceneManagementService;

    /**
     * 查询AI场景管理列表
     *
     * @param bean 条件
     * @return {@link Result}<{@link Page}<{@link AimSceneManagementRespDTO}>>
     */
    @PostMapping(value = {"/index"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Page<AimSceneManagementRespDTO>> index(@RequestBody @Validated AimSceneManagementQueryDTO bean) {
        return success(iAimSceneManagementService.query(bean));
    }

    /**
     * 更新ai场景缓存
     *
     * @return {@link Result}<{@link Page}<{@link AimSceneManagementRespDTO}>>
     */
    @PostMapping(value = {"/store/managment/list"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Page<AimSceneManagementRespDTO>> storeManagementList() {
        iAimSceneManagementService.storeManagementList();
        return success();
    }

    /**
     * 根据id查询AI场景管理
     *
     * @param bean 条件
     * @return {@link Result<AimSceneManagementRespDTO>}
     */
    @PostMapping(value = {"/show"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<AimSceneManagementRespDTO> show(@RequestBody @Validated ScShowDTO bean) {
        return success(iAimSceneManagementService.show(bean));
    }

    /**
     * 新建AI场景管理
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/create"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> create(@RequestBody @Validated AimSceneManagementCreateDTO bean) {
        return success(Objects.nonNull(iAimSceneManagementService.create(bean)));
    }

    /**
     * 修改AI场景管理
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/update"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> update(@RequestBody @Validated AimSceneManagementUpdateDTO bean) {
        return success(Objects.nonNull(iAimSceneManagementService.update(bean)));
    }

    /**
     * 删除AI场景管理
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/delete"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result delete(@RequestBody @Validated ScDeleteDTO bean) {
        iAimSceneManagementService.delete(bean.getIds());
        return success();
    }

    /**
     * 导出AI场景管理
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/export"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result export(@RequestBody @Validated AimSceneManagementQueryDTO bean) {
        iAimSceneManagementService.export(bean);
        return success();
    }

    /**
     * 全部AI场景
     *
     * @return {@link Result}
     */
    @PostMapping(value = {"/all"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    @DSConnection(onlyRead = true)
    public Result<List<AimSceneCategoryDTO>> getSceneAll() {
        return success(iAimSceneManagementService.getSceneAll());
    }

    /**
     * AI场景关联厂商场景
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/scene/associate"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result relevance(@RequestBody @Validated AimSceneRelevanceFirmDTO bean) {
        iAimSceneManagementService.sceneRelevance(bean);
        return success();
    }

    /**
     * AI场景取消关联厂商场景
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/scene/disassociate"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result disassociate(@RequestBody @Validated AimSceneRelevanceFirmDTO bean) {
        iAimSceneManagementService.sceneDisassociate(bean);
        return success();
    }

    /**
     * 统计AI场景数、场景类别数
     *
     */
    @PostMapping(value = {"/scene/statistics"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    @DSConnection(onlyRead = true)
    public Result<AimSceneStatisticsDTO> statistics(@RequestBody @Validated AimSceneManagementQueryDTO bean) {
        return success(iAimSceneManagementService.sceneStatistics(bean));
    }

    /**
     * 根据AI场景id查询厂商场景事件codes
     *
     * @param aimSceneId 条件
     * @return {@link Result<Boolean>}
     */
    @GetMapping(value = {"/codes"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    @DSConnection(onlyRead = true)
    public Result<List<String>> codes(@RequestParam String aimSceneId) {
        return success(iAimSceneManagementService.codes(aimSceneId));
    }


}
