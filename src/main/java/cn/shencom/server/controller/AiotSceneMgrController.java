package cn.shencom.server.controller;


import cn.shencom.model.dto.create.AiotSceneMgrCreateDTO;
import cn.shencom.model.dto.query.AiotSceneMgrQueryDTO;
import cn.shencom.model.dto.resp.AiotSceneMgrRespDTO;
import cn.shencom.model.dto.resp.AiotSceneMgrTreeRespDTO;
import cn.shencom.model.dto.update.AiotSceneMgrUpdateDTO;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.dto.ScDeleteDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.server.controller.BaseController;
import cn.shencom.server.service.IAiotSceneMgrExtService;
import cn.shencom.server.service.IAiotSceneMgrService;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;


/**
 * 场景类别
 *
 * <AUTHOR>
 * @since 2024-08-03
 */
@RestController
@RequestMapping("/xsgc/aiot/scene")
public class AiotSceneMgrController extends BaseController {

    @Resource
    private IAiotSceneMgrService iAiotSceneMgrService;

    @Resource
    private IAiotSceneMgrExtService iAiotSceneMgrExtService;

    /**
     * 查询场景类别列表
     *
     * @param bean 条件
     * @return {@link Result}<{@link Page}<{@link AiotSceneMgrRespDTO}>>
     */
    @PostMapping(value = {"/index"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result index(@RequestBody @Validated AiotSceneMgrQueryDTO bean) {
        return iAiotSceneMgrService.query(bean);
    }

    /**
     * 根据id查询场景类别
     *
     * @param bean 条件
     * @return {@link Result<AiotSceneMgrRespDTO>}
     */
    @PostMapping(value = {"/show"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<AiotSceneMgrRespDTO> show(@RequestBody @Validated ScShowDTO bean) {
        return success(iAiotSceneMgrService.show(bean));
    }

    /**
     * 新建场景类别
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/create"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> create(@RequestBody @Validated AiotSceneMgrCreateDTO bean) {
        return success(Objects.nonNull(iAiotSceneMgrService.create(bean)));
    }

    /**
     * 修改场景类别
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/update"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> update(@RequestBody @Validated AiotSceneMgrUpdateDTO bean) {
        return success(Objects.nonNull(iAiotSceneMgrService.update(bean)));
    }

    /**
     * 删除场景类别
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/delete"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result delete(@RequestBody @Validated ScDeleteDTO bean) {
        iAiotSceneMgrService.delete(bean.getIds());
        return success();
    }

    /**
     * 导出场景类别
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/export"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result export(@RequestBody @Validated AiotSceneMgrQueryDTO bean) {
        return success(iAiotSceneMgrService.export(bean));
    }

    /**
     * 获取全部树形菜单
     *
     * @param bean
     * @return
     */
    @PostMapping(value = {"/tree/all"}, produces = {MediaType.APPLICATION_JSON_VALUE})
    @ResponseBody
    public Result<List<AiotSceneMgrTreeRespDTO>> getAllTree(@RequestBody(required = false) AiotSceneMgrQueryDTO bean) {
        return iAiotSceneMgrExtService.getMenuTreeByAll(bean);
    }




    /**
     * 获取全部场景类别
     *
     * @param bean
     * @return
     */
    @PostMapping(value = {"/list"}, produces = {MediaType.APPLICATION_JSON_VALUE})
    @ResponseBody
    public Result<List<AiotSceneMgrRespDTO>> list(@RequestBody(required = false) AiotSceneMgrQueryDTO bean) {
        return success(iAiotSceneMgrService.list(bean));
    }


}
