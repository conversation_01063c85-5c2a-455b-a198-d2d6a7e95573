package cn.shencom.server.controller;

import cn.shencom.model.dto.create.XsgcOrganizationCreateDTO;
import cn.shencom.model.dto.query.XsgcOrganizationQueryDTO;
import cn.shencom.model.dto.resp.XsgcOrganizationRespDTO;
import cn.shencom.model.dto.update.XsgcOrganizationUpdateDTO;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.dto.ScDeleteDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.server.controller.BaseController;
import cn.shencom.server.service.IXsgcOrganizationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * 小散工程-组织 的接口服务
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@RestController
@RequestMapping("/xsgc/organization")
public class XsgcOrganizationController extends BaseController {

    @Autowired
    private IXsgcOrganizationService iXsgcOrganizationService;

    /**
     * 查询小散工程-组织列表
     *
     * @param bean 条件
     * @return {@link Result}<{@link Page}<{@link XsgcOrganizationRespDTO}>>
     */
    @PostMapping(value = {"/index"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Page<XsgcOrganizationRespDTO>> index(@RequestBody @Validated XsgcOrganizationQueryDTO bean) {
        return success(iXsgcOrganizationService.query(bean));
    }

    /**
     * 根据id查询小散工程-组织
     *
     * @param bean 条件
     * @return {@link Result<XsgcOrganizationRespDTO>}
     */
    @PostMapping(value = {"/show"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<XsgcOrganizationRespDTO> show(@RequestBody @Validated ScShowDTO bean) {
        return success(iXsgcOrganizationService.show(bean));
    }

    /**
     * 新建小散工程-组织
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/create"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> create(@RequestBody @Validated XsgcOrganizationCreateDTO bean) {
        return success(Objects.nonNull(iXsgcOrganizationService.create(bean)));
    }

    /**
     * 修改小散工程-组织
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/update"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> update(@RequestBody @Validated XsgcOrganizationUpdateDTO bean) {
        return success(Objects.nonNull(iXsgcOrganizationService.update(bean)));
    }

    /**
     * 删除小散工程-组织
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/delete"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result delete(@RequestBody @Validated ScDeleteDTO bean) {
        iXsgcOrganizationService.delete(bean.getIds());
        return success();
    }

    /**
     * 导出小散工程-组织
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/export"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result export(@RequestBody @Validated XsgcOrganizationQueryDTO bean) {
        iXsgcOrganizationService.export(bean);
        return success();
    }

}
