package cn.shencom.server.controller;

import cn.shencom.model.dto.create.XsgcCustomerInfoCreateDTO;
import cn.shencom.model.dto.query.XsgcCustomerInfoQueryDTO;
import cn.shencom.model.dto.resp.XsgcCustomerInfoRespDTO;
import cn.shencom.model.dto.update.XsgcCustomerInfoUpdateDTO;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.dto.ScDeleteDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.server.controller.BaseController;
import cn.shencom.server.service.IXsgcCustomerInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * 小散工程-客户信息表 的接口服务
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@RestController
@RequestMapping("/xsgc/customer/info")
public class XsgcCustomerInfoController extends BaseController {

    @Autowired
    private IXsgcCustomerInfoService iXsgcCustomerInfoService;

    /**
     * 查询小散工程-客户信息表列表
     *
     * @param bean 条件
     * @return {@link Result}<{@link Page}<{@link XsgcCustomerInfoRespDTO}>>
     */
    @PostMapping(value = {"/index"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Page<XsgcCustomerInfoRespDTO>> index(@RequestBody @Validated XsgcCustomerInfoQueryDTO bean) {
        return success(iXsgcCustomerInfoService.query(bean));
    }

    /**
     * 根据id查询小散工程-客户信息表
     *
     * @param bean 条件
     * @return {@link Result<XsgcCustomerInfoRespDTO>}
     */
    @PostMapping(value = {"/show"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<XsgcCustomerInfoRespDTO> show(@RequestBody @Validated ScShowDTO bean) {
        return success(iXsgcCustomerInfoService.show(bean));
    }



    /**
     * 根据组织id查询小散工程-客户信息表
     *
     * @param bean 条件
     * @return {@link Result<XsgcCustomerInfoRespDTO>}
     */
    @PostMapping(value = {"/show/byOrganizationId"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<XsgcCustomerInfoRespDTO> showByOrganizationId(@RequestBody @Validated ScShowDTO bean) {
        return success(iXsgcCustomerInfoService.showByOrganizationId(bean));
    }

    /**
     * 新建小散工程-客户信息表
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/create"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> create(@RequestBody @Validated XsgcCustomerInfoCreateDTO bean) {
        return success(Objects.nonNull(iXsgcCustomerInfoService.create(bean)));
    }

    /**
     * 修改小散工程-客户信息表
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/update"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> update(@RequestBody @Validated XsgcCustomerInfoUpdateDTO bean) {
        return success(Objects.nonNull(iXsgcCustomerInfoService.update(bean)));
    }

    /**
     * 删除小散工程-客户信息表
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/delete"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result delete(@RequestBody @Validated ScDeleteDTO bean) {
        iXsgcCustomerInfoService.delete(bean.getIds());
        return success();
    }

    /**
     * 导出小散工程-客户信息表
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/export"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result export(@RequestBody @Validated XsgcCustomerInfoQueryDTO bean) {
        iXsgcCustomerInfoService.export(bean);
        return success();
    }


//
//    /**
//     *  更新服务状态, 激活服务后才能正常使用
//     *  激活/关闭服务
//     */
//    @PostMapping(value = {"/update/status"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
//    public Result updateStatus(@RequestBody @Validated XsgcCustomerInfoUpdateDTO bean) {
//        iXsgcCustomerInfoService.updateStatus(bean);
//        return success();
//    }



    /**
     * 开通服务
     * 开通服务会添加服务记录，激活服务的时候才会真正开始服务
     */
    @PostMapping(value = {"/open/service"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result openService(@RequestBody @Validated XsgcCustomerInfoUpdateDTO bean) {
        iXsgcCustomerInfoService.openService(bean);
        return success();
    }


}
