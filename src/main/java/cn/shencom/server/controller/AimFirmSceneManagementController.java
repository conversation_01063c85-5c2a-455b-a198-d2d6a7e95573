package cn.shencom.server.controller;

import cn.shencom.common.util.DSConnection;
import cn.shencom.model.dto.AimFirmRelevanceSceneDTO;
import cn.shencom.model.dto.create.AimFirmSceneManagementCreateDTO;
import cn.shencom.model.dto.query.AimFirmSceneManagementQueryDTO;
import cn.shencom.model.dto.resp.AimFirmSceneManagementRespDTO;
import cn.shencom.model.dto.resp.AimSceneStatisticsDTO;
import cn.shencom.model.dto.update.AimFirmSceneManagementUpdateDTO;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.dto.ScDeleteDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.server.controller.BaseController;
import cn.shencom.server.service.IAimFirmSceneManagementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * 厂商场景管理 的接口服务
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@RestController
@RequestMapping("/aim/firm/scene/management")
public class AimFirmSceneManagementController extends BaseController {

    @Autowired
    private IAimFirmSceneManagementService iAimFirmSceneManagementService;

    /**
     * 查询厂商场景管理列表
     *
     * @param bean 条件
     * @return {@link Result}<{@link Page}<{@link AimFirmSceneManagementRespDTO}>>
     */
    @PostMapping(value = {"/index"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Page<AimFirmSceneManagementRespDTO>> index(@RequestBody @Validated AimFirmSceneManagementQueryDTO bean) {
        return success(iAimFirmSceneManagementService.query(bean));
    }

    /**
     * 根据id查询厂商场景管理
     *
     * @param bean 条件
     * @return {@link Result<AimFirmSceneManagementRespDTO>}
     */
    @PostMapping(value = {"/show"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<AimFirmSceneManagementRespDTO> show(@RequestBody @Validated ScShowDTO bean) {
        return success(iAimFirmSceneManagementService.show(bean));
    }

    /**
     * 新建厂商场景管理
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/create"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> create(@RequestBody @Validated AimFirmSceneManagementCreateDTO bean) {
        return success(Objects.nonNull(iAimFirmSceneManagementService.create(bean)));
    }

    /**
     * 修改厂商场景管理
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/update"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> update(@RequestBody @Validated AimFirmSceneManagementUpdateDTO bean) {
        return success(Objects.nonNull(iAimFirmSceneManagementService.update(bean)));
    }

    /**
     * 删除厂商场景管理
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/delete"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result delete(@RequestBody @Validated ScDeleteDTO bean) {
        iAimFirmSceneManagementService.delete(bean.getIds());
        return success();
    }

    /**
     * 导出厂商场景管理
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/export"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result export(@RequestBody @Validated AimFirmSceneManagementQueryDTO bean) {
        iAimFirmSceneManagementService.export(bean);
        return success();
    }

    /**
     * 厂商场景关联AI场景
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/scene/associate"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result relevance(@RequestBody @Validated AimFirmRelevanceSceneDTO bean) {
        iAimFirmSceneManagementService.sceneRelevance(bean);
        return success();
    }

    /**
     * 厂商场景取消关联AI场景
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/scene/disassociate"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result disassociate(@RequestBody @Validated AimFirmRelevanceSceneDTO bean) {
        iAimFirmSceneManagementService.sceneDisassociate(bean);
        return success();
    }

    /**
     * 统计关联厂商数、厂商场景数
     */
    @PostMapping(value = {"/scene/statistics"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    @DSConnection(onlyRead = true)
    public Result<AimSceneStatisticsDTO> statistics(@RequestBody @Validated AimFirmSceneManagementQueryDTO bean) {
        return success(iAimFirmSceneManagementService.sceneStatistics(bean));
    }




    /**
     * 统计关联厂商数、厂商场景数
     */
    @PostMapping(value = {"/list"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    @DSConnection(onlyRead = true)
    public Result<List<AimFirmSceneManagementRespDTO>> list() {
        return success(iAimFirmSceneManagementService.list());
    }

}
