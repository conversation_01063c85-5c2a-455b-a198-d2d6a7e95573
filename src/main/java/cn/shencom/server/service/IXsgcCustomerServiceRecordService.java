package cn.shencom.server.service;

import cn.shencom.model.XsgcCustomerServiceRecord;
import cn.shencom.model.dto.create.XsgcCustomerServiceRecordCreateDTO;
import cn.shencom.model.dto.query.XsgcCustomerServiceRecordQueryDTO;
import cn.shencom.model.dto.resp.XsgcCustomerServiceRecordRespDTO;
import cn.shencom.model.dto.update.XsgcCustomerServiceRecordUpdateDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 小散工程-客户服务开通记录 的服务接口
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
public interface IXsgcCustomerServiceRecordService {

    /**
     * 查询小散工程-客户服务开通记录列表
     *
     * @param bean 查询DTO
     * @return 分页列表
     */
    Page<XsgcCustomerServiceRecordRespDTO> query(XsgcCustomerServiceRecordQueryDTO bean);



    /**
     * 查询小散工程-客户服务开通记录列表
     *
     * @param bean 查询DTO
     * @return 分页列表
     */
    XsgcCustomerServiceRecord lastRecord(XsgcCustomerServiceRecordQueryDTO bean);

    /**
     * 根据id查询小散工程-客户服务开通记录
     *
     * @param bean 查询DTO
     * @return 单个实体
     */
    XsgcCustomerServiceRecordRespDTO show(ScShowDTO bean);

    /**
     * 新建小散工程-客户服务开通记录
     *
     * @param bean 新建DTO
     * @return 创建实体
     */
    XsgcCustomerServiceRecord create(XsgcCustomerServiceRecordCreateDTO bean);

    /**
     * 修改小散工程-客户服务开通记录
     *
     * @param bean 修改DTO
     * @return 修改实体
     */
    XsgcCustomerServiceRecord update(XsgcCustomerServiceRecordUpdateDTO bean);

    /**
     * 删除小散工程-客户服务开通记录
     *
     * @param ids id集合
     */
    void delete(List<String> ids);

    /**
     * 导出小散工程-客户服务开通记录
     *
     * @param bean 导出DTO
     */
    void export(XsgcCustomerServiceRecordQueryDTO bean);

}
