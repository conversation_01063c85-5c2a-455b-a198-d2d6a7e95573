package cn.shencom.server.service;

import cn.shencom.model.XsgcOrganization;
import cn.shencom.model.dto.create.XsgcOrganizationCreateDTO;
import cn.shencom.model.dto.query.XsgcOrganizationQueryDTO;
import cn.shencom.model.dto.resp.XsgcOrganizationRespDTO;
import cn.shencom.model.dto.update.XsgcOrganizationUpdateDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 小散工程-组织 的服务接口
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
public interface IXsgcOrganizationService {

    /**
     * 查询小散工程-组织列表
     *
     * @param bean 查询DTO
     * @return 分页列表
     */
    Page<XsgcOrganizationRespDTO> query(XsgcOrganizationQueryDTO bean);

    /**
     * 根据id查询小散工程-组织
     *
     * @param bean 查询DTO
     * @return 单个实体
     */
    XsgcOrganizationRespDTO show(ScShowDTO bean);

    /**
     * 新建小散工程-组织
     *
     * @param bean 新建DTO
     * @return 创建实体
     */
    XsgcOrganization create(XsgcOrganizationCreateDTO bean);

    /**
     * 修改小散工程-组织
     *
     * @param bean 修改DTO
     * @return 修改实体
     */
    XsgcOrganization update(XsgcOrganizationUpdateDTO bean);

    /**
     * 删除小散工程-组织
     *
     * @param ids id集合
     */
    void delete(List<String> ids);

    /**
     * 导出小散工程-组织
     *
     * @param bean 导出DTO
     */
    void export(XsgcOrganizationQueryDTO bean);

}
