package cn.shencom.server.service;

import cn.shencom.model.AimSceneManagement;
import cn.shencom.model.dto.AimSceneRelevanceFirmDTO;
import cn.shencom.model.dto.create.AimSceneManagementCreateDTO;
import cn.shencom.model.dto.query.AimSceneManagementQueryDTO;
import cn.shencom.model.dto.resp.AimSceneCategoryDTO;
import cn.shencom.model.dto.resp.AimSceneManagementRespDTO;
import cn.shencom.model.dto.resp.AimSceneStatisticsDTO;
import cn.shencom.model.dto.update.AimSceneManagementUpdateDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * AI场景管理 的服务接口
 *
 * <AUTHOR>
 * @since 2022-07-26
 */
public interface IAimSceneManagementService {

    /**
     * 查询AI场景管理列表
     *
     * @param bean 查询DTO
     * @return 分页列表
     */
    Page<AimSceneManagementRespDTO> query(AimSceneManagementQueryDTO bean);

    // 数据存redis
    void storeManagementList();

    /**
     * 根据id查询AI场景管理
     *
     * @param bean 查询DTO
     * @return 单个实体
     */
    AimSceneManagementRespDTO show(ScShowDTO bean);

    /**
     * 新建AI场景管理
     *
     * @param bean 新建DTO
     * @return 创建实体
     */
    AimSceneManagement create(AimSceneManagementCreateDTO bean);

    /**
     * 修改AI场景管理
     *
     * @param bean 修改DTO
     * @return 修改实体
     */
    AimSceneManagement update(AimSceneManagementUpdateDTO bean);

    /**
     * 删除AI场景管理
     *
     * @param ids id集合
     */
    void delete(List<String> ids);

    /**
     * 导出AI场景管理
     *
     * @param bean 导出DTO
     */
    void export(AimSceneManagementQueryDTO bean);

    void sceneRelevance(AimSceneRelevanceFirmDTO bean);

    void sceneDisassociate(AimSceneRelevanceFirmDTO bean);

    AimSceneStatisticsDTO sceneStatistics(AimSceneManagementQueryDTO bean);

    List<String> codes(String aimSceneId);

    List<AimSceneCategoryDTO> getSceneAll();
}
