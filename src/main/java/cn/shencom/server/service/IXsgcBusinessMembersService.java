package cn.shencom.server.service;

import cn.shencom.model.XsgcBusinessMembers;
import cn.shencom.model.dto.create.XsgcBusinessMembersCreateDTO;
import cn.shencom.model.dto.query.XsgcBusinessMembersQueryDTO;
import cn.shencom.model.dto.resp.XsgcBusinessMembersRespDTO;
import cn.shencom.model.dto.update.XsgcBusinessMembersUpdateDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 小散工程-业务人员 的服务接口
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
public interface IXsgcBusinessMembersService {

    /**
     * 查询小散工程-业务人员列表
     *
     * @param bean 查询DTO
     * @return 分页列表
     */
    Page<XsgcBusinessMembersRespDTO> query(XsgcBusinessMembersQueryDTO bean);

    /**
     * 根据id查询小散工程-业务人员
     *
     * @param bean 查询DTO
     * @return 单个实体
     */
    XsgcBusinessMembersRespDTO show(ScShowDTO bean);

    /**
     * 新建小散工程-业务人员
     *
     * @param bean 新建DTO
     * @return 创建实体
     */
    XsgcBusinessMembers create(XsgcBusinessMembersCreateDTO bean);

    /**
     * 修改小散工程-业务人员
     *
     * @param bean 修改DTO
     * @return 修改实体
     */
    XsgcBusinessMembers update(XsgcBusinessMembersUpdateDTO bean);

    /**
     * 删除小散工程-业务人员
     *
     * @param ids id集合
     */
    void delete(List<String> ids);

    /**
     * 导出小散工程-业务人员
     *
     * @param bean 导出DTO
     */
    void export(XsgcBusinessMembersQueryDTO bean);

}
