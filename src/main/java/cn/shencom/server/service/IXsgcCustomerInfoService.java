package cn.shencom.server.service;

import cn.shencom.model.XsgcCustomerInfo;
import cn.shencom.model.dto.create.XsgcCustomerInfoCreateDTO;
import cn.shencom.model.dto.query.XsgcCustomerInfoQueryDTO;
import cn.shencom.model.dto.resp.XsgcCustomerInfoRespDTO;
import cn.shencom.model.dto.update.XsgcCustomerInfoUpdateDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 小散工程-客户信息表 的服务接口
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
public interface IXsgcCustomerInfoService {

    /**
     * 查询小散工程-客户信息表列表
     *
     * @param bean 查询DTO
     * @return 分页列表
     */
    Page<XsgcCustomerInfoRespDTO> query(XsgcCustomerInfoQueryDTO bean);

    /**
     * 根据id查询小散工程-客户信息表
     *
     * @param bean 查询DTO
     * @return 单个实体
     */
    XsgcCustomerInfoRespDTO show(ScShowDTO bean);




    /**
     * 根据组织id查询小散工程-客户信息表
     *
     * @param bean 查询DTO
     * @return 单个实体
     */
    XsgcCustomerInfoRespDTO showByOrganizationId(ScShowDTO bean);



    /**
     * 新建小散工程-客户信息表
     *
     * @param bean 新建DTO
     * @return 创建实体
     */
    XsgcCustomerInfo create(XsgcCustomerInfoCreateDTO bean);

    /**
     * 修改小散工程-客户信息表
     *
     * @param bean 修改DTO
     * @return 修改实体
     */
    XsgcCustomerInfo update(XsgcCustomerInfoUpdateDTO bean);

    /**
     * 删除小散工程-客户信息表
     *
     * @param ids id集合
     */
    void delete(List<String> ids);

    /**
     * 导出小散工程-客户信息表
     *
     * @param bean 导出DTO
     */
    void export(XsgcCustomerInfoQueryDTO bean);



    void updateStatus(XsgcCustomerInfoUpdateDTO bean);


    /**
     * 自动关闭服务
     */
    void autoCloseService();


    /**
     * 关闭服务
     */
    void closeService(XsgcCustomerInfoUpdateDTO bean);


    /**
     *   激活服务
     */
    void activeService(XsgcCustomerInfoUpdateDTO bean);


    /**
     * 开通服务
     */
    void openService(XsgcCustomerInfoUpdateDTO bean);

}
