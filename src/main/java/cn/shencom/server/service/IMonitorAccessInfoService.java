package cn.shencom.server.service;

import cn.shencom.model.MonitorAccessInfo;
import cn.shencom.model.dto.create.MonitorAccessInfoCreateDTO;
import cn.shencom.model.dto.query.MonitorAccessInfoQueryDTO;
import cn.shencom.model.dto.resp.MonitorAccessInfoRespDTO;
import cn.shencom.model.dto.update.MonitorAccessInfoUpdateDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 小散工程-监管工单流程-监控接入详情 的服务接口
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
public interface IMonitorAccessInfoService {

    /**
     * 查询小散工程-监管工单流程-监控接入详情列表
     *
     * @param bean 查询DTO
     * @return 分页列表
     */
    Page<MonitorAccessInfoRespDTO> query(MonitorAccessInfoQueryDTO bean);

    /**
     * 根据id查询小散工程-监管工单流程-监控接入详情
     *
     * @param bean 查询DTO
     * @return 单个实体
     */
    MonitorAccessInfoRespDTO show(ScShowDTO bean);

    /**
     * 新建小散工程-监管工单流程-监控接入详情
     *
     * @param bean 新建DTO
     * @return 创建实体
     */
    MonitorAccessInfo create(MonitorAccessInfoCreateDTO bean);

    /**
     * 修改小散工程-监管工单流程-监控接入详情
     *
     * @param bean 修改DTO
     * @return 修改实体
     */
    MonitorAccessInfo update(MonitorAccessInfoUpdateDTO bean);

    /**
     * 删除小散工程-监管工单流程-监控接入详情
     *
     * @param ids id集合
     */
    void delete(List<String> ids);

    /**
     * 导出小散工程-监管工单流程-监控接入详情
     *
     * @param bean 导出DTO
     */
    void export(MonitorAccessInfoQueryDTO bean);


    /**
     * 创建或者更新监管流程中  当前工程关联的摄像头信息
     * @param monitorOrderId
     */
    void createOrUpdateInfo(String projectId,String monitorOrderId);

}
