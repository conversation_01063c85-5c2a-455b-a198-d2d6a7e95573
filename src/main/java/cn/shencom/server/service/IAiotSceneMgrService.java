package cn.shencom.server.service;

import cn.shencom.model.AiotSceneMgr;
import cn.shencom.model.dto.create.AiotSceneMgrCreateDTO;
import cn.shencom.model.dto.query.AiotSceneMgrQueryDTO;
import cn.shencom.model.dto.resp.AiotSceneMgrRespDTO;
import cn.shencom.model.dto.update.AiotSceneMgrUpdateDTO;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.dto.ScShowDTO;

import java.util.List;

/**
 * 场景管理 的服务接口
 *
 * <AUTHOR>
 * @since 2024-08-03
 */
public interface IAiotSceneMgrService {

    /**
     * 查询场景管理数据集
     *
     * @param bean 查询DTO
     * @return 场景管理数据集
     */
    Result query(AiotSceneMgrQueryDTO bean);

    /**
     * 根据id查询场景管理
     *
     * @param bean 查询DTO
     * @return 单个实体
     */
    AiotSceneMgrRespDTO show(ScShowDTO bean);

    /**
     * 新建场景管理
     *
     * @param bean 新建DTO
     * @return 创建实体
     */
    AiotSceneMgr create(AiotSceneMgrCreateDTO bean);

    /**
     * 修改场景管理
     *
     * @param bean 修改DTO
     * @return 修改实体
     */
    AiotSceneMgr update(AiotSceneMgrUpdateDTO bean);

    /**
     * 删除场景管理
     *
     * @param ids id集合
     */
    void delete(List<String> ids);

    /**
     * 导出场景管理
     *
     * @param bean 导出DTO
     */
    boolean export(AiotSceneMgrQueryDTO bean);


    List<AiotSceneMgrRespDTO> list(AiotSceneMgrQueryDTO bean);

}
