package cn.shencom.server.service;

import cn.shencom.model.MonitorRecycleReservation;
import cn.shencom.model.dto.create.MonitorRecycleReservationCreateDTO;
import cn.shencom.model.dto.query.MonitorRecycleReservationQueryDTO;
import cn.shencom.model.dto.resp.MonitorRecycleReservationRespDTO;
import cn.shencom.model.dto.update.MonitorRecycleReservationUpdateDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 小散工程-监管工单流程-回收预约详情 的服务接口
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
public interface IMonitorRecycleReservationService {

    /**
     * 查询小散工程-监管工单流程-回收预约详情列表
     *
     * @param bean 查询DTO
     * @return 分页列表
     */
    Page<MonitorRecycleReservationRespDTO> query(MonitorRecycleReservationQueryDTO bean);

    /**
     * 根据id查询小散工程-监管工单流程-回收预约详情
     *
     * @param bean 查询DTO
     * @return 单个实体
     */
    MonitorRecycleReservationRespDTO show(ScShowDTO bean);

    /**
     * 新建小散工程-监管工单流程-回收预约详情
     *
     * @param bean 新建DTO
     * @return 创建实体
     */
    MonitorRecycleReservation create(MonitorRecycleReservationCreateDTO bean);

    /**
     * 修改小散工程-监管工单流程-回收预约详情
     *
     * @param bean 修改DTO
     * @return 修改实体
     */
    MonitorRecycleReservation update(MonitorRecycleReservationUpdateDTO bean);

    /**
     * 删除小散工程-监管工单流程-回收预约详情
     *
     * @param ids id集合
     */
    void delete(List<String> ids);

    /**
     * 导出小散工程-监管工单流程-回收预约详情
     *
     * @param bean 导出DTO
     */
    void export(MonitorRecycleReservationQueryDTO bean);

}
