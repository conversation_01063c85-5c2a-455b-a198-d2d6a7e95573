package cn.shencom.server.service;

import cn.shencom.model.ContractingUnitOrganizationRelate;
import cn.shencom.model.dto.create.ContractingUnitOrganizationRelateCreateDTO;
import cn.shencom.model.dto.query.ContractingUnitOrganizationRelateQueryDTO;
import cn.shencom.model.dto.resp.ContractingUnitOrganizationRelateRespDTO;
import cn.shencom.model.dto.update.ContractingUnitOrganizationRelateUpdateDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 施工单位组织关联表 的服务接口
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
public interface IContractingUnitOrganizationRelateService {

    /**
     * 查询施工单位组织关联表列表
     *
     * @param bean 查询DTO
     * @return 分页列表
     */
    Page<ContractingUnitOrganizationRelateRespDTO> query(ContractingUnitOrganizationRelateQueryDTO bean);

    /**
     * 根据id查询施工单位组织关联表
     *
     * @param bean 查询DTO
     * @return 单个实体
     */
    ContractingUnitOrganizationRelateRespDTO show(ScShowDTO bean);

    /**
     * 新建施工单位组织关联表
     *
     * @param bean 新建DTO
     * @return 创建实体
     */
    ContractingUnitOrganizationRelate create(ContractingUnitOrganizationRelateCreateDTO bean);

    /**
     * 修改施工单位组织关联表
     *
     * @param bean 修改DTO
     * @return 修改实体
     */
    ContractingUnitOrganizationRelate update(ContractingUnitOrganizationRelateUpdateDTO bean);

    /**
     * 创建或修改施工单位组织关联表
     *
     * @param bean 修改DTO
     * @return 修改实体
     */
    ContractingUnitOrganizationRelate createOrUpdate(ContractingUnitOrganizationRelateUpdateDTO bean);

    /**
     * 删除施工单位组织关联表
     *
     * @param ids id集合
     */
    void delete(List<String> ids);

    /**
     * 导出施工单位组织关联表
     *
     * @param bean 导出DTO
     */
    void export(ContractingUnitOrganizationRelateQueryDTO bean);

}
