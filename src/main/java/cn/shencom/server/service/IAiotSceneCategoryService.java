package cn.shencom.server.service;

import cn.shencom.model.AiotSceneCategory;
import cn.shencom.model.dto.AiotEventPushDTO;
import cn.shencom.model.dto.create.AiotSceneCategoryCreateDTO;
import cn.shencom.model.dto.query.AiotSceneCategoryQueryDTO;
import cn.shencom.model.dto.resp.AiotSceneCategoryRespDTO;
import cn.shencom.model.dto.update.AiotSceneCategoryUpdateDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * aiot_scene_category 的服务接口
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
public interface IAiotSceneCategoryService {

    /**
     * 查询aiot_scene_category列表
     *
     * @param bean 查询DTO
     * @return 分页列表
     */
    Page<AiotSceneCategoryRespDTO> query(AiotSceneCategoryQueryDTO bean);

    /**
     * 根据id查询aiot_scene_category
     *
     * @param bean 查询DTO
     * @return 单个实体
     */
    AiotSceneCategoryRespDTO show(ScShowDTO bean);

    /**
     * 新建aiot_scene_category
     *
     * @param bean 新建DTO
     * @return 创建实体
     */
    AiotSceneCategory create(AiotSceneCategoryCreateDTO bean);

    /**
     * 修改aiot_scene_category
     *
     * @param bean 修改DTO
     * @return 修改实体
     */
    AiotSceneCategory update(AiotSceneCategoryUpdateDTO bean);

    /**
     * 删除aiot_scene_category
     *
     * @param ids id集合
     */
    void delete(List<String> ids);

    /**
     * 导出aiot_scene_category
     *
     * @param bean 导出DTO
     */
    void export(AiotSceneCategoryQueryDTO bean);

    @Transactional(rollbackFor = Exception.class)
    AiotSceneCategory updateOrCreate(String cateCode, String sceneCode);
}
