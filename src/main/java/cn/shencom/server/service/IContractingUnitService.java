package cn.shencom.server.service;

import cn.shencom.model.ContractingUnit;
import cn.shencom.model.dto.create.ContractingUnitCreateDTO;
import cn.shencom.model.dto.query.ContractingUnitQueryDTO;
import cn.shencom.model.dto.resp.ContractingUnitRespDTO;
import cn.shencom.model.dto.update.ContractingUnitUpdateDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 施工单位表 的服务接口
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
public interface IContractingUnitService {

    /**
     * 查询施工单位表列表
     *
     * @param bean 查询DTO
     * @return 分页列表
     */
    Page<ContractingUnitRespDTO> query(ContractingUnitQueryDTO bean);

    /**
     * 根据id查询施工单位表
     *
     * @param bean 查询DTO
     * @return 单个实体
     */
    ContractingUnitRespDTO show(ScShowDTO bean);

    /**
     * 新建施工单位表
     *
     * @param bean 新建DTO
     * @return 创建实体
     */
    ContractingUnit create(ContractingUnitCreateDTO bean);

    /**
     * 修改施工单位表
     *
     * @param bean 修改DTO
     * @return 修改实体
     */
    ContractingUnit update(ContractingUnitUpdateDTO bean);

    /**
     * 创建或修改施工单位表
     *
     * @param bean 修改DTO
     * @return 修改实体
     */
    ContractingUnit createOrUpdate(ContractingUnitUpdateDTO bean);

    /**
     * 删除施工单位表
     *
     * @param ids id集合
     */
    void delete(List<String> ids);

    /**
     * 导出施工单位表
     *
     * @param bean 导出DTO
     */
    void export(ContractingUnitQueryDTO bean);

}
