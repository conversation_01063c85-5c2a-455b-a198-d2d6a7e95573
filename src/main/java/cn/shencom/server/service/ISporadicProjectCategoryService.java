package cn.shencom.server.service;

import cn.shencom.model.SporadicProjectCategory;
import cn.shencom.model.dto.SporadicProjectCategoryTreeDTO;
import cn.shencom.model.dto.create.SporadicProjectCategoryCreateDTO;
import cn.shencom.model.dto.query.SporadicProjectCategoryQueryDTO;
import cn.shencom.model.dto.resp.SporadicProjectCategoryRespDTO;
import cn.shencom.model.dto.update.SporadicProjectCategoryUpdateDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 小散工程分类 的服务接口
 *
 * <AUTHOR>
 * @since 2025-05-29
 */
public interface ISporadicProjectCategoryService {

    /**
     * 查询小散工程分类列表
     *
     * @param bean 查询DTO
     * @return 分页列表
     */
    Page<SporadicProjectCategoryRespDTO> query(SporadicProjectCategoryQueryDTO bean);
    List<SporadicProjectCategoryTreeDTO> tree();

    /**
     * 根据id查询小散工程分类
     *
     * @param bean 查询DTO
     * @return 单个实体
     */
    SporadicProjectCategoryRespDTO show(ScShowDTO bean);

    /**
     * 新建小散工程分类
     *
     * @param bean 新建DTO
     * @return 创建实体
     */
    SporadicProjectCategory create(SporadicProjectCategoryCreateDTO bean);

    /**
     * 修改小散工程分类
     *
     * @param bean 修改DTO
     * @return 修改实体
     */
    SporadicProjectCategory update(SporadicProjectCategoryUpdateDTO bean);

    /**
     * 删除小散工程分类
     *
     * @param ids id集合
     */
    void delete(List<String> ids);

    /**
     * 导出小散工程分类
     *
     * @param bean 导出DTO
     */
    void export(SporadicProjectCategoryQueryDTO bean);

}
