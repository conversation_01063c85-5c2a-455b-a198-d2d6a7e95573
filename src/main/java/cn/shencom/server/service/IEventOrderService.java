package cn.shencom.server.service;

import cn.shencom.model.EventOrder;
import cn.shencom.model.dto.create.EventOrderCreateDTO;
import cn.shencom.model.dto.query.EventOrderQueryDTO;
import cn.shencom.model.dto.query.EventOrderTypeQueryDTO;
import cn.shencom.model.dto.resp.ESEventOrderRespDTO;
import cn.shencom.model.dto.resp.EventOrderRespDTO;
import cn.shencom.model.dto.resp.EventOrderTypeRespDTO;
import cn.shencom.model.dto.update.EventOrderUpdateDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 事件工单表 的服务接口
 *
 * <AUTHOR>
 * @since 2021-06-29
 */
public interface IEventOrderService {

    /**
     * 查询事件工单表列表
     *
     * @param bean 查询DTO
     * @return 分页列表
     */
    Page<EventOrderRespDTO> query(EventOrderQueryDTO bean);


    Page<ESEventOrderRespDTO> esQuery(EventOrderQueryDTO bean) throws Exception;

    /**
     * 根据id查询事件工单表
     *
     * @param bean 查询DTO
     * @return 单个实体
     */
    EventOrderRespDTO show(ScShowDTO bean);

    /**
     * 新建事件工单表
     *
     * @param bean 新建DTO
     * @return 创建实体
     */
    EventOrder create(EventOrderCreateDTO bean);

    /**
     * 修改事件工单表
     *
     * @param bean 修改DTO
     * @return 修改实体
     */
    EventOrder update(EventOrderUpdateDTO bean);

    /**
     * 删除事件工单表
     *
     * @param ids id集合
     */
    void delete(List<String> ids);

    /**
     * 导出事件工单表
     *
     * @param bean 导出DTO
     */
    boolean export(EventOrderQueryDTO bean);




    /**
     * 查询监控事件类型列表
     *
     * @param bean 查询DTO
     * @return 分页列表
     */
    List<EventOrderTypeRespDTO> typeList(EventOrderTypeQueryDTO bean);

}
