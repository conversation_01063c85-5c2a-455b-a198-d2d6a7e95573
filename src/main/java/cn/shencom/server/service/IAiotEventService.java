package cn.shencom.server.service;

import cn.shencom.model.AiotEvent;
import cn.shencom.model.dto.AiotEventPushDTO;
import cn.shencom.model.dto.create.AiotEventCreateDTO;
import cn.shencom.model.dto.query.AiotEventMobileQueryDTO;
import cn.shencom.model.dto.query.AiotEventQueryDTO;
import cn.shencom.model.dto.resp.AiotEventMobileRespDTO;
import cn.shencom.model.dto.resp.AiotEventRespDTO;
import cn.shencom.model.dto.update.AiotEventUpdateDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 监控事件 的服务接口
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
public interface IAiotEventService {

    /**
     * 查询监控事件列表
     *
     * @param bean 查询DTO
     * @return 分页列表
     */
    Page<AiotEventRespDTO> query(AiotEventQueryDTO bean);

    /**
     * 根据id查询监控事件
     *
     * @param bean 查询DTO
     * @return 单个实体
     */
    AiotEventRespDTO show(ScShowDTO bean);

    /**
     * 新建监控事件
     *
     * @param bean 新建DTO
     * @return 创建实体
     */
    AiotEvent create(AiotEventCreateDTO bean);

    /**
     * 修改监控事件
     *
     * @param bean 修改DTO
     * @return 修改实体
     */
    AiotEvent update(AiotEventUpdateDTO bean);

    /**
     * 删除监控事件
     *
     * @param ids id集合
     */
    void delete(List<String> ids);

    /**
     * 导出监控事件
     *
     * @param bean 导出DTO
     */
    void export(AiotEventQueryDTO bean);

    @Transactional(rollbackFor = Exception.class)
    AiotEvent updateOrCreate(AiotEventPushDTO bean);

    void push(AiotEventPushDTO bean);

    /**
     * 查询监控事件列表 - 移动端专用
     *
     * @param bean 查询DTO
     * @return 分页列表
     */
    Page<AiotEventMobileRespDTO> mobileIndex(AiotEventMobileQueryDTO bean);

//    void createEventOrder(AiotEvent bean);
}
