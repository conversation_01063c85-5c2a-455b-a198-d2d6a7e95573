package cn.shencom.server.service;

import cn.shencom.model.AiotScene;
import cn.shencom.model.dto.create.AiotSceneCreateDTO;
import cn.shencom.model.dto.query.AiotSceneQueryDTO;
import cn.shencom.model.dto.resp.AiotSceneRespDTO;
import cn.shencom.model.dto.update.AiotSceneUpdateDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * aiot场景分类 的服务接口
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
public interface IAiotSceneService {

    /**
     * 查询aiot场景分类列表
     *
     * @param bean 查询DTO
     * @return 分页列表
     */
    Page<AiotSceneRespDTO> query(AiotSceneQueryDTO bean);

    /**
     * 根据id查询aiot场景分类
     *
     * @param bean 查询DTO
     * @return 单个实体
     */
    AiotSceneRespDTO show(ScShowDTO bean);

    /**
     * 新建aiot场景分类
     *
     * @param bean 新建DTO
     * @return 创建实体
     */
    AiotScene create(AiotSceneCreateDTO bean);

    /**
     * 修改aiot场景分类
     *
     * @param bean 修改DTO
     * @return 修改实体
     */
    AiotScene update(AiotSceneUpdateDTO bean);

    /**
     * 删除aiot场景分类
     *
     * @param ids id集合
     */
    void delete(List<String> ids);

    /**
     * 导出aiot场景分类
     *
     * @param bean 导出DTO
     */
    void export(AiotSceneQueryDTO bean);

    @Transactional(rollbackFor = Exception.class)
    AiotScene updateOrCreate(String code, String name);
}
