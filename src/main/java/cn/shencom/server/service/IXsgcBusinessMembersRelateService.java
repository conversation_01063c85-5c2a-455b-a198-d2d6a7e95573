package cn.shencom.server.service;

import cn.shencom.model.XsgcBusinessMembersRelate;
import cn.shencom.model.dto.create.XsgcBusinessMembersRelateCreateDTO;
import cn.shencom.model.dto.query.XsgcBusinessMembersRelateQueryDTO;
import cn.shencom.model.dto.resp.XsgcBusinessMembersRelateRespDTO;
import cn.shencom.model.dto.update.XsgcBusinessMembersRelateUpdateDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 小散工程-业务人员客户关联表 的服务接口
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
public interface IXsgcBusinessMembersRelateService {

    /**
     * 查询小散工程-业务人员客户关联表列表
     *
     * @param bean 查询DTO
     * @return 分页列表
     */
    Page<XsgcBusinessMembersRelateRespDTO> query(XsgcBusinessMembersRelateQueryDTO bean);

    /**
     * 根据id查询小散工程-业务人员客户关联表
     *
     * @param bean 查询DTO
     * @return 单个实体
     */
    XsgcBusinessMembersRelateRespDTO show(ScShowDTO bean);

    /**
     * 新建小散工程-业务人员客户关联表
     *
     * @param bean 新建DTO
     * @return 创建实体
     */
    XsgcBusinessMembersRelate create(XsgcBusinessMembersRelateCreateDTO bean);

    /**
     * 修改小散工程-业务人员客户关联表
     *
     * @param bean 修改DTO
     * @return 修改实体
     */
    XsgcBusinessMembersRelate update(XsgcBusinessMembersRelateUpdateDTO bean);

    /**
     * 删除小散工程-业务人员客户关联表
     *
     * @param ids id集合
     */
    void delete(List<String> ids);

    /**
     * 导出小散工程-业务人员客户关联表
     *
     * @param bean 导出DTO
     */
    void export(XsgcBusinessMembersRelateQueryDTO bean);


    Page<XsgcBusinessMembersRelateRespDTO> relevanceIndex(XsgcBusinessMembersRelateQueryDTO bean);


    Page<XsgcBusinessMembersRelateRespDTO> notRelevanceIndex(XsgcBusinessMembersRelateQueryDTO bean);


    void relevance(XsgcBusinessMembersRelateUpdateDTO bean);


    void moreRelevance(List<XsgcBusinessMembersRelateUpdateDTO> bean);

}
