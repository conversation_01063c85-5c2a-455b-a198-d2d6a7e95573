package cn.shencom.server.service;

import cn.shencom.model.FnRmsv3MembersTypeRelateBinding;
import cn.shencom.model.dto.create.FnRmsv3MembersTypeRelateBindingCreateDTO;
import cn.shencom.model.dto.query.FnRmsv3MembersTypeRelateBindingQueryDTO;
import cn.shencom.model.dto.resp.FnRmsv3MembersTypeRelateBindingRespDTO;
import cn.shencom.model.dto.update.FnRmsv3MembersTypeRelateBindingUpdateDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 小散工程-组织团队成员区域关联关系 的服务接口
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
public interface IFnRmsv3MembersTypeRelateBindingService {

    /**
     * 查询小散工程-组织团队成员区域关联关系列表
     *
     * @param bean 查询DTO
     * @return 分页列表
     */
    Page<FnRmsv3MembersTypeRelateBindingRespDTO> query(FnRmsv3MembersTypeRelateBindingQueryDTO bean);

    /**
     * 根据id查询小散工程-组织团队成员区域关联关系
     *
     * @param bean 查询DTO
     * @return 单个实体
     */
    FnRmsv3MembersTypeRelateBindingRespDTO show(ScShowDTO bean);

    /**
     * 新建小散工程-组织团队成员区域关联关系
     *
     * @param bean 新建DTO
     * @return 创建实体
     */
    FnRmsv3MembersTypeRelateBinding create(FnRmsv3MembersTypeRelateBindingCreateDTO bean);

    /**
     * 修改小散工程-组织团队成员区域关联关系
     *
     * @param bean 修改DTO
     * @return 修改实体
     */
    FnRmsv3MembersTypeRelateBinding update(FnRmsv3MembersTypeRelateBindingUpdateDTO bean);

    /**
     * 删除小散工程-组织团队成员区域关联关系
     *
     * @param ids id集合
     */
    void delete(List<String> ids);

    /**
     * 导出小散工程-组织团队成员区域关联关系
     *
     * @param bean 导出DTO
     */
    void export(FnRmsv3MembersTypeRelateBindingQueryDTO bean);

}
