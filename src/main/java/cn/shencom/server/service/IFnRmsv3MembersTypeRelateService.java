package cn.shencom.server.service;

import cn.shencom.model.FnRmsv3Members;
import cn.shencom.model.FnRmsv3MembersTypeRelate;
import cn.shencom.model.dto.MemberRelateStatics;
import cn.shencom.model.dto.create.FnRmsv3MembersTypeRelateCreateDTO;
import cn.shencom.model.dto.query.FnRmsv3MembersTypeRelateQueryDTO;
import cn.shencom.model.dto.resp.FnRmsv3MembersTypeRelateRespDTO;
import cn.shencom.model.dto.resp.XsgcOrganizationRespDTO;
import cn.shencom.model.dto.update.FnRmsv3MembersTypeRelateUpdateDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import org.springframework.data.domain.Page;
import java.util.List;

/**
 * 小散工程-组织团队成员关系 的服务接口
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
public interface IFnRmsv3MembersTypeRelateService {

    /**
     * 查询小散工程-组织团队成员关系列表
     *
     * @param bean 查询DTO
     * @return 分页列表
     */
    Page<FnRmsv3MembersTypeRelateRespDTO> query(FnRmsv3MembersTypeRelateQueryDTO bean);

    /**
     * 根据id查询小散工程-组织团队成员关系
     *
     * @param bean 查询DTO
     * @return 单个实体
     */
    FnRmsv3MembersTypeRelateRespDTO show(ScShowDTO bean);

    /**
     * 新建小散工程-组织团队成员关系
     *
     * @param bean 新建DTO
     * @return 创建实体
     */
    FnRmsv3MembersTypeRelate create(FnRmsv3MembersTypeRelateCreateDTO bean);

    /**
     * 修改小散工程-组织团队成员关系
     *
     * @param bean 修改DTO
     * @return 修改实体
     */
    FnRmsv3MembersTypeRelate update(FnRmsv3MembersTypeRelateUpdateDTO bean);

    /**
     * 修改小散工程-单独更新组织成员有效状态
     *
     * @param bean 修改DTO
     * @return 修改实体
     */
    FnRmsv3MembersTypeRelate updateStatus(FnRmsv3MembersTypeRelateUpdateDTO bean);

    /**
     * 删除小散工程-组织团队成员关系
     *
     * @param ids id集合
     */
    void delete(List<String> ids);

    /**
     * 导出小散工程-组织团队成员关系
     *
     * @param bean 导出DTO
     */
    void export(FnRmsv3MembersTypeRelateQueryDTO bean);

    /**
     * 团队角色删除后，清理权限和relate表
     */
    void deleteMember(FnRmsv3Members fnRmsv3Members);

    void createMore(FnRmsv3MembersTypeRelateCreateDTO bean);

    List<XsgcOrganizationRespDTO> allOrganization();

    List<XsgcOrganizationRespDTO> allOrganization(String userId);

    List<XsgcOrganizationRespDTO> getMemberByOrganizationIdAndUserId(String userId, String organizationId);

    MemberRelateStatics statics();

    /**
     * 查询当前用户在当前组织下的区域权限信息
     */
    FnRmsv3MembersTypeRelateRespDTO getMemberRelateByUserAndOrganization();

    /**
     * 查询当前用户在指定组织下的区域权限信息
     */
    FnRmsv3MembersTypeRelateRespDTO getMemberRelateByUserAndOrganization(String userId, String organizationId);

}
