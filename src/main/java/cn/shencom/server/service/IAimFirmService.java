package cn.shencom.server.service;

import cn.shencom.model.AimFirm;
import cn.shencom.model.dto.create.AimFirmCreateDTO;
import cn.shencom.model.dto.query.AimFirmQueryDTO;
import cn.shencom.model.dto.resp.AimFirmRespDTO;
import cn.shencom.model.dto.update.AimFirmUpdateDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 人工智能管理厂商表 的服务接口
 *
 * <AUTHOR>
 * @since 2022-08-02
 */
public interface IAimFirmService {

    /**
     * 查询人工智能管理厂商表列表
     *
     * @param bean 查询DTO
     * @return 分页列表
     */
    Page<AimFirmRespDTO> query(AimFirmQueryDTO bean);

    /**
     * 根据id查询人工智能管理厂商表
     *
     * @param bean 查询DTO
     * @return 单个实体
     */
    AimFirmRespDTO show(ScShowDTO bean);

    /**
     * 新建人工智能管理厂商表
     *
     * @param bean 新建DTO
     * @return 创建实体
     */
    AimFirm create(AimFirmCreateDTO bean);

    /**
     * 修改人工智能管理厂商表
     *
     * @param bean 修改DTO
     * @return 修改实体
     */
    AimFirm update(AimFirmUpdateDTO bean);

    /**
     * 删除人工智能管理厂商表
     *
     * @param ids id集合
     */
    void delete(List<String> ids);

    /**
     * 导出人工智能管理厂商表
     *
     * @param bean 导出DTO
     */
    void export(AimFirmQueryDTO bean);

    List<AimFirmRespDTO> getAllFirm();

}
