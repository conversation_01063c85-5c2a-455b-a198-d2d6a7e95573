package cn.shencom.server.service;

import cn.shencom.model.XsgcMessageRemind;
import cn.shencom.model.dto.XsgcMessageRemindDTO;
import cn.shencom.model.dto.create.XsgcMessageRemindCreateDTO;
import cn.shencom.model.dto.query.XsgcMessageRemindQueryDTO;
import cn.shencom.model.dto.resp.XsgcMessageRemindRespDTO;
import cn.shencom.model.dto.update.XsgcMessageRemindUpdateDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 小散工程-消息提醒表 的服务接口
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
public interface IXsgcMessageRemindService {

    /**
     * 查询小散工程-消息提醒表列表
     *
     * @param bean 查询DTO
     * @return 分页列表
     */
    Page<XsgcMessageRemindRespDTO> query(XsgcMessageRemindQueryDTO bean);

    /**
     * 根据id查询小散工程-消息提醒表
     *
     * @param bean 查询DTO
     * @return 单个实体
     */
    XsgcMessageRemindRespDTO show(ScShowDTO bean);

    /**
     * 新建小散工程-消息提醒表
     *
     * @param bean 新建DTO
     * @return 创建实体
     */
    XsgcMessageRemind create(XsgcMessageRemindCreateDTO bean);

    /**
     * 修改小散工程-消息提醒表
     *
     * @param bean 修改DTO
     * @return 修改实体
     */
    XsgcMessageRemind update(XsgcMessageRemindUpdateDTO bean);

    /**
     * 删除小散工程-消息提醒表
     *
     * @param ids id集合
     */
    void delete(List<String> ids);

    /**
     * 导出小散工程-消息提醒表
     *
     * @param bean 导出DTO
     */
    void export(XsgcMessageRemindQueryDTO bean);


    void createRemind(XsgcMessageRemindDTO bean);


    /**
     * 完成待办提醒
     */
    void completeRemind(String relateId,String userId);

}
