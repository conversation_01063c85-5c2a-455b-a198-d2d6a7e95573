package cn.shencom.server.service;

import cn.shencom.model.ConstructionUnitOrganizationRelate;
import cn.shencom.model.dto.create.ConstructionUnitOrganizationRelateCreateDTO;
import cn.shencom.model.dto.query.ConstructionUnitOrganizationRelateQueryDTO;
import cn.shencom.model.dto.resp.ConstructionUnitOrganizationRelateRespDTO;
import cn.shencom.model.dto.update.ConstructionUnitOrganizationRelateUpdateDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 施工单位组织关联表 的服务接口
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
public interface IConstructionUnitOrganizationRelateService {

    /**
     * 查询施工单位组织关联表列表
     *
     * @param bean 查询DTO
     * @return 分页列表
     */
    Page<ConstructionUnitOrganizationRelateRespDTO> query(ConstructionUnitOrganizationRelateQueryDTO bean);

    /**
     * 根据id查询施工单位组织关联表
     *
     * @param bean 查询DTO
     * @return 单个实体
     */
    ConstructionUnitOrganizationRelateRespDTO show(ScShowDTO bean);

    /**
     * 新建施工单位组织关联表
     *
     * @param bean 新建DTO
     * @return 创建实体
     */
    ConstructionUnitOrganizationRelate create(ConstructionUnitOrganizationRelateCreateDTO bean);

    /**
     * 修改施工单位组织关联表
     *
     * @param bean 修改DTO
     * @return 修改实体
     */
    ConstructionUnitOrganizationRelate update(ConstructionUnitOrganizationRelateUpdateDTO bean);

    /**
     * 删除施工单位组织关联表
     *
     * @param ids id集合
     */
    void delete(List<String> ids);

    /**
     * 导出施工单位组织关联表
     *
     * @param bean 导出DTO
     */
    void export(ConstructionUnitOrganizationRelateQueryDTO bean);

}
