package cn.shencom.server.service;

import cn.shencom.model.MonitorInstall;
import cn.shencom.model.dto.create.MonitorInstallCreateDTO;
import cn.shencom.model.dto.query.MonitorInstallQueryDTO;
import cn.shencom.model.dto.resp.MonitorInstallRespDTO;
import cn.shencom.model.dto.update.MonitorInstallUpdateDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 小散工程-监管工单流程-上门安装详情 的服务接口
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
public interface IMonitorInstallService {

    /**
     * 查询小散工程-监管工单流程-上门安装详情列表
     *
     * @param bean 查询DTO
     * @return 分页列表
     */
    Page<MonitorInstallRespDTO> query(MonitorInstallQueryDTO bean);

    /**
     * 根据id查询小散工程-监管工单流程-上门安装详情
     *
     * @param bean 查询DTO
     * @return 单个实体
     */
    MonitorInstallRespDTO show(ScShowDTO bean);

    /**
     * 新建小散工程-监管工单流程-上门安装详情
     *
     * @param bean 新建DTO
     * @return 创建实体
     */
    MonitorInstall create(MonitorInstallCreateDTO bean);

    /**
     * 修改小散工程-监管工单流程-上门安装详情
     *
     * @param bean 修改DTO
     * @return 修改实体
     */
    MonitorInstall update(MonitorInstallUpdateDTO bean);

    /**
     * 删除小散工程-监管工单流程-上门安装详情
     *
     * @param ids id集合
     */
    void delete(List<String> ids);

    /**
     * 导出小散工程-监管工单流程-上门安装详情
     *
     * @param bean 导出DTO
     */
    void export(MonitorInstallQueryDTO bean);

}
