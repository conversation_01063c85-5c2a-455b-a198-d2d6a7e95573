package cn.shencom.server.service;

import cn.shencom.model.MonitorOrder;
import cn.shencom.model.SporadicProject;
import cn.shencom.model.dto.create.MonitorOrderCreateDTO;
import cn.shencom.model.dto.query.MonitorOrderQueryDTO;
import cn.shencom.model.dto.resp.MonitorOrderRespDTO;
import cn.shencom.model.dto.resp.MonitorOrderRespOverviewDTO;
import cn.shencom.model.dto.update.MonitorOrderUpdateDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 小散工程-监管工单 的服务接口
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
public interface IMonitorOrderService {

    /**
     * 查询小散工程-监管工单列表
     *
     * @param bean 查询DTO
     * @return 分页列表
     */
    Page<MonitorOrderRespDTO> query(MonitorOrderQueryDTO bean);

    /**
     * 根据id查询小散工程-监管工单
     *
     * @param bean 查询DTO
     * @return 单个实体
     */
    MonitorOrderRespDTO show(ScShowDTO bean);

    /**
     * 新建小散工程-监管工单
     *
     * @param bean 新建DTO
     * @return 创建实体
     */
    MonitorOrder create(MonitorOrderCreateDTO bean);

    /**
     * 修改小散工程-监管工单
     *
     * @param bean 修改DTO
     * @return 修改实体
     */
    MonitorOrder update(MonitorOrderUpdateDTO bean);



    MonitorOrder  findByProjectId(String projectId);



    /**
     * 删除小散工程-监管工单
     *
     * @param ids id集合
     */
    void delete(List<String> ids);

    /**
     * 导出小散工程-监管工单
     *
     * @param bean 导出DTO
     */
    void export(MonitorOrderQueryDTO bean);

    /**
     * 获取数据概览统计
     *
     * @return 数据概览统计结果
     */
    MonitorOrderRespOverviewDTO getDataOverview();

    /**
     * 获取用户数据概览统计
     *
     * @return 数据概览统计结果
     */
    MonitorOrderRespOverviewDTO getDataOverviewUser();

    void createOrder(SporadicProject sporadicProject);
}
