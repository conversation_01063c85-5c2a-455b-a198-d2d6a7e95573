package cn.shencom.server.service;

import cn.shencom.model.EventCameraPointDevice;
import cn.shencom.model.MonitorFlow;
import cn.shencom.model.dto.create.EventCameraPointDeviceCreateDTO;
import cn.shencom.model.dto.query.EventCameraPointDeviceQueryDTO;
import cn.shencom.model.dto.query.EventCameraPointDeviceMobileQueryDTO;
import cn.shencom.model.dto.resp.EventCameraPointDeviceRespDTO;
import cn.shencom.model.dto.resp.EventCameraPointDeviceMobileRespDTO;
import cn.shencom.model.dto.update.EventCameraPointDeviceUpdateDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 摄像头表 的服务接口
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
public interface IEventCameraPointDeviceService {

    /**
     * 查询摄像头表列表
     *
     * @param bean 查询DTO
     * @return 分页列表
     */
    Page<EventCameraPointDeviceRespDTO> query(EventCameraPointDeviceQueryDTO bean);

    /**
     * 根据id查询摄像头表
     *
     * @param bean 查询DTO
     * @return 单个实体
     */
    EventCameraPointDeviceRespDTO show(ScShowDTO bean);

    /**
     * 新建摄像头表
     *
     * @param bean 新建DTO
     * @return 创建实体
     */
    EventCameraPointDevice create(EventCameraPointDeviceCreateDTO bean);

    /**
     * 修改摄像头表
     *
     * @param bean 修改DTO
     * @return 修改实体
     */
    EventCameraPointDevice update(EventCameraPointDeviceUpdateDTO bean);

    /**
     * 删除摄像头表
     *
     * @param ids id集合
     */
    void delete(List<String> ids);

    /**
     * 导出摄像头表
     *
     * @param bean 导出DTO
     */
    void export(EventCameraPointDeviceQueryDTO bean);

    /**
     * 查询监控设备表列表 - 移动端专用
     *
     * @param bean 查询DTO
     * @return 分页列表
     */
    Page<EventCameraPointDeviceMobileRespDTO> mobileIndex(EventCameraPointDeviceMobileQueryDTO bean);


    /**
     * 查询未关联的设备
     * @param bean
     * @return
     */
    Page<EventCameraPointDeviceRespDTO> notRelevance(EventCameraPointDeviceQueryDTO bean);


    /**
     * 取消关联设备
     */
    void cancelRelevance(String projectId);

    /**
     * 创建关联
     */
    void createRelevance(MonitorFlow monitorFlow, List<String> deviceIds);


}
