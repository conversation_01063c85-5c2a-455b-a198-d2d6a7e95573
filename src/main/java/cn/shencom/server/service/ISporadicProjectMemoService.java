package cn.shencom.server.service;

import cn.shencom.model.SporadicProjectMemo;
import cn.shencom.model.dto.create.SporadicProjectMemoCreateDTO;
import cn.shencom.model.dto.query.SporadicProjectMemoQueryDTO;
import cn.shencom.model.dto.resp.SporadicProjectMemoRespDTO;
import cn.shencom.model.dto.update.SporadicProjectMemoUpdateDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 小散工程-工程备注表 的服务接口
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
public interface ISporadicProjectMemoService {

    /**
     * 查询小散工程-工程备注表列表
     *
     * @param bean 查询DTO
     * @return 分页列表
     */
    Page<SporadicProjectMemoRespDTO> query(SporadicProjectMemoQueryDTO bean);


    List<SporadicProjectMemoRespDTO> findByProjectId(String projectId);


    /**
     * 根据id查询小散工程-工程备注表
     *
     * @param bean 查询DTO
     * @return 单个实体
     */
    SporadicProjectMemoRespDTO show(ScShowDTO bean);

    /**
     * 新建小散工程-工程备注表
     *
     * @param bean 新建DTO
     * @return 创建实体
     */
    SporadicProjectMemo create(SporadicProjectMemoCreateDTO bean);

    /**
     * 修改小散工程-工程备注表
     *
     * @param bean 修改DTO
     * @return 修改实体
     */
    SporadicProjectMemo update(SporadicProjectMemoUpdateDTO bean);

    /**
     * 删除小散工程-工程备注表
     *
     * @param ids id集合
     */
    void delete(List<String> ids);

    /**
     * 导出小散工程-工程备注表
     *
     * @param bean 导出DTO
     */
    void export(SporadicProjectMemoQueryDTO bean);

}
