package cn.shencom.server.service;

import cn.shencom.model.EventCameraPoint;
import cn.shencom.model.EventCameraPointDevice;
import cn.shencom.model.dto.CameraTreeRegionDTO;
import cn.shencom.model.dto.create.EventCameraPointCreateDTO;
import cn.shencom.model.dto.query.EventCameraPointQueryDTO;
import cn.shencom.model.dto.resp.EventCameraPointRespDTO;
import cn.shencom.model.dto.resp.ExApiDevicePlaybackListRespDTO;
import cn.shencom.model.dto.resp.LiveRespDTO;
import cn.shencom.model.dto.update.EventCameraPointUpdateDTO;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.dto.ScShowDTO;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 摄像头信息表 的服务接口
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
public interface IEventCameraPointService {

    /**
     * 查询摄像头信息表列表
     *
     * @param bean 查询DTO
     * @return 分页列表
     */
    Page<EventCameraPointRespDTO> query(EventCameraPointQueryDTO bean);

    /**
     * 根据id查询摄像头信息表
     *
     * @param bean 查询DTO
     * @return 单个实体
     */
    EventCameraPointRespDTO show(ScShowDTO bean);

    /**
     * 新建摄像头信息表
     *
     * @param bean 新建DTO
     * @return 创建实体
     */
    String create(EventCameraPointDevice device, EventCameraPointCreateDTO bean);

    List<Map<String, String>> getCameraType(EventCameraPointQueryDTO bean);

    /**
     * 修改摄像头信息表
     *
     * @param bean 修改DTO
     * @return 修改实体
     */
    EventCameraPoint update(EventCameraPointDevice entity, EventCameraPointUpdateDTO bean);

    /**
     * 删除摄像头信息表
     *
     * @param ids id集合
     */
    void delete(List<String> ids);

    /**
     * 导出摄像头信息表
     *
     * @param bean 导出DTO
     */
    void export(EventCameraPointQueryDTO bean);

    Result<List<CameraTreeRegionDTO>> treeQueryAll(EventCameraPointQueryDTO bean, HttpServletResponse response);


    List<LiveRespDTO> getLiveUrlByAreaId(EventCameraPointQueryDTO bean);

    Result cameraOpen(EventCameraPointQueryDTO bean);

    void syncCameraOnline();

    LiveRespDTO getLive(EventCameraPointQueryDTO bean);

    LiveRespDTO getHistory(EventCameraPointQueryDTO bean);

    List<ExApiDevicePlaybackListRespDTO> getHistoryList(EventCameraPointQueryDTO bean);

    Result treeQuery(EventCameraPointQueryDTO bean);




    void updateAiotDeviceStatus();
}
