package cn.shencom.server.service;

import cn.shencom.model.MonitorFlow;
import cn.shencom.model.MonitorOrder;
import cn.shencom.model.dto.create.MonitorFlowCreateDTO;
import cn.shencom.model.dto.query.MonitorFlowQueryDTO;
import cn.shencom.model.dto.resp.MonitorFlowRespDTO;
import cn.shencom.model.dto.update.MonitorFlowUpdateDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 小散工程-监管工单流程 的服务接口
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
public interface IMonitorFlowService {

    /**
     * 查询小散工程-监管工单流程列表
     *
     * @param bean 查询DTO
     * @return 分页列表
     */
    Page<MonitorFlowRespDTO> query(MonitorFlowQueryDTO bean);

    /**
     * 根据id查询小散工程-监管工单流程
     *
     * @param bean 查询DTO
     * @return 单个实体
     */
    MonitorFlowRespDTO show(ScShowDTO bean);

    /**
     * 新建小散工程-监管工单流程
     *
     * @param bean 新建DTO
     * @return 创建实体
     */
    MonitorFlow create(MonitorFlowCreateDTO bean);

    /**
     * 修改小散工程-监管工单流程
     *
     * @param bean 修改DTO
     * @return 修改实体
     */
    MonitorFlow update(MonitorFlowUpdateDTO bean);

    /**
     * 删除小散工程-监管工单流程
     *
     * @param ids id集合
     */
    void delete(List<String> ids);

    /**
     * 导出小散工程-监管工单流程
     *
     * @param bean 导出DTO
     */
    void export(MonitorFlowQueryDTO bean);


    /**
     * 初始化监管流程工单
     * @param monitorOrder
     */
    void initFlow(MonitorOrder monitorOrder);


    MonitorFlow nextFlow(MonitorFlow currentFlow,MonitorFlowUpdateDTO bean);



    MonitorFlow findByOrderIdAndFlow(String orderId,Integer flow);


    List<String> findOwnerOrderIds(String userId, Integer flow);

}
