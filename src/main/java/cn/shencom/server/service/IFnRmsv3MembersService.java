package cn.shencom.server.service;

import cn.shencom.model.FnRmsv3Members;
import cn.shencom.model.dto.create.FnRmsv3MembersCreateDTO;
import cn.shencom.model.dto.query.FnRmsv3MembersQueryDTO;
import cn.shencom.model.dto.resp.FnRmsv3MembersRespDTO;
import cn.shencom.model.dto.update.FnRmsv3MembersUpdateDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 小散工程-组织团队成员表 的服务接口
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
public interface IFnRmsv3MembersService {

    /**
     * 查询小散工程-组织团队成员表列表
     *
     * @param bean 查询DTO
     * @return 分页列表
     */
    Page<FnRmsv3MembersRespDTO> query(FnRmsv3MembersQueryDTO bean);

    /**
     * 根据id查询小散工程-组织团队成员表
     *
     * @param bean 查询DTO
     * @return 单个实体
     */
    FnRmsv3MembersRespDTO show(ScShowDTO bean);

    /**
     * 新建小散工程-组织团队成员表
     *
     * @param bean 新建DTO
     * @return 创建实体
     */
    FnRmsv3Members create(FnRmsv3MembersCreateDTO bean);

    /**
     * 修改小散工程-组织团队成员表
     *
     * @param bean 修改DTO
     * @return 修改实体
     */
    FnRmsv3Members update(FnRmsv3MembersUpdateDTO bean);

    /**
     * 删除小散工程-组织团队成员表
     *
     * @param ids id集合
     */
    void delete(List<String> ids);

    /**
     * 导出小散工程-组织团队成员表
     *
     * @param bean 导出DTO
     */
    void export(FnRmsv3MembersQueryDTO bean);

}
