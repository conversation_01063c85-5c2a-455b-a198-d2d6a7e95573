package cn.shencom.server.service;

import cn.shencom.model.EngineeringMembersProjectRelate;
import cn.shencom.model.dto.create.EngineeringMembersProjectRelateCreateDTO;
import cn.shencom.model.dto.query.EngineeringMembersProjectRelateQueryDTO;
import cn.shencom.model.dto.resp.EngineeringMembersProjectRelateRespDTO;
import cn.shencom.model.dto.update.EngineeringMembersProjectRelateUpdateDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 小散工程-工程人员关联项目表 的服务接口
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
public interface IEngineeringMembersProjectRelateService {

    /**
     * 查询小散工程-工程人员关联项目表列表
     *
     * @param bean 查询DTO
     * @return 分页列表
     */
    Page<EngineeringMembersProjectRelateRespDTO> query(EngineeringMembersProjectRelateQueryDTO bean);

    /**
     * 根据id查询小散工程-工程人员关联项目表
     *
     * @param bean 查询DTO
     * @return 单个实体
     */
    EngineeringMembersProjectRelateRespDTO show(ScShowDTO bean);

    /**
     * 新建小散工程-工程人员关联项目表
     *
     * @param bean 新建DTO
     * @return 创建实体
     */
    EngineeringMembersProjectRelate create(EngineeringMembersProjectRelateCreateDTO bean);

    /**
     * 修改小散工程-工程人员关联项目表
     *
     * @param bean 修改DTO
     * @return 修改实体
     */
    EngineeringMembersProjectRelate update(EngineeringMembersProjectRelateUpdateDTO bean);

    /**
     * 删除小散工程-工程人员关联项目表
     *
     * @param ids id集合
     */
    void delete(List<String> ids);

    /**
     * 导出小散工程-工程人员关联项目表
     *
     * @param bean 导出DTO
     */
    void export(EngineeringMembersProjectRelateQueryDTO bean);

    /**
     * 通过工程ID和Type查询成员
     */
    List<EngineeringMembersProjectRelateRespDTO> queryByProjectIdInAndType(List<String> projectIds, Integer type);
}
