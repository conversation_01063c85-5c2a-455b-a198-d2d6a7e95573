package cn.shencom.server.service;

import cn.shencom.model.AiotCategory;
import cn.shencom.model.dto.AiotEventPushDTO;
import cn.shencom.model.dto.create.AiotCategoryCreateDTO;
import cn.shencom.model.dto.query.AiotCategoryQueryDTO;
import cn.shencom.model.dto.resp.AiotCategoryRespDTO;
import cn.shencom.model.dto.update.AiotCategoryUpdateDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * aiot场景分类 的服务接口
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
public interface IAiotCategoryService {

    /**
     * 查询aiot场景分类列表
     *
     * @param bean 查询DTO
     * @return 分页列表
     */
    Page<AiotCategoryRespDTO> query(AiotCategoryQueryDTO bean);

    /**
     * 根据id查询aiot场景分类
     *
     * @param bean 查询DTO
     * @return 单个实体
     */
    AiotCategoryRespDTO show(ScShowDTO bean);

    /**
     * 新建aiot场景分类
     *
     * @param bean 新建DTO
     * @return 创建实体
     */
    AiotCategory create(AiotCategoryCreateDTO bean);

    /**
     * 修改aiot场景分类
     *
     * @param bean 修改DTO
     * @return 修改实体
     */
    AiotCategory update(AiotCategoryUpdateDTO bean);

    /**
     * 删除aiot场景分类
     *
     * @param ids id集合
     */
    void delete(List<String> ids);

    /**
     * 导出aiot场景分类
     *
     * @param bean 导出DTO
     */
    void export(AiotCategoryQueryDTO bean);

    @Transactional(rollbackFor = Exception.class)
    void batchUpdateOrCreateAiCategories(List<AiotEventPushDTO.SceneCategoriesDTO> sceneCategories, String sceneCode);

    @Transactional(rollbackFor = Exception.class)
    void batchUpdateOrCreateAiScenes(List<AiotEventPushDTO.AiScenesDTO> aiScenes, String sceneCode);
}
