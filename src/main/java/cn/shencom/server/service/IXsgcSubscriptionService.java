package cn.shencom.server.service;

import cn.shencom.model.XsgcSubscription;
import cn.shencom.model.dto.create.XsgcSubscriptionCreateDTO;
import cn.shencom.model.dto.query.XsgcSubscriptionQueryDTO;
import cn.shencom.model.dto.resp.XsgcSubscriptionRespDTO;
import cn.shencom.model.dto.update.XsgcSubscriptionUpdateDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 小散工程-套餐 的服务接口
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
public interface IXsgcSubscriptionService {

    /**
     * 查询小散工程-套餐列表
     *
     * @param bean 查询DTO
     * @return 分页列表
     */
    Page<XsgcSubscriptionRespDTO> query(XsgcSubscriptionQueryDTO bean);

    /**
     * 根据id查询小散工程-套餐
     *
     * @param bean 查询DTO
     * @return 单个实体
     */
    XsgcSubscriptionRespDTO show(ScShowDTO bean);

    /**
     * 新建小散工程-套餐
     *
     * @param bean 新建DTO
     * @return 创建实体
     */
    XsgcSubscription create(XsgcSubscriptionCreateDTO bean);

    /**
     * 修改小散工程-套餐
     *
     * @param bean 修改DTO
     * @return 修改实体
     */
    XsgcSubscription update(XsgcSubscriptionUpdateDTO bean);

    /**
     * 删除小散工程-套餐
     *
     * @param ids id集合
     */
    void delete(List<String> ids);

    /**
     * 导出小散工程-套餐
     *
     * @param bean 导出DTO
     */
    void export(XsgcSubscriptionQueryDTO bean);

}
