package cn.shencom.server.service;

import cn.shencom.model.ConstructionUnit;
import cn.shencom.model.dto.create.ConstructionUnitCreateDTO;
import cn.shencom.model.dto.query.ConstructionUnitQueryDTO;
import cn.shencom.model.dto.resp.ConstructionUnitRespDTO;
import cn.shencom.model.dto.update.ConstructionUnitUpdateDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 施工单位表 的服务接口
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
public interface IConstructionUnitService {

    /**
     * 查询施工单位表列表
     *
     * @param bean 查询DTO
     * @return 分页列表
     */
    Page<ConstructionUnitRespDTO> query(ConstructionUnitQueryDTO bean);

    /**
     * 根据id查询施工单位表
     *
     * @param bean 查询DTO
     * @return 单个实体
     */
    ConstructionUnitRespDTO show(ScShowDTO bean);

    /**
     * 新建施工单位表
     *
     * @param bean 新建DTO
     * @return 创建实体
     */
    ConstructionUnit create(ConstructionUnitCreateDTO bean);

    /**
     * 修改施工单位表
     *
     * @param bean 修改DTO
     * @return 修改实体
     */
    ConstructionUnit update(ConstructionUnitUpdateDTO bean);

    /**
     * 删除施工单位表
     *
     * @param ids id集合
     */
    void delete(List<String> ids);

    /**
     * 导出施工单位表
     *
     * @param bean 导出DTO
     */
    void export(ConstructionUnitQueryDTO bean);

}
