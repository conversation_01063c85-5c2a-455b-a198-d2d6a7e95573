package cn.shencom.server.service.impl;

import cn.shencom.model.ContractingUnit;
import cn.shencom.model.ContractingUnitOrganizationRelate;
import cn.shencom.model.SporadicProject;
import cn.shencom.model.dto.create.ContractingUnitCreateDTO;
import cn.shencom.model.dto.query.ContractingUnitQueryDTO;
import cn.shencom.model.dto.resp.ContractingUnitRespDTO;
import cn.shencom.model.dto.update.ContractingUnitUpdateDTO;
import cn.shencom.repos.ContractingUnitRepository;
import cn.shencom.repos.SporadicProjectRepository;
import cn.shencom.scloud.common.base.constant.RespCode;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.jpa.scpage.MyLink;
import cn.shencom.scloud.common.jpa.util.export.ScExport;
import cn.shencom.scloud.common.jpa.util.query.LinkUtil;
import cn.shencom.scloud.common.jpa.util.query.ScQueryUtil;
import cn.shencom.scloud.common.server.service.BaseImpl;
import cn.shencom.scloud.common.util.BeanUtil;
import cn.shencom.scloud.security.core.ScContext;
import cn.shencom.server.service.IContractingUnitService;
import cn.shencom.utils.XsgcContext;
import jodd.util.StringUtil;

import com.alibaba.nacos.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.NumberUtils;

import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 施工单位表 的服务实现
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Service
@Slf4j
public class ContractingUnitServiceImpl extends BaseImpl implements IContractingUnitService {

    @Autowired
    private ContractingUnitRepository contractingUnitRepository;

    @Autowired
    private SporadicProjectRepository sporadicProjectRepository;

    @Override
    public Page<ContractingUnitRespDTO> query(ContractingUnitQueryDTO bean) {

        String organizationId = XsgcContext.getOrganizationId();

        String isBlacklist = ScQueryUtil.getValueAndRm(bean.getQuery(), "isBlacklist");

        Map<String, MyLink> linkMap = LinkUtil.convertLink(ContractingUnit.class);
        Page<ContractingUnit> res = contractingUnitRepository.findAll((root, query, builder) -> {
            // 用于拼接条件
            List<Predicate> ps = new ArrayList<>();

            // 需要 通过 organizationId 查询 constructionUnitOrganization 表
            Join<Object, Object> join = root.join("contractingUnitOrganizationRelates", JoinType.LEFT);
            ps.add(builder.equal(join.get("organizationId"), organizationId));
            if (StringUtils.isNotBlank(isBlacklist)) {
                ps.add(builder.equal(join.get("isBlacklist"), NumberUtils.parseNumber(isBlacklist, Integer.class)));
            }

            return ScQueryUtil.getPredicate(ps, bean, linkMap, root, query, builder);
        }, PageRequest.of(bean.getPage(), bean.getSize()));

        List<String> ids = res.getContent().stream().map(ContractingUnit::getId).collect(Collectors.toList());
        // 获取工程
        List<SporadicProject> projects = sporadicProjectRepository.findByContractorIdIn(ids);

        Map<String, Integer> contractorLeaderMap = projects.stream()
                .filter(p -> p.getContractorChargerMobile() != null)
                .collect(Collectors.groupingBy(
                        SporadicProject::getContractorId,
                        Collectors.collectingAndThen(
                                Collectors.mapping(
                                        SporadicProject::getContractorChargerMobile,
                                        Collectors.toSet()),
                                Set::size)));

        return ScQueryUtil.handle(res, linkMap, ContractingUnitRespDTO.class, (r, dto) -> {
            ContractingUnitOrganizationRelate relate = r.getContractingUnitOrganizationRelates().get(0);
            if (relate == null) {
                return;
            }

            // 负责人数量
            dto.setLeaderCount(contractorLeaderMap.getOrDefault(relate.getContractingUnitId(), 0));

            // 工人数量
            dto.setWorkerCount(0);

            BeanUtil.copyProperties(relate, dto);
        });
    }

    @Override
    public ContractingUnitRespDTO show(ScShowDTO bean) {
        Optional<ContractingUnit> option = contractingUnitRepository.findById(bean.getId());
        if (!option.isPresent()) {
            return null;
        }
        ContractingUnit entity = option.get();
        Map<String, MyLink> linkMap = LinkUtil.convertLink(ContractingUnit.class);
        return ScQueryUtil.handleOne(entity, linkMap, ContractingUnitRespDTO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ContractingUnit create(ContractingUnitCreateDTO bean) {
        ContractingUnit entity = new ContractingUnit();
        BeanUtil.copyProperties(bean, entity);
        return contractingUnitRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ContractingUnit createOrUpdate(ContractingUnitUpdateDTO bean) {

        String organizationId = XsgcContext.getOrganizationId();
        if (StringUtil.isBlank(organizationId)) {
            throw new ScException("组织id不能为空！");
        }

        String userId = ScContext.getCurrentUserThrow().getId();

        ContractingUnit entity = contractingUnitRepository.findByName(bean.getName())
                .orElse(null);

        if (entity == null) {
            bean.setCreatedUser(userId);
            entity = new ContractingUnit();
        }

        bean.setUpdatedUser(userId);
        BeanUtil.copyProperties(bean, entity);
        return contractingUnitRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ContractingUnit update(ContractingUnitUpdateDTO bean) {
        ContractingUnit entity = contractingUnitRepository.findById(bean.getId())
                .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));
        BeanUtil.copyProperties(bean, entity);
        return contractingUnitRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            ids.forEach(id -> contractingUnitRepository.findById(id).ifPresent(entity -> {
                entity.setIsDeleted(1);
                entity.setDeletedAt(new Date());
                contractingUnitRepository.save(entity);
            }));
        }
    }

    @Override
    public void export(ContractingUnitQueryDTO bean) {
        List<ContractingUnitRespDTO> dtos = new ArrayList<>(query(bean).getContent());
        ScExport.export(bean.getCfg(), dtos, ContractingUnitRespDTO.class);
    }
}