package cn.shencom.server.service.impl;

import cn.shencom.enums.BusinessMemberTypeEnum;
import cn.shencom.model.XsgcCustomerInfo;
import cn.shencom.model.XsgcCustomerServiceRecord;
import cn.shencom.model.XsgcOrganization;
import cn.shencom.model.dto.create.XsgcCustomerInfoCreateDTO;
import cn.shencom.model.dto.query.XsgcBusinessMembersRelateQueryDTO;
import cn.shencom.model.dto.query.XsgcCustomerInfoQueryDTO;
import cn.shencom.model.dto.resp.XsgcBusinessMembersRelateRespDTO;
import cn.shencom.model.dto.resp.XsgcCustomerInfoRespDTO;
import cn.shencom.model.dto.update.XsgcCustomerInfoUpdateDTO;
import cn.shencom.repos.*;
import cn.shencom.scloud.common.base.constant.RespCode;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.jpa.scpage.MyLink;
import cn.shencom.scloud.common.jpa.util.export.ScExport;
import cn.shencom.scloud.common.jpa.util.query.LinkUtil;
import cn.shencom.scloud.common.jpa.util.query.ScQueryUtil;
import cn.shencom.scloud.common.server.service.BaseImpl;
import cn.shencom.scloud.common.util.BeanUtil;
import cn.shencom.scloud.common.util.DateUtil;
import cn.shencom.scloud.security.core.ScContext;
import cn.shencom.server.service.ISysRoleUserOrganizationService;
import cn.shencom.server.service.IXsgcCustomerInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.time.LocalDate;
import java.util.*;


/**
 * 小散工程-客户信息表 的服务实现
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Service
@Slf4j
public class XsgcCustomerInfoServiceImpl extends BaseImpl implements IXsgcCustomerInfoService {

    @Autowired
    private XsgcCustomerInfoRepository xsgcCustomerInfoRepository;

    @Autowired
    private XsgcOrganizationRepository organizationRepository;


    @Autowired
    private XsgcBusinessMembersRelateRepository xsgcBusinessMembersRelateRepository;

    @Autowired
    private FnRmsv3MembersRepository fnRmsv3MembersRepository;

    @Autowired
    private XsgcCustomerServiceRecordRepository serviceRecordRepository;

    @Autowired
    private ISysRoleUserOrganizationService sysRoleUserOrganizationService;

    @Override
    public Page<XsgcCustomerInfoRespDTO> query(XsgcCustomerInfoQueryDTO bean) {
        Map<String, MyLink> linkMap = LinkUtil.convertLink(XsgcCustomerInfo.class);

        String renewalMark = ScQueryUtil.getValueAndRm(bean.getQuery(), "renewalMark");

        Page<XsgcCustomerInfo> res = xsgcCustomerInfoRepository.findAll((root, query, builder) -> {
            //用于拼接条件
            List<Predicate> ps = new ArrayList<>();

            if ("2".equals(renewalMark)){
                List<String> ids = serviceRecordRepository.selectTempRenewal();
                if (!ids.isEmpty()){
                    ps.add(builder.in(root.get("id")).value(ids));
                }else {
                    ps.add(builder.isNull(root.get("id")));
                }
            }

            return ScQueryUtil.getPredicate(ps, bean, linkMap, root, query, builder);
        }, PageRequest.of(bean.getPage(), bean.getSize()));

        Page<XsgcCustomerInfoRespDTO> page = ScQueryUtil.handle(res, linkMap, XsgcCustomerInfoRespDTO.class);
        for (XsgcCustomerInfoRespDTO respDTO : page.getContent()) {
            fillRelevanceUserNum(respDTO);
        }
        return page;
    }

    @Override
    public XsgcCustomerInfoRespDTO show(ScShowDTO bean) {
        Optional<XsgcCustomerInfo> option = xsgcCustomerInfoRepository.findById(bean.getId());
        if (!option.isPresent()) {
            return null;
        }
        XsgcCustomerInfo entity = option.get();
        Map<String, MyLink> linkMap = LinkUtil.convertLink(XsgcCustomerInfo.class);


        XsgcCustomerInfoRespDTO respDTO = ScQueryUtil.handleOne(entity, linkMap, XsgcCustomerInfoRespDTO.class);


        //填充客户关联的业务人员
        XsgcBusinessMembersRelateQueryDTO queryDTO = new XsgcBusinessMembersRelateQueryDTO();
        queryDTO.setOrganizationId(respDTO.getOrganizationId());
        for (BusinessMemberTypeEnum typeEnum : BusinessMemberTypeEnum.getAllTypeEnum()) {
            queryDTO.setType(typeEnum.getCode().toString());
            Page<XsgcBusinessMembersRelateRespDTO> relatePage = xsgcBusinessMembersRelateRepository.relevanceIndex(queryDTO, PageRequest.of(0, 99));
            switch (typeEnum.getCode()){
                case 1:
                    respDTO.setTechnician(relatePage.getContent());
                    break;
                case 2:
                    respDTO.setBusinessPeople(relatePage.getContent());
                    break;
                case 3:
                    respDTO.setSalesperson(relatePage.getContent());
                    break;
                case 4:
                    respDTO.setInstaller(relatePage.getContent());
                    break;
                default:;
            }
        }


        return respDTO;
    }


    @Override
    public XsgcCustomerInfoRespDTO showByOrganizationId(ScShowDTO bean) {
        String organizationId = bean.getId();

        //查询组织
        XsgcCustomerInfo xsgcCustomerInfo = xsgcCustomerInfoRepository.findFirstByOrganizationId(organizationId);

        if (xsgcCustomerInfo==null){
            throw new ScException("当前组织不存在！");
        }
        bean.setId(xsgcCustomerInfo.getId());
        return show(bean);
    }

    private void  fillRelevanceUserNum(XsgcCustomerInfoRespDTO respDTO){
        //获取关联业务人员和组织人员数
        respDTO.setAdministratorNum(fnRmsv3MembersRepository.countByOrganizationIdAndTypeIn(respDTO.getOrganizationId(),Arrays.asList("1")));
        respDTO.setOrdinaryUserNum(fnRmsv3MembersRepository.countByOrganizationIdAndTypeIn(respDTO.getOrganizationId(),Arrays.asList("2","3","4","5","6")));
    }
    

    @Transactional(rollbackFor = Exception.class)
    @Override
    public XsgcCustomerInfo create(XsgcCustomerInfoCreateDTO bean) {

        String userId = ScContext.getCurrentUserThrow().getId();

        //开始时间和结束时间,和套餐，需要通过服务开通才能更改
        bean.setStartDate(null);
        bean.setEndDate(null);
        bean.setOptionId(null);

        XsgcCustomerInfo entity = new XsgcCustomerInfo();
        BeanUtil.copyProperties(bean, entity);

        //创建的时候默认为无效
        entity.setStatus(0);
        entity.setCreatedUser(userId);


        //客户编码不能重复，需要做校验
        if (xsgcCustomerInfoRepository.existsByNumber(bean.getNumber())){
            throw new ScException("当前客户编码已存在，添加失败！");
        }


        //创建客户的同时需要创建组织
        XsgcOrganization xsgcOrganization = new XsgcOrganization();
        xsgcOrganization.setCreatedUser(userId);
        xsgcOrganization.setName(entity.getName());
        XsgcOrganization organization = organizationRepository.save(xsgcOrganization);

        //设置组织id
        entity.setOrganizationId(organization.getId());

        return xsgcCustomerInfoRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public XsgcCustomerInfo update(XsgcCustomerInfoUpdateDTO bean) {

        String userId = ScContext.getCurrentUserThrow().getId();

        //开始时间和结束时间，以及激活状态 需要通过服务开通才能更改
        bean.setStartDate(null);
        bean.setEndDate(null);


        XsgcCustomerInfo entity = xsgcCustomerInfoRepository.findById(bean.getId())
                .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));


        //客户编码不能重复
        if (!entity.getNumber().equals(bean.getNumber())){
            //客户编码不能重复，需要做校验
            if (xsgcCustomerInfoRepository.existsByNumber(bean.getNumber())){
                throw new ScException("当前客户编码已存在，修改失败！");
            }
        }


        //更新有效状态
        if (bean.getStatus()!=null&&!bean.getStatus().equals(entity.getStatus())){
            updateStatus(bean);
        }
        //再次查询,获取最新的信息
        entity = xsgcCustomerInfoRepository.findById(bean.getId())
                .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));

        BeanUtil.copyProperties(bean, entity);

        entity.setUpdatedUser(userId);


        //组织改名
        XsgcOrganization xsgcOrganization = organizationRepository.findById(entity.getOrganizationId()).get();
        xsgcOrganization.setName(entity.getName());
        organizationRepository.save(xsgcOrganization);

        return xsgcCustomerInfoRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            ids.forEach(id -> xsgcCustomerInfoRepository.findById(id).ifPresent(entity -> {

                checkIfHasRelateBusiness(entity);

                //删除客户关联的业务人员
                xsgcBusinessMembersRelateRepository.deleteByOrganizationId(entity.getOrganizationId());

                //删除客户时删除服务开通记录
                serviceRecordRepository.deleteByCustomerId(id);

                //删除组织
                XsgcOrganization xsgcOrganization = organizationRepository.findById(entity.getOrganizationId()).get();
                xsgcOrganization.setIsDeleted(1);
                xsgcOrganization.setDeletedAt(new Date());
                organizationRepository.save(xsgcOrganization);

                entity.setIsDeleted(1);
                entity.setDeletedAt(new Date());
                xsgcCustomerInfoRepository.save(entity);
            }));
        }
    }

    @Override
    public void export(XsgcCustomerInfoQueryDTO bean) {
        List<XsgcCustomerInfoRespDTO> dtos = new ArrayList<>(query(bean).getContent());
        ScExport.export(bean.getCfg(), dtos, XsgcCustomerInfoRespDTO.class);
    }


    /**
     * 更新有效状态为无效时时需要校验是否有关联业务
     * @return  如果有关联业务，返回true
     */
    private boolean checkIfHasRelateBusiness(XsgcCustomerInfo customerInfo){


        //查询是否有关联的组织人员
        boolean flag = fnRmsv3MembersRepository.existsByOrganizationId(customerInfo.getOrganizationId());
        if (flag){
            throw new ScException("当前客户存在关联的成员，请先删除关联的成员！");
        }

        return false;
    }


    @Override
    @Transactional
    public void updateStatus(XsgcCustomerInfoUpdateDTO bean) {

        if (bean.getStatus()==null){
            throw new ScException("服务状态不能为空！");
        }

        if (bean.getStatus()==1){
            activeService(bean);
        }else if (bean.getStatus()==0){
            closeService(bean);
        }
    }

    @Override
    @Transactional
    public void closeService(XsgcCustomerInfoUpdateDTO bean) {

        String customerId= bean.getId();
        //查询用户
        XsgcCustomerInfo customer = xsgcCustomerInfoRepository.findById(customerId).orElseThrow(() -> new ScException("当前客户不存在！"));

        String organizationId = customer.getOrganizationId();

        sysRoleUserOrganizationService.removePermissionForOrganization(organizationId);

        //修改服务关闭状态
        customer.setStatus(0);
        xsgcCustomerInfoRepository.save(customer);
    }

    @Override
    @Transactional
    public void activeService(XsgcCustomerInfoUpdateDTO bean) {

        String userId = ScContext.getCurrentUserThrow().getId();

        String customerId= bean.getId();
        //查询用户
        XsgcCustomerInfo customer = xsgcCustomerInfoRepository.findById(customerId).orElseThrow(() -> new ScException("当前客户不存在！"));


        Date today = DateUtil.getStartOfToday(LocalDate.now());
        //判断当前服务期是否已满
        if (customer.getEndDate()!=null&&checkIfInServicePeriod(today,customer.getStartDate(),customer.getEndDate())){
            //情况1： 非首次激活，且当前服务未过期, 直接修改状态

        }else {
            //情况2： 首次激活, 或者服务已过期
            //查询当前未激活的服务记录
            XsgcCustomerServiceRecord serviceRecord = serviceRecordRepository.findFirstByCustomerIdAndActiveOrderByStartDateAsc(customerId, 0);
            //如果不存在可以激活的记录
            if (serviceRecord==null){
                throw new ScException("当前不存在可以激活的服务！");
            }
            //判断当前是否处于服务期内
            if (!checkIfInServicePeriod(today,serviceRecord.getStartDate(),serviceRecord.getEndDate())){
                throw new ScException(String.format("当前不在服务期内，无法激活！服务期：%s ~ %s", DateUtil.defaultDate(serviceRecord.getStartDate()), DateUtil.defaultDate(serviceRecord.getEndDate())));
            }
            //激活服务
            serviceRecord.setActive(1);
            serviceRecordRepository.save(serviceRecord);

            //修改服务时间
            customer.setStartDate(serviceRecord.getStartDate());
            customer.setEndDate(serviceRecord.getEndDate());
            customer.setRenewalMark(serviceRecord.getRenewalMark());
            customer.setOptionId(serviceRecord.getOptionId());

        }

        //查询当前用户关联的全部成员，并且赋予权限
        sysRoleUserOrganizationService.addPermissionForOrganization(customer.getOrganizationId());

        customer.setUpdatedUser(userId);
        xsgcCustomerInfoRepository.save(customer);
    }


    private boolean checkIfInServicePeriod(Date today,Date startDay,Date endDate){
        if (today.getTime() >= startDay.getTime() && today.getTime() <=endDate.getTime()){
            return true;
        }
        return false;
    }


    @Override
    @Transactional
    public void openService(XsgcCustomerInfoUpdateDTO bean) {
        String userId = ScContext.getCurrentUserThrow().getId();

        String customerId = bean.getId();

        //查询客户信息
        XsgcCustomerInfo customer = xsgcCustomerInfoRepository.findById(bean.getId()).orElseThrow(() -> new ScException("当前客户不存在！"));

        //创建服务开通记录
        XsgcCustomerServiceRecord serviceRecord = new XsgcCustomerServiceRecord();
        serviceRecord.setCreatedUser(userId);
        serviceRecord.setOptionId(bean.getOptionId());
        serviceRecord.setRenewalMark(bean.getRenewalMark());
        serviceRecord.setStartDate(bean.getStartDate());
        serviceRecord.setEndDate(bean.getEndDate());
        serviceRecord.setMemo(bean.getMemo());
        serviceRecord.setCustomerId(customerId);

        //判断开通记录的时间是否正确
        //查询上一次开通记录
        XsgcCustomerServiceRecord lastRecord = serviceRecordRepository.findFirstByCustomerIdOrderByIdDesc(customerId);
        if (lastRecord!=null){
            //服务期不能重叠，且新开的服务的起始日期必须在旧记录结束日期之后
            Date lastRecordEnd= lastRecord.getEndDate();
            Date newRecordStart = bean.getStartDate();
            if (newRecordStart.getTime()<=lastRecordEnd.getTime()){
                throw new ScException("服务期限不能重叠，上一次服务结束时间为:"+ DateUtil.defaultDate(lastRecordEnd));
            }
        }


        //todo 判断套餐类型是否更改，如果更改当前客户关联的用户的角色类型   普通用户->高级用户

        //查询上一次开通的时间
        XsgcCustomerServiceRecord lastActiveRecord = serviceRecordRepository.findFirstByCustomerIdAndActiveOrderByStartDateDesc(customerId, 1);
        if (lastActiveRecord!=null){
            //如果不是首次开通,需要判断服务期是否连续
            int day = DateUtil.daysBetween( lastActiveRecord.getEndDate(),bean.getStartDate());
            if (day<=1){
                //日期连续，修改客户服务结束期限,自动激活当前
                customer.setEndDate(bean.getEndDate());
                customer.setOptionId(bean.getOptionId());
                serviceRecord.setActive(1);
            }
        }else {

            //查询是否有过开通记录
            if (!serviceRecordRepository.existsByCustomerId(customerId)){
                //如果是首次开通，续签标记为 “首次开通”
                serviceRecord.setRenewalMark(0);
            }
        }

        serviceRecordRepository.save(serviceRecord);
        xsgcCustomerInfoRepository.save(customer);

    }


    @Override
    @Transactional
    public void autoCloseService() {
        //前一天，定时任务在凌晨一点执行
        Date yesterday = DateUtil.yesterday();
        List<XsgcCustomerInfo> needCloseCustomer = xsgcCustomerInfoRepository.findByStatusAndEndDateLessThan(1,yesterday);
        for (XsgcCustomerInfo customerInfo : needCloseCustomer) {
            XsgcCustomerInfoUpdateDTO updateDTO = new XsgcCustomerInfoUpdateDTO();
            updateDTO.setId(customerInfo.getId());
            closeService(updateDTO);
        }
    }
}