package cn.shencom.server.service.impl;

import cn.shencom.model.SysRoleUser;
import cn.shencom.model.SysRoles;
import cn.shencom.repos.SysRoleUserRepository;
import cn.shencom.repos.SysRolesRepository;
import cn.shencom.server.service.ISysRolesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class SysRolesServiceImpl implements ISysRolesService {


    @Autowired
    private SysRolesRepository sysRolesRepository;

    @Autowired
    private SysRoleUserRepository sysRoleUserRepository;

    /**
     * 为用户添加角色
     * @param roleName  角色标识
     * @param userId    用户id
     */
    @Override
    @Transactional
    public void addRoleForUser(String roleName, String userId) {

        //根据角色标识查询角色
        SysRoles firstByName = sysRolesRepository.findFirstByName(roleName);
        if (firstByName!=null){
            sysRoleUserRepository.deleteByUserIdAndRoleId(userId,firstByName.getId());
            SysRoleUser sysRoleUser = new SysRoleUser();
            sysRoleUser.setUserId(userId);
            sysRoleUser.setRoleId(firstByName.getId());
            sysRoleUserRepository.save(sysRoleUser);
        }
    }

    /**
     * 为用户删除角色
     * @param roleName  角色标识
     * @param userId    用户id
     */
    @Override
    public void deleteRoleForUser(String roleName, String userId) {
        //根据角色标识查询角色
        SysRoles firstByName = sysRolesRepository.findFirstByName(roleName);
        if (firstByName!=null){
            sysRoleUserRepository.deleteByUserIdAndRoleId(userId,firstByName.getId());
        }

    }
}
