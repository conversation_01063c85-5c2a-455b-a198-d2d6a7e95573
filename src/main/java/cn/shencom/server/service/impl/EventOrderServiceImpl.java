package cn.shencom.server.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.shencom.config.EsIndexConfig;
import cn.shencom.model.ComRegion;
import cn.shencom.model.EventOrder;
import cn.shencom.model.EventOrderSource;
import cn.shencom.model.EventOrderType;
import cn.shencom.model.dto.create.EventOrderCreateDTO;
import cn.shencom.model.dto.query.EventOrderQueryDTO;
import cn.shencom.model.dto.query.EventOrderTypeQueryDTO;
import cn.shencom.model.dto.resp.ESEventOrderRespDTO;
import cn.shencom.model.dto.resp.EventOrderRespDTO;
import cn.shencom.model.dto.resp.EventOrderTypeRespDTO;
import cn.shencom.model.dto.update.EventOrderUpdateDTO;
import cn.shencom.repos.ComRegionRepository;
import cn.shencom.repos.EventOrderRepository;
import cn.shencom.repos.EventOrderSourceRepository;
import cn.shencom.repos.EventOrderTypeRepository;
import cn.shencom.scloud.common.base.constant.RespCode;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.jpa.scpage.MyLink;
import cn.shencom.scloud.common.jpa.scpage.ScSort;
import cn.shencom.scloud.common.jpa.util.export.ScExport;
import cn.shencom.scloud.common.jpa.util.query.LinkUtil;
import cn.shencom.scloud.common.jpa.util.query.ScQueryUtil;
import cn.shencom.scloud.common.server.service.BaseImpl;
import cn.shencom.scloud.common.util.BeanUtil;
import cn.shencom.scloud.common.util.PageUtil;
import cn.shencom.scloud.common.util.thread.ExportThreadPool;
import cn.shencom.scloud.common.utils.EsBuilder;
import cn.shencom.scloud.common.utils.EsOperation;
import cn.shencom.scloud.security.core.ScContext;
import cn.shencom.scloud.security.core.SecurityUser;
import cn.shencom.server.service.IEventCameraPointService;
import cn.shencom.server.service.IEventOrderService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 事件工单表 的服务实现
 *
 * <AUTHOR>
 * @since 2021-06-29
 */
@Service
@Slf4j
public class EventOrderServiceImpl extends BaseImpl implements IEventOrderService {

    @Resource
    private EventOrderRepository eventOrderRepository;

    @Resource
    private IEventCameraPointService iEventCameraPointService;

    @Resource
    private ComRegionRepository comRegionRepository;

    @Resource
    private EsOperation esOperation;

    @Resource
    private EsIndexConfig esIndexConfig;

    @Resource
    private EventOrderSourceRepository eventOrderSourceRepository;

    @Autowired
    private EventOrderTypeRepository eventOrderTypeRepository;


    @Override
    public Page<EventOrderRespDTO> query(EventOrderQueryDTO bean) {
        //人工智能管理下的事件列表
        String sceneId = bean.getSceneId();
        if (StringUtils.isBlank(sceneId)) {
            sceneId = ScQueryUtil.getValueAndRm(bean.getQuery(), "sceneId");
        }

        //违规类型编号
        List<String> typeCodeList = Lists.newArrayList();
//        if (StringUtils.isNotBlank(sceneId)) {
//            //该场景关联的厂商场景
//            List<AimSceneFirmRelationship> sceneFirmRelationships = aimSceneFirmRelationshipRepository.findBySceneId(sceneId);
//            for (AimSceneFirmRelationship firmRelationship : sceneFirmRelationships) {
//                Map<String, String> firmData = aimFirmSceneManagementRepository.getNameAndCodeById(firmRelationship.getFirmId());
//                typeCodeList.add(firmData.get("code"));
//            }
//        }
        Map<String, MyLink> linkMap = LinkUtil.convertLink(EventOrder.class);
        String finalSceneId = sceneId;

        Page<EventOrder> res = eventOrderRepository.findAll((root, query, builder) -> {
            //用于拼接条件
            List<Predicate> ps = new ArrayList<>();

            if (StringUtils.isNotBlank(finalSceneId)) {
                ps.add(builder.in(root.get("typeCode")).value(typeCodeList));
            }

            if (StringUtils.isNotBlank(bean.getProjectId())) {
                ps.add(builder.equal(root.get("projectId"), bean.getProjectId()));
            }
            return ScQueryUtil.getPredicate(ps, bean, linkMap, root, query, builder);
        }, PageRequest.of(bean.getPage(), bean.getSize()));
        ScQueryUtil.dealWith(res, linkMap);
        return PageUtil.toBeanPage(res, EventOrderRespDTO.class);
    }

    @Override
    public Page<ESEventOrderRespDTO> esQuery(EventOrderQueryDTO bean) throws Exception {
        ScQueryUtil.getValuesRmAndSetBean(bean.getQuery(), Arrays.asList("sceneId", "companyName", "finishStatus", "regionPid","eventedAt"), bean);
        Pageable pageable = PageRequest.of(bean.getPage(), bean.getSize());
        List<ESEventOrderRespDTO> data = new ArrayList<>();

        EsBuilder builder = new EsBuilder();
        //设置区域限制参数
        if (ObjectUtil.isNotNull(bean.getRegionPid())) {
            builder.andIn("regionPid", bean.getRegionPid());
        }
        if (ObjectUtil.isNotNull(bean.getRegionId())) {
            builder.andIn("regionId", bean.getRegionId());
        }
        if (ObjectUtil.isNotNull(bean.getRegionCid())) {
            builder.andIn("regionCid", bean.getRegionCid());
        }
        if (Objects.nonNull(bean.getEventSource())) {
            builder.andEqual("eventSource", bean.getEventSource());
        }
        //限制宝安区不查询上报来源为轮询上报的数据，具体问懒豆腐
        //完全过滤轮询上报，轮巡上报有另外一个页面单独展示
        builder.andNotEqual("origin", "3");
        builder.andEqual("isDeleted", "0");

        // 创建 BoolQueryBuilder 对象
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

        if (StringUtils.isNotBlank(bean.getEventedAt())) {
            String[] split = bean.getEventedAt().split(",");
            // 创建第一个 range 查询
            boolQuery.filter(QueryBuilders.rangeQuery("eventedAt")
                    .from(split[0])
                    .to(split[1])
                    .includeLower(true)
                    .includeUpper(true)
                    .boost(1.0f));
        }


        builder.query(bean.getQuery(), ESEventOrderRespDTO.class);
        builder.pageable(pageable);
        // 创建脚本排序部分
//        ScriptSortBuilder scriptSortBuilder = new ScriptSortBuilder(
//                new Script(
//                        Script.DEFAULT_SCRIPT_TYPE,
//                        Script.DEFAULT_SCRIPT_LANG,
//                        "def values = params.values; def value = doc['id'].value; for (int i = 0; i < values.length; i++) { if (value == values[i]) { return i; } } return values.length;",
//                        Collections.singletonMap("values", topIds)
//                ),
//                ScriptSortBuilder.ScriptSortType.NUMBER)
//                .order(SortOrder.ASC);

        // 创建id排序部分
        FieldSortBuilder fieldSortBuilder = SortBuilders.fieldSort("id").order(SortOrder.DESC);
        if (Objects.nonNull(bean.getSorts())) {
            ScSort scSort = bean.getSorts().get(0);
            if (scSort.getOrderType().equals("ASC")) {
                fieldSortBuilder = SortBuilders.fieldSort(scSort.getOrderField()).order(SortOrder.ASC);
            } else {
                fieldSortBuilder = SortBuilders.fieldSort(scSort.getOrderField()).order(SortOrder.DESC);
            }
        }


        NativeSearchQuery query = builder.buildQuery().getSearchBuilder()
                .withFilter(boolQuery)
//                .withSort(scriptSortBuilder)
                .withSort(fieldSortBuilder)
                .build();
        query.setTrackTotalHits(true);

        SearchHits<ESEventOrderRespDTO> result = esOperation.search(
                query,
                ESEventOrderRespDTO.class,
                IndexCoordinates.of(esIndexConfig.getEventOrderIndex())
        );
        data = result.stream().map(hit -> {
            ESEventOrderRespDTO dto = hit.getContent();
            handleFields(dto);
            dealScene(dto);
            return dto;
        }).collect(Collectors.toList());
        return new PageImpl<>(data, pageable, result.getTotalHits());
    }

    private void handleFields(ESEventOrderRespDTO dto) {
        esOperation.fieldsLocalTime(dto);
        if (StringUtils.isNotBlank(dto.getRegionPid())) {
            Optional<ComRegion> comRegionOptional = comRegionRepository.findById(dto.getRegionPid());
            if (comRegionOptional.isPresent()) {
                dto.setDistrictName(comRegionOptional.get().getTitle());
            }
        }
        if (StringUtils.isNotBlank(dto.getRegionId())) {
            Optional<ComRegion> comRegionOptional = comRegionRepository.findById(dto.getRegionId());
            if (comRegionOptional.isPresent()) {
                dto.setStreetName(comRegionOptional.get().getTitle());
            }
        }
        if (StringUtils.isNotBlank(dto.getRegionCid())) {
            Optional<ComRegion> comRegionOptional = comRegionRepository.findById(dto.getRegionCid());
            if (comRegionOptional.isPresent()) {
                dto.setVillageName(comRegionOptional.get().getTitle());
            }
        }
        if (Objects.nonNull(dto.getEventSource())) {
            Optional<EventOrderSource> sourceOptional = eventOrderSourceRepository.findById(String.valueOf(dto.getEventSource()));
            if (sourceOptional.isPresent()) {
                dto.setEventSourceStr(sourceOptional.get().getName());
            }
        }

    }



    @Override
    public EventOrderRespDTO show(ScShowDTO bean) {
        SecurityUser currentUser = ScContext.getCurrentUserThrow();
        Optional<EventOrder> option = eventOrderRepository.findById(bean.getId());
        if (!option.isPresent()) {
            return null;
        }
        EventOrder entity = option.get();
        Map<String, MyLink> linkMap = LinkUtil.convertLink(EventOrder.class);
        ScQueryUtil.dealWith(entity, linkMap);

        EventOrderRespDTO eventOrderRespDTO = BeanUtil.toBean(entity, EventOrderRespDTO.class);
        dealScene(eventOrderRespDTO);
        if (eventOrderRespDTO.getCameraType() == null && StringUtils.isNotBlank(eventOrderRespDTO.getCameraNo())) {
            String cameraNo = eventOrderRespDTO.getCameraNo();
            int indexOf = cameraNo.lastIndexOf("#");
            cameraNo = cameraNo.substring(0, indexOf < 1 ? cameraNo.length() : indexOf);
            //查询openAI返回类型
            eventOrderRespDTO.setCameraType(eventOrderRepository.findOpenAiCameraTypeByCameraNo(cameraNo));
        }

        eventOrderRespDTO.setHasVideoUrl(0);
//        if (StrUtil.equals(CommonConstant.LONGGANG_REGION_PID,eventOrderRespDTO.getRegionPid())){
//             String videoUrl =   eventOrderRepository.getEventOrderVideoUrl(eventOrderRespDTO.getId());
//             if (StrUtil.isNotBlank(videoUrl)){
//                 eventOrderRespDTO.setVideoUrl(videoUrl);
//                 eventOrderRespDTO.setHasVideoUrl(1);
//             }
//        }
        return eventOrderRespDTO;
    }


    private void dealScene(ESEventOrderRespDTO eventOrderRespDTO) {
//        StringBuilder sb = new StringBuilder();
//        //根据违规类型编号 获取厂商-> 获取AI场景
//        List<String> sceneNames = aimFirmSceneManagementRepository.getSceneName(eventOrderRespDTO.getTypeCode());
//        if (CollectionUtils.isNotEmpty(sceneNames)) {
//            for (String sceneName : sceneNames) {
//                sb.append(sceneName).append(",");
//            }
//            eventOrderRespDTO.setSceneName(sb.substring(0, sb.length() - 1));
//        }
    }

    private void dealScene(EventOrderRespDTO eventOrderRespDTO) {
//        StringBuilder sb = new StringBuilder();
//        //根据违规类型编号 获取厂商-> 获取AI场景
//        List<String> sceneNames = aimFirmSceneManagementRepository.getSceneName(eventOrderRespDTO.getTypeCode());
//        if (CollectionUtils.isNotEmpty(sceneNames)) {
//            for (String sceneName : sceneNames) {
//                sb.append(sceneName).append(",");
//            }
//            eventOrderRespDTO.setSceneName(sb.substring(0, sb.length() - 1));
//        }
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public EventOrder create(EventOrderCreateDTO bean) {
        EventOrder entity = new EventOrder();
        BeanUtil.copyProperties(bean, entity);
        return eventOrderRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public EventOrder update(EventOrderUpdateDTO bean) {
        EventOrder entity = eventOrderRepository.findById(bean.getId())
                .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));
        BeanUtil.copyProperties(bean, entity);
        return eventOrderRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            ids.forEach(id -> eventOrderRepository.findById(id).ifPresent(entity -> {
                entity.setIsDeleted(1);
                entity.setDeletedAt(new Date());
                eventOrderRepository.save(entity);
            }));
        }
    }

    @Override
    public boolean export(EventOrderQueryDTO bean) {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        return ScExport.exportAsync(bean.getCfg(), ESEventOrderRespDTO.class, ExportThreadPool.getInstance(), () -> {
            List<ESEventOrderRespDTO> content = null;
            try {
                RequestContextHolder.setRequestAttributes(requestAttributes,true);
                content = esQuery(bean).getContent();
            } catch (Exception e) {
                e.printStackTrace();
                throw new ScException("查询失败");
            }
            return new ArrayList<>(
                    content
            );
        });
    }


    @Override
    public List<EventOrderTypeRespDTO> typeList(EventOrderTypeQueryDTO bean) {
        Map<String, MyLink> linkMap = LinkUtil.convertLink(EventOrderType.class);
        List<EventOrderType> res = eventOrderTypeRepository.findAll((root, query, builder) -> {
            //用于拼接条件
            List<Predicate> ps = new ArrayList<>();

            switch (bean.getTag()) {
                case 1:
                    ps.add(builder.equal(root.get("isSelf"), 1));
                    break;
                case 2:
                    ps.add(builder.equal(root.get("isOpen"), 1));
                    break;
            }

            return ScQueryUtil.getPredicate(ps, bean, linkMap, root, query, builder);
        });
        return ScQueryUtil.handle(res, linkMap, EventOrderTypeRespDTO.class);
    }
}