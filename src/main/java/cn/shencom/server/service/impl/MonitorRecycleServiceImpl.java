package cn.shencom.server.service.impl;

import cn.shencom.model.MonitorRecycle;
import cn.shencom.model.dto.create.MonitorRecycleCreateDTO;
import cn.shencom.model.dto.query.MonitorRecycleQueryDTO;
import cn.shencom.model.dto.resp.MonitorRecycleRespDTO;
import cn.shencom.model.dto.update.MonitorRecycleUpdateDTO;
import cn.shencom.repos.MonitorRecycleRepository;
import cn.shencom.scloud.common.base.constant.RespCode;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.jpa.scpage.MyLink;
import cn.shencom.scloud.common.jpa.util.export.ScExport;
import cn.shencom.scloud.common.jpa.util.query.LinkUtil;
import cn.shencom.scloud.common.jpa.util.query.ScQueryUtil;
import cn.shencom.scloud.common.server.service.BaseImpl;
import cn.shencom.scloud.common.util.BeanUtil;
import cn.shencom.server.service.IMonitorRecycleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.util.*;


/**
 * 小散工程-监管工单流程-上门回收详情 的服务实现
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@Service
@Slf4j
public class MonitorRecycleServiceImpl extends BaseImpl implements IMonitorRecycleService {

    @Autowired
    private MonitorRecycleRepository monitorRecycleRepository;

    @Override
    public Page<MonitorRecycleRespDTO> query(MonitorRecycleQueryDTO bean) {
        Map<String, MyLink> linkMap = LinkUtil.convertLink(MonitorRecycle.class);
        Page<MonitorRecycle> res = monitorRecycleRepository.findAll((root, query, builder) -> {
            //用于拼接条件
            List<Predicate> ps = new ArrayList<>();

            return ScQueryUtil.getPredicate(ps, bean, linkMap, root, query, builder);
        }, PageRequest.of(bean.getPage(), bean.getSize()));
        return ScQueryUtil.handle(res, linkMap, MonitorRecycleRespDTO.class);
    }

    @Override
    public MonitorRecycleRespDTO show(ScShowDTO bean) {
        Optional<MonitorRecycle> option = monitorRecycleRepository.findById(bean.getId());
        if (!option.isPresent()) {
            return null;
        }
        MonitorRecycle entity = option.get();
        Map<String, MyLink> linkMap = LinkUtil.convertLink(MonitorRecycle.class);
        return ScQueryUtil.handleOne(entity, linkMap, MonitorRecycleRespDTO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public MonitorRecycle create(MonitorRecycleCreateDTO bean) {
        MonitorRecycle entity = new MonitorRecycle();
        BeanUtil.copyProperties(bean, entity);
        return monitorRecycleRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public MonitorRecycle update(MonitorRecycleUpdateDTO bean) {
        MonitorRecycle entity = monitorRecycleRepository.findById(bean.getId())
                .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));
        BeanUtil.copyProperties(bean, entity);
        return monitorRecycleRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            ids.forEach(id -> monitorRecycleRepository.deleteById(id));
        }
    }

    @Override
    public void export(MonitorRecycleQueryDTO bean) {
        List<MonitorRecycleRespDTO> dtos = new ArrayList<>(query(bean).getContent());
        ScExport.export(bean.getCfg(), dtos, MonitorRecycleRespDTO.class);
    }
}