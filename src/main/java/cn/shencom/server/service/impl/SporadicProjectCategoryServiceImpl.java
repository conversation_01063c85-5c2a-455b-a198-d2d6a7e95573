package cn.shencom.server.service.impl;

import cn.shencom.model.SporadicProjectCategory;
import cn.shencom.model.dto.SporadicProjectCategoryTreeDTO;
import cn.shencom.model.dto.create.SporadicProjectCategoryCreateDTO;
import cn.shencom.model.dto.query.SporadicProjectCategoryQueryDTO;
import cn.shencom.model.dto.resp.SporadicProjectCategoryRespDTO;
import cn.shencom.model.dto.update.SporadicProjectCategoryUpdateDTO;
import cn.shencom.repos.SporadicProjectCategoryRepository;
import cn.shencom.scloud.common.base.constant.RespCode;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.jpa.scpage.MyLink;
import cn.shencom.scloud.common.jpa.util.export.ScExport;
import cn.shencom.scloud.common.jpa.util.query.LinkUtil;
import cn.shencom.scloud.common.jpa.util.query.ScQueryUtil;
import cn.shencom.scloud.common.server.service.BaseImpl;
import cn.shencom.scloud.common.util.BeanUtil;
import cn.shencom.server.service.ISporadicProjectCategoryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 小散工程分类 的服务实现
 *
 * <AUTHOR>
 * @since 2025-05-29
 */
@Service
@Slf4j
public class SporadicProjectCategoryServiceImpl extends BaseImpl implements ISporadicProjectCategoryService {

    @Autowired
    private SporadicProjectCategoryRepository sporadicProjectCategoryRepository;

    @Override
    public Page<SporadicProjectCategoryRespDTO> query(SporadicProjectCategoryQueryDTO bean) {
        Map<String, MyLink> linkMap = LinkUtil.convertLink(SporadicProjectCategory.class);
        Page<SporadicProjectCategory> res = sporadicProjectCategoryRepository.findAll((root, query, builder) -> {
            //用于拼接条件
            List<Predicate> ps = new ArrayList<>();

            return ScQueryUtil.getPredicate(ps, bean, linkMap, root, query, builder);
        }, PageRequest.of(bean.getPage(), bean.getSize()));
        return ScQueryUtil.handle(res, linkMap, SporadicProjectCategoryRespDTO.class);
    }

    @Override
    public List<SporadicProjectCategoryTreeDTO> tree() {
        List<SporadicProjectCategory> pCates = sporadicProjectCategoryRepository.findAllBypId("0");
        List<String> pids = pCates.stream().map(SporadicProjectCategory::getId).collect(Collectors.toList());
        List<SporadicProjectCategory> cates = sporadicProjectCategoryRepository.findAllBypIdIn(pids);
        Map<String, List<SporadicProjectCategoryTreeDTO>> childrenMap = new HashMap<>();
        cates.forEach(cate -> {
            List<SporadicProjectCategoryTreeDTO> children = childrenMap.getOrDefault(cate.getPId(),Lists.newArrayList());
            children.add(new SporadicProjectCategoryTreeDTO(cate.getId(), cate.getPId(), cate.getName(), null));
            childrenMap.put(cate.getPId(), children);
        });

        List<SporadicProjectCategoryTreeDTO> result = Lists.newArrayList();
        pCates.forEach(pCate -> result.add(
                new SporadicProjectCategoryTreeDTO(pCate.getId(), pCate.getPId(), pCate.getName(), childrenMap.get(pCate.getId())))
        );
        return result;
    }

    @Override
    public SporadicProjectCategoryRespDTO show(ScShowDTO bean) {
        Optional<SporadicProjectCategory> option = sporadicProjectCategoryRepository.findById(bean.getId());
        if (!option.isPresent()) {
            return null;
        }
        SporadicProjectCategory entity = option.get();
        Map<String, MyLink> linkMap = LinkUtil.convertLink(SporadicProjectCategory.class);
        return ScQueryUtil.handleOne(entity, linkMap, SporadicProjectCategoryRespDTO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public SporadicProjectCategory create(SporadicProjectCategoryCreateDTO bean) {
        SporadicProjectCategory entity = new SporadicProjectCategory();
        BeanUtil.copyProperties(bean, entity);
        return sporadicProjectCategoryRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public SporadicProjectCategory update(SporadicProjectCategoryUpdateDTO bean) {
        SporadicProjectCategory entity = sporadicProjectCategoryRepository.findById(bean.getId())
                .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));
        BeanUtil.copyProperties(bean, entity);
        return sporadicProjectCategoryRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            ids.forEach(id -> sporadicProjectCategoryRepository.findById(id).ifPresent(entity -> {
                entity.setIsDeleted(1);
                entity.setDeletedAt(new Date());
                sporadicProjectCategoryRepository.save(entity);
            }));
        }
    }

    @Override
    public void export(SporadicProjectCategoryQueryDTO bean) {
        List<SporadicProjectCategoryRespDTO> dtos = new ArrayList<>(query(bean).getContent());
        ScExport.export(bean.getCfg(), dtos, SporadicProjectCategoryRespDTO.class);
    }
}