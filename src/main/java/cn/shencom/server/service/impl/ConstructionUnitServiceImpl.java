package cn.shencom.server.service.impl;

import cn.shencom.model.ConstructionUnit;
import cn.shencom.model.dto.create.ConstructionUnitCreateDTO;
import cn.shencom.model.dto.query.ConstructionUnitQueryDTO;
import cn.shencom.model.dto.resp.ConstructionUnitRespDTO;
import cn.shencom.model.dto.update.ConstructionUnitUpdateDTO;
import cn.shencom.repos.ConstructionUnitRepository;
import cn.shencom.scloud.common.base.constant.RespCode;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.jpa.scpage.MyLink;
import cn.shencom.scloud.common.jpa.util.export.ScExport;
import cn.shencom.scloud.common.jpa.util.query.LinkUtil;
import cn.shencom.scloud.common.jpa.util.query.ScQueryUtil;
import cn.shencom.scloud.common.server.service.BaseImpl;
import cn.shencom.scloud.common.util.BeanUtil;
import cn.shencom.server.service.IConstructionUnitService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.util.*;

/**
 * 施工单位表 的服务实现
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Service
@Slf4j
public class ConstructionUnitServiceImpl extends BaseImpl implements IConstructionUnitService {

    @Autowired
    private ConstructionUnitRepository constructionUnitRepository;

    @Override
    public Page<ConstructionUnitRespDTO> query(ConstructionUnitQueryDTO bean) {
        Map<String, MyLink> linkMap = LinkUtil.convertLink(ConstructionUnit.class);
        Page<ConstructionUnit> res = constructionUnitRepository.findAll((root, query, builder) -> {
            // 用于拼接条件
            List<Predicate> ps = new ArrayList<>();

            return ScQueryUtil.getPredicate(ps, bean, linkMap, root, query, builder);
        }, PageRequest.of(bean.getPage(), bean.getSize()));
        return ScQueryUtil.handle(res, linkMap, ConstructionUnitRespDTO.class);
    }

    @Override
    public ConstructionUnitRespDTO show(ScShowDTO bean) {
        Optional<ConstructionUnit> option = constructionUnitRepository.findById(bean.getId());
        if (!option.isPresent()) {
            return null;
        }
        ConstructionUnit entity = option.get();
        Map<String, MyLink> linkMap = LinkUtil.convertLink(ConstructionUnit.class);
        return ScQueryUtil.handleOne(entity, linkMap, ConstructionUnitRespDTO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ConstructionUnit create(ConstructionUnitCreateDTO bean) {
        ConstructionUnit entity = new ConstructionUnit();
        BeanUtil.copyProperties(bean, entity);
        return constructionUnitRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ConstructionUnit update(ConstructionUnitUpdateDTO bean) {
        ConstructionUnit entity = constructionUnitRepository.findById(bean.getId())
                .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));
        BeanUtil.copyProperties(bean, entity);
        return constructionUnitRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            ids.forEach(id -> constructionUnitRepository.findById(id).ifPresent(entity -> {
                entity.setIsDeleted(1);
                entity.setDeletedAt(new Date());
                constructionUnitRepository.save(entity);
            }));
        }
    }

    @Override
    public void export(ConstructionUnitQueryDTO bean) {
        List<ConstructionUnitRespDTO> dtos = new ArrayList<>(query(bean).getContent());
        ScExport.export(bean.getCfg(), dtos, ConstructionUnitRespDTO.class);
    }
}