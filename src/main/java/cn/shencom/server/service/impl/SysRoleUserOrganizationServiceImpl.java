package cn.shencom.server.service.impl;

import cn.shencom.enums.BusinessRoleEnum;
import cn.shencom.enums.OrganizationMemberTypeEnum;
import cn.shencom.model.*;
import cn.shencom.model.dto.SimpleMemberDTO;
import cn.shencom.model.dto.create.SysRoleUserOrganizationCreateDTO;
import cn.shencom.model.dto.query.SysRoleUserOrganizationQueryDTO;
import cn.shencom.model.dto.resp.SysRoleUserOrganizationRespDTO;
import cn.shencom.model.dto.resp.XsgcOrganizationRespDTO;
import cn.shencom.model.dto.update.SysRoleUserOrganizationUpdateDTO;
import cn.shencom.repos.*;
import cn.shencom.scloud.common.base.constant.RespCode;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.jpa.scpage.MyLink;
import cn.shencom.scloud.common.jpa.util.export.ScExport;
import cn.shencom.scloud.common.jpa.util.query.LinkUtil;
import cn.shencom.scloud.common.jpa.util.query.ScQueryUtil;
import cn.shencom.scloud.common.server.service.BaseImpl;
import cn.shencom.scloud.common.util.BeanUtil;
import cn.shencom.scloud.common.util.ScidContext;
import cn.shencom.scloud.common.util.StringUtil;
import cn.shencom.scloud.scloudapiuaa.dto.SysRoleUserDTO;
import cn.shencom.scloud.scloudapiuaa.service.UaaService;
import cn.shencom.scloud.security.core.ScContext;
import cn.shencom.server.service.IFnRmsv3MembersTypeRelateService;
import cn.shencom.server.service.ISysRoleUserOrganizationService;
import cn.shencom.utils.XsgcContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.util.*;
import java.util.stream.Collectors;

import static cn.shencom.enums.BusinessRoleEnum.*;


/**
 * 系统角色，权限，组织关联表 的服务实现
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Service
@Slf4j
public class SysRoleUserOrganizationServiceImpl extends BaseImpl implements ISysRoleUserOrganizationService {

    @Autowired
    private SysRoleUserOrganizationRepository sysRoleUserOrganizationRepository;

    @Autowired
    private FnRmsv3MembersTypeRelateRepository fnRmsv3MembersTypeRelateRepository;

    @Autowired
    private XsgcCustomerInfoRepository xsgcCustomerInfoRepository;

    @Autowired
    private XsgcSubscriptionRepository xsgcSubscriptionRepository;

    @Autowired
    private SysRolesRepository sysRolesRepository;

    @Autowired
    private SysUsersRepository sysUsersRepository;

    @Autowired
    private IFnRmsv3MembersTypeRelateService fnRmsv3MembersTypeRelateService;

    @Autowired
    private UaaService uaaService;


    @Override
    @Transactional
    public void addPermissionForOrganization(String organizationId) {

        //查询当前组织绑定的客户
        XsgcCustomerInfo customerInfo = xsgcCustomerInfoRepository.findFirstByOrganizationId(organizationId);
        if (customerInfo==null){
            log.error("客户不存在!无法添加权限，组织id: {}",organizationId);
            return;
        }
        //查询当前客户使用的套餐
        Optional<XsgcSubscription> subscriptionOptional = xsgcSubscriptionRepository.findById(customerInfo.getOptionId());
        if (!subscriptionOptional.isPresent()){
            log.error("套餐不存在!无法添加权限，套餐id: {}",customerInfo.getOptionId());
            return;
        }

        XsgcSubscription xsgcSubscription = subscriptionOptional.get();

        //当前组织的全部成员
        List<SimpleMemberDTO> simpleMemberDTOList = fnRmsv3MembersTypeRelateRepository.findAllRelateByOrganizationId(organizationId);
        if (simpleMemberDTOList.isEmpty()){
            return;
        }


        List<BusinessRoleEnum> businessRoleEnumList;
        if (xsgcSubscription.getType()==1){
            //高级套餐
            businessRoleEnumList = SENIOR_ROLES;
        }else {
            //低级套餐
            businessRoleEnumList = ORDINARY_ROLES;
        }

        Map<String, List<SimpleMemberDTO>> collectMap = simpleMemberDTOList.stream().collect(Collectors.groupingBy(SimpleMemberDTO::getTypeId));

        for (BusinessRoleEnum businessRoleEnum : businessRoleEnumList) {
            SysRoles sysRoles = sysRolesRepository.findFirstByName(businessRoleEnum.getRole());
            if (sysRoles==null){
                throw new ScException("角色不存在！角色标识：{}", businessRoleEnum.getRole());
            }
            if (collectMap.containsKey(businessRoleEnum.getMemberType())){
                List<SysRoleUserOrganization> sysRoleUserOrganizationList = new ArrayList<>();
                for (SimpleMemberDTO memberDTO : collectMap.get(businessRoleEnum.getMemberType())) {
                    SysRoleUserOrganization entity = new SysRoleUserOrganization();
                    entity.setOrganizationId(organizationId);
                    entity.setUserId(memberDTO.getUserId());
                    entity.setRoleId(sysRoles.getId());
                    sysRoleUserOrganizationList.add(entity);
                }
                sysRoleUserOrganizationRepository.saveAll(sysRoleUserOrganizationList);
            }
        }

    }


    @Override
    @Transactional
    public void removePermissionForOrganization(String organizationId) {

        //移除掉当前客户关联的全部用户的权限
        sysRoleUserOrganizationRepository.deleteByOrOrganizationId(organizationId);
    }


    @Override
    @Transactional
    public void addPermissionForUser(SysRoleUserOrganizationUpdateDTO bean) {
        String organizationId = bean.getOrganizationId();
        String userId = bean.getUserId();

        for (String role : bean.getRoleNames()) {
            SysRoles sysRoles = sysRolesRepository.findFirstByName(role);
            //查询是否存在权限
            if (sysRoleUserOrganizationRepository.existsByOrganizationIdAndUserIdAndRoleId(organizationId,userId,sysRoles.getId())){
                //有权限，无需添加
                return;
            }
            SysRoleUserOrganization entity = new SysRoleUserOrganization();
            entity.setRoleId(sysRoles.getId());
            entity.setUserId(userId);
            entity.setOrganizationId(organizationId);
            sysRoleUserOrganizationRepository.save(entity);
        }


        for (String role : bean.getRoleNames()) {
            if (role.equals(ADMINISTRATOR.getRole())){
                SysRoles sysRoles = sysRolesRepository.findFirstByName(role);
                //如果是组织管理员，需要额外关联表sys_role_user
                SysRoleUserDTO sysRoleUserDTO = new SysRoleUserDTO();
                sysRoleUserDTO.setUserIds(userId);
                sysRoleUserDTO.setRoleIds(sysRoles.getId());
                uaaService.createUserRoles(ScidContext.getScid(),sysRoleUserDTO);
            }
        }



    }


    @Override
    public void removePermissionForUser(SysRoleUserOrganizationUpdateDTO bean) {
        String organizationId = bean.getOrganizationId();
        String userId = bean.getUserId();

        for (String role : bean.getRoleNames()) {
            SysRoles sysRoles = sysRolesRepository.findFirstByName(role);
            //查询是否存在权限
            List<SysRoleUserOrganization> sysRoleUserOrganizations = sysRoleUserOrganizationRepository.findByOrganizationIdAndUserIdAndRoleId(organizationId, userId, sysRoles.getId());
            if (!sysRoleUserOrganizations.isEmpty()){
                sysRoleUserOrganizationRepository.deleteAll(sysRoleUserOrganizations);
            }
        }
    }

    @Override
    public Page<SysRoleUserOrganizationRespDTO> query(SysRoleUserOrganizationQueryDTO bean) {
        Map<String, MyLink> linkMap = LinkUtil.convertLink(SysRoleUserOrganization.class);
        Page<SysRoleUserOrganization> res = sysRoleUserOrganizationRepository.findAll((root, query, builder) -> {
            //用于拼接条件
            List<Predicate> ps = new ArrayList<>();

            return ScQueryUtil.getPredicate(ps, bean, linkMap, root, query, builder);
        }, PageRequest.of(bean.getPage(), bean.getSize()));
        return ScQueryUtil.handle(res, linkMap, SysRoleUserOrganizationRespDTO.class);
    }

    @Override
    public SysRoleUserOrganizationRespDTO show(ScShowDTO bean) {
        Optional<SysRoleUserOrganization> option = sysRoleUserOrganizationRepository.findById(bean.getId());
        if (!option.isPresent()) {
            return null;
        }
        SysRoleUserOrganization entity = option.get();
        Map<String, MyLink> linkMap = LinkUtil.convertLink(SysRoleUserOrganization.class);
        return ScQueryUtil.handleOne(entity, linkMap, SysRoleUserOrganizationRespDTO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public SysRoleUserOrganization create(SysRoleUserOrganizationCreateDTO bean) {
        SysRoleUserOrganization entity = new SysRoleUserOrganization();
        BeanUtil.copyProperties(bean, entity);
        return sysRoleUserOrganizationRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public SysRoleUserOrganization update(SysRoleUserOrganizationUpdateDTO bean) {
        SysRoleUserOrganization entity = sysRoleUserOrganizationRepository.findById(bean.getId())
                .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));
        BeanUtil.copyProperties(bean, entity);
        return sysRoleUserOrganizationRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            ids.forEach(id -> sysRoleUserOrganizationRepository.deleteById(id));
        }
    }

    @Override
    public void export(SysRoleUserOrganizationQueryDTO bean) {
        List<SysRoleUserOrganizationRespDTO> dtos = new ArrayList<>(query(bean).getContent());
        ScExport.export(bean.getCfg(), dtos, SysRoleUserOrganizationRespDTO.class);
    }


    @Override
    @Transactional
    public XsgcOrganizationRespDTO switchOrganization(SysRoleUserOrganizationQueryDTO bean) {
        String organizationId = bean.getOrganizationId();
        String userId = ScContext.getCurrentUserThrow().getId();
        XsgcOrganizationRespDTO organization = null;
        int platform = bean.getPlatform();

        //查询用户
        SysUsers sysUsers = sysUsersRepository.findById(userId).orElseThrow(() -> new ScException("当前用户不存在！"));
        //查询当前用户的所有组织
        List<XsgcOrganizationRespDTO> organizationList = fnRmsv3MembersTypeRelateService.allOrganization();

        //检查当前组织是否存在
        if (organizationList.isEmpty()){
            throw new ScException("您未加入组织！");
        }

        if (StringUtil.isBlank(organizationId)){
            //查询上一次的组织id
            organizationId = platform ==0?  sysUsers.getLastOrganization() : sysUsers.getLastOrganizationMobile();
            organization = findOrganization(organizationList, organizationId);

        }else {
            //校验要切换的组织是否存在
            organization = findOrganization(organizationList, organizationId);
            if (organization == null){
                //未加入当前组织
                throw new ScException("组织切换失败！您未加入该组织，请刷新页面试试!");
            }
        }
        if (organization==null){
            //如果上一次选择的组织为空，则获取当前用户的所有组织,并选择一个
            organization=organizationList.get(0);
        }

        if (platform==0){
            sysUsers.setLastOrganization(organization.getId());
        }else {
            sysUsers.setLastOrganizationMobile(organization.getId());
        }

        sysUsersRepository.save(sysUsers);

        return organization;
    }


    /**
     *   根据id查询组织,如果选择的组织仍然有效，则返回，否则返回null
     */
    private XsgcOrganizationRespDTO findOrganization(List<XsgcOrganizationRespDTO> organization, String organizationId){
        return organization.stream().filter(o -> o.getId().equals(organizationId)).findFirst().orElse(null);
    }


    @Override
    public List<SysRoleUserOrganizationRespDTO> getAllRoles(SysRoleUserOrganizationQueryDTO bean) {

        String userId = ScContext.getCurrentUserThrow().getId();
        //组织id
        String organizationId = XsgcContext.getOrganizationId();
        bean.setUserId(userId);
        bean.setOrganizationId(organizationId);


        List<SysRoleUserOrganizationRespDTO> sysRolesList ;
        if (StringUtil.isBlank(organizationId)){
            sysRolesList = sysRolesRepository.getAllRoles(bean);
        }else {
            sysRolesList = sysRolesRepository.getAllOrganizationRoles(bean);
        }
        return sysRolesList;
    }
}