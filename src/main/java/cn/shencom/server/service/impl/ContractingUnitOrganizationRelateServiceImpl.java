package cn.shencom.server.service.impl;

import cn.shencom.model.ContractingUnitOrganizationRelate;
import cn.shencom.model.dto.create.ContractingUnitOrganizationRelateCreateDTO;
import cn.shencom.model.dto.query.ContractingUnitOrganizationRelateQueryDTO;
import cn.shencom.model.dto.resp.ContractingUnitOrganizationRelateRespDTO;
import cn.shencom.model.dto.update.ContractingUnitOrganizationRelateUpdateDTO;
import cn.shencom.repos.ContractingUnitOrganizationRelateRepository;
import cn.shencom.scloud.common.base.constant.RespCode;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.jpa.scpage.MyLink;
import cn.shencom.scloud.common.jpa.util.export.ScExport;
import cn.shencom.scloud.common.jpa.util.query.LinkUtil;
import cn.shencom.scloud.common.jpa.util.query.ScQueryUtil;
import cn.shencom.scloud.common.server.service.BaseImpl;
import cn.shencom.scloud.common.util.BeanUtil;
import cn.shencom.scloud.security.core.ScContext;
import cn.shencom.server.service.IContractingUnitOrganizationRelateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.util.*;

/**
 * 施工单位组织关联表 的服务实现
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Service
@Slf4j
public class ContractingUnitOrganizationRelateServiceImpl extends BaseImpl
        implements IContractingUnitOrganizationRelateService {

    @Autowired
    private ContractingUnitOrganizationRelateRepository contractingUnitOrganizationRelateRepository;

    @Override
    public Page<ContractingUnitOrganizationRelateRespDTO> query(ContractingUnitOrganizationRelateQueryDTO bean) {
        Map<String, MyLink> linkMap = LinkUtil.convertLink(ContractingUnitOrganizationRelate.class);
        Page<ContractingUnitOrganizationRelate> res = contractingUnitOrganizationRelateRepository
                .findAll((root, query, builder) -> {
                    // 用于拼接条件
                    List<Predicate> ps = new ArrayList<>();

                    return ScQueryUtil.getPredicate(ps, bean, linkMap, root, query, builder);
                }, PageRequest.of(bean.getPage(), bean.getSize()));
        return ScQueryUtil.handle(res, linkMap, ContractingUnitOrganizationRelateRespDTO.class);
    }

    @Override
    public ContractingUnitOrganizationRelateRespDTO show(ScShowDTO bean) {
        Optional<ContractingUnitOrganizationRelate> option = contractingUnitOrganizationRelateRepository
                .findById(bean.getId());
        if (!option.isPresent()) {
            return null;
        }
        ContractingUnitOrganizationRelate entity = option.get();
        Map<String, MyLink> linkMap = LinkUtil.convertLink(ContractingUnitOrganizationRelate.class);
        return ScQueryUtil.handleOne(entity, linkMap, ContractingUnitOrganizationRelateRespDTO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ContractingUnitOrganizationRelate create(ContractingUnitOrganizationRelateCreateDTO bean) {
        ContractingUnitOrganizationRelate entity = new ContractingUnitOrganizationRelate();
        BeanUtil.copyProperties(bean, entity);
        return contractingUnitOrganizationRelateRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ContractingUnitOrganizationRelate update(ContractingUnitOrganizationRelateUpdateDTO bean) {
        ContractingUnitOrganizationRelate entity = contractingUnitOrganizationRelateRepository.findById(bean.getId())
                .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));
        BeanUtil.copyProperties(bean, entity);
        return contractingUnitOrganizationRelateRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ContractingUnitOrganizationRelate createOrUpdate(ContractingUnitOrganizationRelateUpdateDTO bean) {

        // 通过施工单位id和组织id查询
        ContractingUnitOrganizationRelate entity = contractingUnitOrganizationRelateRepository
                .findByContractingUnitIdAndOrganizationId(bean.getContractingUnitId(), bean.getOrganizationId())
                .orElse(null);

        String userId = ScContext.getCurrentUserThrow().getId();
        
        if (entity == null) {
            bean.setCreatedUser(userId);
            entity = new ContractingUnitOrganizationRelate();
        }
        
        bean.setUpdatedUser(userId);
        BeanUtil.copyProperties(bean, entity);

        return contractingUnitOrganizationRelateRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            ids.forEach(id -> contractingUnitOrganizationRelateRepository.findById(id).ifPresent(entity -> {
                entity.setIsDeleted(1);
                entity.setDeletedAt(new Date());
                contractingUnitOrganizationRelateRepository.save(entity);
            }));
        }
    }

    @Override
    public void export(ContractingUnitOrganizationRelateQueryDTO bean) {
        List<ContractingUnitOrganizationRelateRespDTO> dtos = new ArrayList<>(query(bean).getContent());
        ScExport.export(bean.getCfg(), dtos, ContractingUnitOrganizationRelateRespDTO.class);
    }
}