package cn.shencom.server.service.impl;

import cn.shencom.model.SporadicProjectMemo;
import cn.shencom.model.dto.create.SporadicProjectMemoCreateDTO;
import cn.shencom.model.dto.query.SporadicProjectMemoQueryDTO;
import cn.shencom.model.dto.query.SporadicProjectQueryDTO;
import cn.shencom.model.dto.resp.SporadicProjectMemoRespDTO;
import cn.shencom.model.dto.update.SporadicProjectMemoUpdateDTO;
import cn.shencom.repos.SporadicProjectMemoRepository;
import cn.shencom.scloud.common.base.constant.RespCode;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.jpa.scpage.MyLink;
import cn.shencom.scloud.common.jpa.util.export.ScExport;
import cn.shencom.scloud.common.jpa.util.query.LinkUtil;
import cn.shencom.scloud.common.jpa.util.query.ScQueryUtil;
import cn.shencom.scloud.common.server.service.BaseImpl;
import cn.shencom.scloud.common.util.BeanUtil;
import cn.shencom.scloud.security.core.ScContext;
import cn.shencom.server.service.ISporadicProjectMemoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.util.*;


/**
 * 小散工程-工程备注表 的服务实现
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Service
@Slf4j
public class SporadicProjectMemoServiceImpl extends BaseImpl implements ISporadicProjectMemoService {

    @Autowired
    private SporadicProjectMemoRepository sporadicProjectMemoRepository;

    @Override
    public Page<SporadicProjectMemoRespDTO> query(SporadicProjectMemoQueryDTO bean) {

        //todo 后续可以添加权限校验，只有业务人员能看备注

        Map<String, MyLink> linkMap = LinkUtil.convertLink(SporadicProjectMemo.class);
        Page<SporadicProjectMemo> res = sporadicProjectMemoRepository.findAll((root, query, builder) -> {
            //用于拼接条件
            List<Predicate> ps = new ArrayList<>();

            if (bean.getProjectId()!=null){
                ps.add(builder.equal(root.get("projectId"),bean.getProjectId()));
            }

            return ScQueryUtil.getPredicate(ps, bean, linkMap, root, query, builder);
        }, PageRequest.of(bean.getPage(), bean.getSize()));
        return ScQueryUtil.handle(res, linkMap, SporadicProjectMemoRespDTO.class);
    }


    @Override
    public List<SporadicProjectMemoRespDTO> findByProjectId(String projectId) {
        SporadicProjectMemoQueryDTO queryDTO = new SporadicProjectMemoQueryDTO();
        queryDTO.setProjectId(projectId);
        queryDTO.setSize(99);
        Page<SporadicProjectMemoRespDTO> page = query(queryDTO);
        return page.getContent();
    }

    @Override
    public SporadicProjectMemoRespDTO show(ScShowDTO bean) {
        Optional<SporadicProjectMemo> option = sporadicProjectMemoRepository.findById(bean.getId());
        if (!option.isPresent()) {
            return null;
        }
        SporadicProjectMemo entity = option.get();
        Map<String, MyLink> linkMap = LinkUtil.convertLink(SporadicProjectMemo.class);
        return ScQueryUtil.handleOne(entity, linkMap, SporadicProjectMemoRespDTO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public SporadicProjectMemo create(SporadicProjectMemoCreateDTO bean) {
        //todo 后续可以添加权限校验，只有业务人员能添加

        String userId= ScContext.getCurrentUserThrow().getId();
        bean.setUserId(userId);

        SporadicProjectMemo entity = new SporadicProjectMemo();
        BeanUtil.copyProperties(bean, entity);
        return sporadicProjectMemoRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public SporadicProjectMemo update(SporadicProjectMemoUpdateDTO bean) {
        SporadicProjectMemo entity = sporadicProjectMemoRepository.findById(bean.getId())
                .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));
        BeanUtil.copyProperties(bean, entity);
        return sporadicProjectMemoRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            ids.forEach(id -> sporadicProjectMemoRepository.findById(id).ifPresent(entity -> {
                entity.setIsDeleted(1);
                entity.setDeletedAt(new Date());
                sporadicProjectMemoRepository.save(entity);
            }));
        }
    }

    @Override
    public void export(SporadicProjectMemoQueryDTO bean) {
        List<SporadicProjectMemoRespDTO> dtos = new ArrayList<>(query(bean).getContent());
        ScExport.export(bean.getCfg(), dtos, SporadicProjectMemoRespDTO.class);
    }
}