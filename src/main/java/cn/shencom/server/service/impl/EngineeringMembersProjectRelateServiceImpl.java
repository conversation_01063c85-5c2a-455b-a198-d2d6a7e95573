package cn.shencom.server.service.impl;

import cn.shencom.model.EngineeringMembers;
import cn.shencom.model.EngineeringMembersProjectRelate;
import cn.shencom.model.dto.create.EngineeringMembersProjectRelateCreateDTO;
import cn.shencom.model.dto.query.EngineeringMembersProjectRelateQueryDTO;
import cn.shencom.model.dto.resp.EngineeringMembersProjectRelateRespDTO;
import cn.shencom.model.dto.update.EngineeringMembersProjectRelateUpdateDTO;
import cn.shencom.repos.EngineeringMembersProjectRelateRepository;
import cn.shencom.repos.EngineeringMembersRepository;
import cn.shencom.scloud.common.base.constant.RespCode;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.jpa.scpage.MyLink;
import cn.shencom.scloud.common.jpa.util.export.ScExport;
import cn.shencom.scloud.common.jpa.util.query.LinkUtil;
import cn.shencom.scloud.common.jpa.util.query.ScQueryUtil;
import cn.shencom.scloud.common.server.service.BaseImpl;
import cn.shencom.scloud.common.util.BeanUtil;
import cn.shencom.server.service.IEngineeringMembersProjectRelateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 小散工程-工程人员关联项目表 的服务实现
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Service
@Slf4j
public class EngineeringMembersProjectRelateServiceImpl extends BaseImpl
        implements IEngineeringMembersProjectRelateService {

    @Autowired
    private EngineeringMembersProjectRelateRepository engineeringMembersProjectRelateRepository;

    @Autowired
    private EngineeringMembersRepository engineeringMembersRepository;

    @Override
    public Page<EngineeringMembersProjectRelateRespDTO> query(EngineeringMembersProjectRelateQueryDTO bean) {
        Map<String, MyLink> linkMap = LinkUtil.convertLink(EngineeringMembersProjectRelate.class);
        Page<EngineeringMembersProjectRelate> res = engineeringMembersProjectRelateRepository
                .findAll((root, query, builder) -> {
                    // 用于拼接条件
                    List<Predicate> ps = new ArrayList<>();

                    return ScQueryUtil.getPredicate(ps, bean, linkMap, root, query, builder);
                }, PageRequest.of(bean.getPage(), bean.getSize()));
        return ScQueryUtil.handle(res, linkMap, EngineeringMembersProjectRelateRespDTO.class);
    }

    @Override
    public EngineeringMembersProjectRelateRespDTO show(ScShowDTO bean) {
        Optional<EngineeringMembersProjectRelate> option = engineeringMembersProjectRelateRepository
                .findById(bean.getId());
        if (!option.isPresent()) {
            return null;
        }
        EngineeringMembersProjectRelate entity = option.get();
        Map<String, MyLink> linkMap = LinkUtil.convertLink(EngineeringMembersProjectRelate.class);
        return ScQueryUtil.handleOne(entity, linkMap, EngineeringMembersProjectRelateRespDTO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public EngineeringMembersProjectRelate create(EngineeringMembersProjectRelateCreateDTO bean) {
        EngineeringMembersProjectRelate entity = new EngineeringMembersProjectRelate();
        BeanUtil.copyProperties(bean, entity);
        return engineeringMembersProjectRelateRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public EngineeringMembersProjectRelate update(EngineeringMembersProjectRelateUpdateDTO bean) {
        EngineeringMembersProjectRelate entity = engineeringMembersProjectRelateRepository.findById(bean.getId())
                .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));
        BeanUtil.copyProperties(bean, entity);
        return engineeringMembersProjectRelateRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            ids.forEach(id -> engineeringMembersProjectRelateRepository.findById(id).ifPresent(entity -> {
                entity.setIsDeleted(1);
                entity.setDeletedAt(new Date());
                engineeringMembersProjectRelateRepository.save(entity);
            }));
        }
    }

    @Override
    public void export(EngineeringMembersProjectRelateQueryDTO bean) {
        List<EngineeringMembersProjectRelateRespDTO> dtos = new ArrayList<>(query(bean).getContent());
        ScExport.export(bean.getCfg(), dtos, EngineeringMembersProjectRelateRespDTO.class);
    }

    /**
     * 通过工程ID和Type查询成员
     */
    @Override
    public List<EngineeringMembersProjectRelateRespDTO> queryByProjectIdInAndType(List<String> projectIds,
            Integer type) {
        List<EngineeringMembersProjectRelate> relates = engineeringMembersProjectRelateRepository
                .findByProjectIdIn(projectIds);

        List<String> memberIds = relates.stream().map(r -> r.getMemberId()).distinct().collect(Collectors.toList());

        List<EngineeringMembers> members = engineeringMembersRepository.findByIdInAndType(memberIds, type);

        List<EngineeringMembersProjectRelateRespDTO> res = BeanUtil.toBeanList(relates,
                EngineeringMembersProjectRelateRespDTO.class);

        res.forEach(r -> {
            if (CollectionUtils.isNotEmpty(members)) {
                EngineeringMembers member = members.stream().filter(m -> m.getId().equals(r.getMemberId())).findFirst()
                        .orElse(null);
                if (Objects.nonNull(member)) {
                    r.setMember(member);
                }
            }
        });

        return res;
    }
}