package cn.shencom.server.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.shencom.enums.BusinessRoleEnum;
import cn.shencom.enums.OrganizationMemberTypeEnum;
import cn.shencom.model.*;
import cn.shencom.model.dto.MemberRelateStatics;
import cn.shencom.model.dto.SimpleRegionDTO;
import cn.shencom.model.dto.create.FnRmsv3MembersTypeRelateCreateDTO;
import cn.shencom.model.dto.query.FnRmsv3MembersTypeRelateQueryDTO;
import cn.shencom.model.dto.resp.FnRmsv3MembersTypeRelateRespDTO;
import cn.shencom.model.dto.resp.XsgcOrganizationRespDTO;
import cn.shencom.model.dto.update.FnRmsv3MembersTypeRelateUpdateDTO;
import cn.shencom.model.dto.update.SysRoleUserOrganizationUpdateDTO;
import cn.shencom.repos.*;
import cn.shencom.scloud.common.base.constant.RespCode;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.jpa.scpage.MyLink;
import cn.shencom.scloud.common.jpa.util.export.ScExport;
import cn.shencom.scloud.common.jpa.util.query.LinkUtil;
import cn.shencom.scloud.common.jpa.util.query.ScQueryUtil;
import cn.shencom.scloud.common.server.service.BaseImpl;
import cn.shencom.scloud.common.util.BeanUtil;
import cn.shencom.scloud.security.core.ScContext;
import cn.shencom.server.service.IFnRmsv3MembersTypeRelateService;
import cn.shencom.server.service.ISysRoleUserOrganizationService;
import cn.shencom.utils.XsgcContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import java.util.*;


/**
 * 小散工程-组织团队成员关系 的服务实现
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Service
@Slf4j
public class FnRmsv3MembersTypeRelateServiceImpl extends BaseImpl implements IFnRmsv3MembersTypeRelateService {

    @Autowired
    private FnRmsv3MembersTypeRelateRepository fnRmsv3MembersTypeRelateRepository;

    @Autowired
    private SysRoleUserOrganizationRepository sysRoleUserOrganizationRepository;

    @Autowired
    private XsgcCustomerInfoRepository xsgcCustomerInfoRepository;

    @Autowired
    private FnRmsv3MembersRepository fnRmsv3MembersRepository;

    @Autowired
    private FnRmsv3MembersTypeRelateBindingRepository fnRmsv3MembersTypeRelateBindingRepository;

    @Autowired
    private ISysRoleUserOrganizationService sysRoleUserOrganizationService;

    @Autowired
    private  ComRegionRepository  comRegionRepository;




    @Autowired
    private SysRolesRepository sysRolesRepository;

    @Override
    public Page<FnRmsv3MembersTypeRelateRespDTO> query(FnRmsv3MembersTypeRelateQueryDTO bean) {

        String organizationId = XsgcContext.getOrganizationId();
        if (organizationId==null){
            throw new ScException("组织id不能为空！");
        }

        String typeId= bean.getTypeId();

        ScQueryUtil.getValuesRmAndSetBean(bean.getQuery(), Arrays.asList("regionPid", "regionId", "regionCid"), bean);

        Map<String, MyLink> linkMap = LinkUtil.convertLink(FnRmsv3MembersTypeRelate.class);
        Page<FnRmsv3MembersTypeRelate> res = fnRmsv3MembersTypeRelateRepository.findAll((root, query, builder) -> {
            //用于拼接条件
            List<Predicate> ps = new ArrayList<>();
            if (typeId!=null){
                ps.add(builder.equal(root.get("typeId"),typeId));
            }

            String regionPid = bean.getRegionPid();
            String regionId = bean.getRegionId();
            String regionCid = bean.getRegionCid();


            Join<Object, Object> join = root.join("fnRmsv3Members", JoinType.INNER);
            ps.add(builder.equal(join.get("organizationId"),organizationId));

            //区域筛选
            boolean queryRegion=StrUtil.isNotBlank(regionPid)||StrUtil.isNotBlank(regionId)||StrUtil.isNotBlank(regionCid);
            if (queryRegion) {
                Join<Object, Object> bindingList = root.join("fnRmsv3MembersTypeRelateBindings", JoinType.INNER);

                if (StrUtil.isNotBlank(regionPid)) {
                    ps.add(builder.equal(bindingList.get("regionPid"), regionPid));
                }
                if (StrUtil.isNotBlank(regionId)) {
                    ps.add(builder.equal(bindingList.get("regionId"), regionId));
                }
                if (StrUtil.isNotBlank(regionCid)) {
                    ps.add(builder.equal(bindingList.get("regionCid"), regionCid));
                }
            }

            return ScQueryUtil.getPredicate(ps, bean, linkMap, root, query, builder);
        }, PageRequest.of(bean.getPage(), bean.getSize()));


        Page<FnRmsv3MembersTypeRelateRespDTO> page = ScQueryUtil.handle(res, linkMap, FnRmsv3MembersTypeRelateRespDTO.class);

        for (FnRmsv3MembersTypeRelateRespDTO respDTO : page.getContent()) {
            List<FnRmsv3MembersTypeRelateBinding> bindings = fnRmsv3MembersTypeRelateBindingRepository.findByRelateId(respDTO.getId());
            if (CollectionUtils.isNotEmpty(bindings)) {
                StringBuilder regionNames = new StringBuilder();
                for (FnRmsv3MembersTypeRelateBinding binding : bindings) {
                    String regionPid = binding.getRegionPid();
                    String regionId = binding.getRegionId();
                    String regionCid = binding.getRegionCid();
                    if (StringUtils.isNotBlank(regionPid)){
                        Optional<ComRegion> regionPidName = comRegionRepository.findById(regionPid);
                        if (regionPidName.isPresent()) {
                            regionNames.append(regionPidName.get().getTitle());
                        }
                    }
                    if (StringUtils.isNotBlank(regionId)){
                        Optional<ComRegion> streetName = comRegionRepository.findById(regionId);
                        if (streetName.isPresent()) {
                            regionNames.append(streetName.get().getTitle());
                        }
                    }
                    if (StringUtils.isNotBlank(regionCid)){
                        Optional<ComRegion> communityName = comRegionRepository.findById(regionCid);
                        if (communityName.isPresent()) {
                            regionNames.append(communityName.get().getTitle());
                        }
                    }


                    regionNames.append(",");
                }
                if (StringUtils.isNotBlank(regionNames)) {
                    String names = String.valueOf(regionNames);
                    String substring = names.substring(0, names.length() - 1);
                    respDTO.setRegionNames(substring);
                }

            }
        }

        return page;
    }

    @Override
    public FnRmsv3MembersTypeRelateRespDTO show(ScShowDTO bean) {
        Optional<FnRmsv3MembersTypeRelate> option = fnRmsv3MembersTypeRelateRepository.findById(bean.getId());
        if (!option.isPresent()) {
            return null;
        }
        FnRmsv3MembersTypeRelate entity = option.get();
        Map<String, MyLink> linkMap = LinkUtil.convertLink(FnRmsv3MembersTypeRelate.class);
        FnRmsv3MembersTypeRelateRespDTO respDTO = ScQueryUtil.handleOne(entity, linkMap, FnRmsv3MembersTypeRelateRespDTO.class);

        //查询绑定的区域信息
        List<FnRmsv3MembersTypeRelateBinding> bindings = fnRmsv3MembersTypeRelateBindingRepository.findByRelateId(entity.getId());
        respDTO.setRegionIds(bindings);

        return respDTO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public FnRmsv3MembersTypeRelate create(FnRmsv3MembersTypeRelateCreateDTO bean) {

        //获取当前组织id
        String organizationId = XsgcContext.getOrganizationId();
        if (organizationId==null){
            throw new ScException("组织id不能为空！");
        }


        //查询成员
        FnRmsv3Members members = fnRmsv3MembersRepository.findById(bean.getMemberId()).orElseThrow(() -> new ScException("当前成员不存在！"));
        //查询客户信息
        XsgcCustomerInfo customerInfo = xsgcCustomerInfoRepository.findFirstByOrganizationId(members.getOrganizationId());
        if (customerInfo==null){
            throw new ScException("当前客户不存在！");
        }
        //订阅的套餐信息
        XsgcSubscription xsgcSubscription = customerInfo.getXsgcSubscription();
        if (xsgcSubscription==null){
            throw new ScException("当前客户未开通服务！");
        }

        List<SimpleRegionDTO> regionIds = bean.getRegionIds();

        //校验当前用户是否已经创建角色
        if (fnRmsv3MembersTypeRelateRepository.existsByMemberIdAndTypeId(bean.getMemberId(),bean.getTypeId())){
            throw new ScException("该团队成员角色已存在，请勿重复添加");
        }

        //如果当前添加的是 市级/区级/街道级/社区级/巡查人员 ，校验当前用户只能是其中一种角色
        List<String> typeIds = Arrays.asList("2", "3", "4", "5", "6");
        if (typeIds.contains(bean.getTypeId())){
            FnRmsv3MembersTypeRelate typeRelate = fnRmsv3MembersTypeRelateRepository.findFirstByMemberIdAndTypeIdIn(bean.getMemberId(), typeIds);
            if (typeRelate!=null){
                throw new ScException(String.format("您已经是%s,无法再成为%s",
                        OrganizationMemberTypeEnum.getMsgByCode(Integer.valueOf(typeRelate.getTypeId())),
                        OrganizationMemberTypeEnum.getMsgByCode(Integer.valueOf(bean.getTypeId()))
                       ));
            }
        }

        //为当前用户添加权限
        SysRoleUserOrganizationUpdateDTO roleUpdateDTO = new SysRoleUserOrganizationUpdateDTO();
        roleUpdateDTO.setOrganizationId(organizationId);
        if (xsgcSubscription.getType()==1){
            //高级套餐
            roleUpdateDTO.setRoleNames(Arrays.asList(BusinessRoleEnum.getRoleByType(bean.getTypeId(),true)));
        }else {
            roleUpdateDTO.setRoleNames(Arrays.asList(BusinessRoleEnum.getRoleByType(bean.getTypeId(),false)));
        }

        roleUpdateDTO.setUserId(members.getUserId());
        sysRoleUserOrganizationService.addPermissionForUser(roleUpdateDTO);

        FnRmsv3MembersTypeRelate entity = new FnRmsv3MembersTypeRelate();
        BeanUtil.copyProperties(bean, entity);
        FnRmsv3MembersTypeRelate save = fnRmsv3MembersTypeRelateRepository.save(entity);


        String relateId = save.getId();
        //添加新的绑定关系
        for (SimpleRegionDTO place : regionIds) {
            FnRmsv3MembersTypeRelateBinding binding = new FnRmsv3MembersTypeRelateBinding();
            if (StringUtils.isNotBlank(relateId)){
                binding.setRelateId(relateId);
                binding.setRegionPid(place.getRegionPid());
                binding.setRegionId(place.getRegionId());
                binding.setRegionCid(place.getRegionCid());
                fnRmsv3MembersTypeRelateBindingRepository.save(binding);
            }
        }

        return save;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public FnRmsv3MembersTypeRelate update(FnRmsv3MembersTypeRelateUpdateDTO bean) {

        //获取当前组织id
        String organizationId = XsgcContext.getOrganizationId();
        if (organizationId==null){
            throw new ScException("组织id不能为空！");
        }

        FnRmsv3MembersTypeRelate entity = fnRmsv3MembersTypeRelateRepository.findById(bean.getId())
                .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));

        List<SimpleRegionDTO> regionIds = bean.getRegionIds();

        //可能需要去掉
        if (CollectionUtils.isEmpty(regionIds)){
            throw new ScException("请先选择区域");
        }

        String relateId = entity.getId();

        BeanUtil.copyProperties(bean,entity);
        entity.setUpdatedAt(new Date());

        //删除旧的绑定关系
        fnRmsv3MembersTypeRelateBindingRepository.deleteByRelateId(relateId);

        //添加新的绑定关系
        for (SimpleRegionDTO place : regionIds) {
            FnRmsv3MembersTypeRelateBinding binding = new FnRmsv3MembersTypeRelateBinding();
            if (StringUtils.isNotBlank(relateId)){
                binding.setRelateId(relateId);
                binding.setRegionPid(place.getRegionPid());
                binding.setRegionId(place.getRegionId());
                binding.setRegionCid(place.getRegionCid());
                fnRmsv3MembersTypeRelateBindingRepository.save(binding);
            }
        }

        return fnRmsv3MembersTypeRelateRepository.save(entity);
    }


    @Override
    @Transactional
    public FnRmsv3MembersTypeRelate updateStatus(FnRmsv3MembersTypeRelateUpdateDTO bean) {

        FnRmsv3MembersTypeRelate entity = fnRmsv3MembersTypeRelateRepository.findById(bean.getId())
                .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));

        Integer active = bean.getActive();
        if (active!=null&&!active.equals(entity.getActive())){
            //修改状态
            entity.setActive(active);


            FnRmsv3Members member = entity.getFnRmsv3Members();
            if (member==null){
                throw new ScException("当前用户不存在！");
            }

            //查询组织
            XsgcCustomerInfo customerInfo = xsgcCustomerInfoRepository.findFirstByOrganizationId(member.getOrganizationId());
            //查询套餐
            XsgcSubscription subscription = customerInfo.getXsgcSubscription();

            SysRoleUserOrganizationUpdateDTO updateDTO = new SysRoleUserOrganizationUpdateDTO();
            updateDTO.setOrganizationId(member.getOrganizationId());
            updateDTO.setUserId(member.getUserId());
            updateDTO.setRoleNames(Arrays.asList(BusinessRoleEnum.getRoleByType(entity.getTypeId(),subscription.getType()==1)));
            //需要为用户移除/添加权限
            if (active==1){
                sysRoleUserOrganizationService.addPermissionForUser(updateDTO);
            }else {
                sysRoleUserOrganizationService.removePermissionForUser(updateDTO);
            }
        }
        return fnRmsv3MembersTypeRelateRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            ids.forEach(id -> fnRmsv3MembersTypeRelateRepository.findById(id).ifPresent(entity -> {
                entity.setIsDeleted(1);

                //查询成员
                FnRmsv3Members member = fnRmsv3MembersRepository.findById(entity.getMemberId()).get();

                //删除当前用户权限
                SysRoleUserOrganizationUpdateDTO updateDTO=new SysRoleUserOrganizationUpdateDTO();
                updateDTO.setUserId(member.getUserId());
                updateDTO.setOrganizationId(member.getOrganizationId());
                updateDTO.setRoleNames(BusinessRoleEnum.getRoleByType(entity.getTypeId()));
                sysRoleUserOrganizationService.removePermissionForUser(updateDTO);

                fnRmsv3MembersTypeRelateRepository.save(entity);
            }));
        }
    }

    @Override
    public void export(FnRmsv3MembersTypeRelateQueryDTO bean) {
        List<FnRmsv3MembersTypeRelateRespDTO> dtos = new ArrayList<>(query(bean).getContent());
        ScExport.export(bean.getCfg(), dtos, FnRmsv3MembersTypeRelateRespDTO.class);
    }


    @Override
    @Transactional
    public void deleteMember(FnRmsv3Members member) {
        String memberId = member.getId();
        List<FnRmsv3MembersTypeRelate> membersTypeRelateList = fnRmsv3MembersTypeRelateRepository.findByMemberId(memberId);
        membersTypeRelateList.forEach( m -> m.setIsDeleted(1));
        fnRmsv3MembersTypeRelateRepository.saveAll(membersTypeRelateList);


        SysRoleUserOrganizationUpdateDTO updateDTO=new SysRoleUserOrganizationUpdateDTO();
        updateDTO.setUserId(member.getUserId());
        updateDTO.setOrganizationId(member.getOrganizationId());
        updateDTO.setRoleNames( BusinessRoleEnum.getAllRoleName());
        sysRoleUserOrganizationService.removePermissionForUser(updateDTO);

    }


    @Override
    @Transactional
    public void createMore(FnRmsv3MembersTypeRelateCreateDTO bean) {

        String organizationId = XsgcContext.getOrganizationId();
        assert organizationId!=null;
        bean.setOrganizationId(organizationId);


        Set<String> userIds = new HashSet<>();
        if (StrUtil.isNotBlank(bean.getMemberId())) {
            userIds.add(bean.getMemberId());
        }
        if (CollUtil.isNotEmpty(bean.getMemberIds())) {
            userIds.addAll(bean.getMemberIds());
        }
        for (String userId : userIds) {
            bean.setMemberId(userId);
            create(bean);
        }
    }


    @Override
    public List<XsgcOrganizationRespDTO> allOrganization() {
        String userId = ScContext.getCurrentUserThrow().getId();
        return allOrganization(userId);
    }



    @Override
    public List<XsgcOrganizationRespDTO> allOrganization(String userId) {
        return fnRmsv3MembersTypeRelateRepository.getAllOrganizationByUserId(userId);
    }

    @Override
    public List<XsgcOrganizationRespDTO> getMemberByOrganizationIdAndUserId(String userId, String organizationId) {
        return fnRmsv3MembersTypeRelateRepository.getAllOrganizationByUserId(userId, organizationId);
    }

    @Override
    public MemberRelateStatics statics() {

        String organizationId = XsgcContext.getOrganizationId();
        assert organizationId!=null;
        return fnRmsv3MembersTypeRelateRepository.getStatics(organizationId);
    }

    @Override
    public FnRmsv3MembersTypeRelateRespDTO getMemberRelateByUserAndOrganization() {
        String userId = ScContext.getCurrentUserThrow().getId();
        String organizationId = XsgcContext.getOrganizationId();
        if (organizationId==null){
            return null;
        }
        return getMemberRelateByUserAndOrganization(userId,organizationId);
    }

    @Override
    public FnRmsv3MembersTypeRelateRespDTO getMemberRelateByUserAndOrganization(String userId, String organizationId) {

        //如果有组织id就查询组织成员
        FnRmsv3Members members = fnRmsv3MembersRepository.findByUserIdAndOrganizationId(userId, organizationId);
        if (members==null){
            return null;
        }
        FnRmsv3MembersTypeRelate relate = fnRmsv3MembersTypeRelateRepository.findFirstByMemberIdAndTypeIdIn(members.getId(), OrganizationMemberTypeEnum.ORDINARY_MEMBER_TYPE);
        if (relate==null){
            return null;
        }
        //查询当前角色绑定的区域
        FnRmsv3MembersTypeRelateRespDTO respDTO = new FnRmsv3MembersTypeRelateRespDTO();
        BeanUtil.copyProperties(relate,respDTO);

        List<FnRmsv3MembersTypeRelateBinding> bindings = fnRmsv3MembersTypeRelateBindingRepository.findByRelateId(relate.getId());
        respDTO.setRegionIds(bindings);

        return respDTO;
    }
}