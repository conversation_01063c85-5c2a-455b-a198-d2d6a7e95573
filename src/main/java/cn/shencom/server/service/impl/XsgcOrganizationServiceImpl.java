package cn.shencom.server.service.impl;

import cn.shencom.model.XsgcOrganization;
import cn.shencom.model.dto.create.XsgcOrganizationCreateDTO;
import cn.shencom.model.dto.query.XsgcOrganizationQueryDTO;
import cn.shencom.model.dto.resp.XsgcOrganizationRespDTO;
import cn.shencom.model.dto.update.XsgcOrganizationUpdateDTO;
import cn.shencom.repos.XsgcOrganizationRepository;
import cn.shencom.scloud.common.base.constant.RespCode;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.jpa.scpage.MyLink;
import cn.shencom.scloud.common.jpa.util.export.ScExport;
import cn.shencom.scloud.common.jpa.util.query.LinkUtil;
import cn.shencom.scloud.common.jpa.util.query.ScQueryUtil;
import cn.shencom.scloud.common.server.service.BaseImpl;
import cn.shencom.scloud.common.util.BeanUtil;
import cn.shencom.server.service.IXsgcOrganizationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.util.*;


/**
 * 小散工程-组织 的服务实现
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Service
@Slf4j
public class XsgcOrganizationServiceImpl extends BaseImpl implements IXsgcOrganizationService {

    @Autowired
    private XsgcOrganizationRepository xsgcOrganizationRepository;

    @Override
    public Page<XsgcOrganizationRespDTO> query(XsgcOrganizationQueryDTO bean) {
        Map<String, MyLink> linkMap = LinkUtil.convertLink(XsgcOrganization.class);
        Page<XsgcOrganization> res = xsgcOrganizationRepository.findAll((root, query, builder) -> {
            //用于拼接条件
            List<Predicate> ps = new ArrayList<>();

            return ScQueryUtil.getPredicate(ps, bean, linkMap, root, query, builder);
        }, PageRequest.of(bean.getPage(), bean.getSize()));
        return ScQueryUtil.handle(res, linkMap, XsgcOrganizationRespDTO.class);
    }

    @Override
    public XsgcOrganizationRespDTO show(ScShowDTO bean) {
        Optional<XsgcOrganization> option = xsgcOrganizationRepository.findById(bean.getId());
        if (!option.isPresent()) {
            return null;
        }
        XsgcOrganization entity = option.get();
        Map<String, MyLink> linkMap = LinkUtil.convertLink(XsgcOrganization.class);
        return ScQueryUtil.handleOne(entity, linkMap, XsgcOrganizationRespDTO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public XsgcOrganization create(XsgcOrganizationCreateDTO bean) {
        XsgcOrganization entity = new XsgcOrganization();
        BeanUtil.copyProperties(bean, entity);
        return xsgcOrganizationRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public XsgcOrganization update(XsgcOrganizationUpdateDTO bean) {
        XsgcOrganization entity = xsgcOrganizationRepository.findById(bean.getId())
                .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));
        BeanUtil.copyProperties(bean, entity);
        return xsgcOrganizationRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            ids.forEach(id -> xsgcOrganizationRepository.findById(id).ifPresent(entity -> {
                entity.setIsDeleted(1);
                entity.setDeletedAt(new Date());
                xsgcOrganizationRepository.save(entity);
            }));
        }
    }

    @Override
    public void export(XsgcOrganizationQueryDTO bean) {
        List<XsgcOrganizationRespDTO> dtos = new ArrayList<>(query(bean).getContent());
        ScExport.export(bean.getCfg(), dtos, XsgcOrganizationRespDTO.class);
    }
}