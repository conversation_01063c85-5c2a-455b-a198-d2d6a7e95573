package cn.shencom.server.service.impl;

import cn.shencom.model.EventCameraPointDevice;
import cn.shencom.model.MonitorAccessInfo;
import cn.shencom.model.dto.create.MonitorAccessInfoCreateDTO;
import cn.shencom.model.dto.query.MonitorAccessInfoQueryDTO;
import cn.shencom.model.dto.resp.MonitorAccessInfoRespDTO;
import cn.shencom.model.dto.update.MonitorAccessInfoUpdateDTO;
import cn.shencom.repos.EventCameraPointDeviceRepository;
import cn.shencom.repos.MonitorAccessInfoRepository;
import cn.shencom.scloud.common.base.constant.RespCode;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.jpa.scpage.MyLink;
import cn.shencom.scloud.common.jpa.util.export.ScExport;
import cn.shencom.scloud.common.jpa.util.query.LinkUtil;
import cn.shencom.scloud.common.jpa.util.query.ScQueryUtil;
import cn.shencom.scloud.common.server.service.BaseImpl;
import cn.shencom.scloud.common.util.BeanUtil;
import cn.shencom.server.service.IMonitorAccessInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.util.*;


/**
 * 小散工程-监管工单流程-监控接入详情 的服务实现
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@Service
@Slf4j
public class MonitorAccessInfoServiceImpl extends BaseImpl implements IMonitorAccessInfoService {

    @Autowired
    private MonitorAccessInfoRepository monitorAccessInfoRepository;


    @Autowired
    private EventCameraPointDeviceRepository eventCameraPointDeviceRepository;

    @Override
    public Page<MonitorAccessInfoRespDTO> query(MonitorAccessInfoQueryDTO bean) {
        Map<String, MyLink> linkMap = LinkUtil.convertLink(MonitorAccessInfo.class);
        Page<MonitorAccessInfo> res = monitorAccessInfoRepository.findAll((root, query, builder) -> {
            //用于拼接条件
            List<Predicate> ps = new ArrayList<>();

            return ScQueryUtil.getPredicate(ps, bean, linkMap, root, query, builder);
        }, PageRequest.of(bean.getPage(), bean.getSize()));
        return ScQueryUtil.handle(res, linkMap, MonitorAccessInfoRespDTO.class);
    }

    @Override
    public MonitorAccessInfoRespDTO show(ScShowDTO bean) {
        Optional<MonitorAccessInfo> option = monitorAccessInfoRepository.findById(bean.getId());
        if (!option.isPresent()) {
            return null;
        }
        MonitorAccessInfo entity = option.get();
        Map<String, MyLink> linkMap = LinkUtil.convertLink(MonitorAccessInfo.class);
        return ScQueryUtil.handleOne(entity, linkMap, MonitorAccessInfoRespDTO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public MonitorAccessInfo create(MonitorAccessInfoCreateDTO bean) {
        MonitorAccessInfo entity = new MonitorAccessInfo();
        BeanUtil.copyProperties(bean, entity);
        return monitorAccessInfoRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public MonitorAccessInfo update(MonitorAccessInfoUpdateDTO bean) {
        MonitorAccessInfo entity = monitorAccessInfoRepository.findById(bean.getId())
                .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));
        BeanUtil.copyProperties(bean, entity);
        return monitorAccessInfoRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            ids.forEach(id -> monitorAccessInfoRepository.deleteById(id));
        }
    }

    @Override
    public void export(MonitorAccessInfoQueryDTO bean) {
        List<MonitorAccessInfoRespDTO> dtos = new ArrayList<>(query(bean).getContent());
        ScExport.export(bean.getCfg(), dtos, MonitorAccessInfoRespDTO.class);
    }


    @Override
    @Transactional
    public void createOrUpdateInfo(String projectId,String monitorOrderId) {
        //删除旧的
        monitorAccessInfoRepository.deleteByOrderId(monitorOrderId);

        //查询当前项目绑定的设备信息
        List<EventCameraPointDevice> deviceList = eventCameraPointDeviceRepository.findByProjectId(projectId);

        if (deviceList.isEmpty()){
            return;
        }

        List<MonitorAccessInfo> monitorAccessInfosNew = new ArrayList<>();
        for (EventCameraPointDevice device : deviceList) {
            MonitorAccessInfo info = new MonitorAccessInfo();
            info.setDeviceId(device.getId());
            info.setOrderId(monitorOrderId);
            info.setSerialNo(device.getSerialNo());
            monitorAccessInfosNew.add(info);
        }
        if (!monitorAccessInfosNew.isEmpty()){
            monitorAccessInfoRepository.saveAll(monitorAccessInfosNew);
        }
    }
}