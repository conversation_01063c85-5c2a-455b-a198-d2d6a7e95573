package cn.shencom.server.service.impl;

import cn.shencom.model.XsgcBusinessMembers;
import cn.shencom.model.dto.create.XsgcBusinessMembersCreateDTO;
import cn.shencom.model.dto.query.XsgcBusinessMembersQueryDTO;
import cn.shencom.model.dto.resp.XsgcBusinessMembersRespDTO;
import cn.shencom.model.dto.update.XsgcBusinessMembersUpdateDTO;
import cn.shencom.repos.XsgcBusinessMembersRelateRepository;
import cn.shencom.repos.XsgcBusinessMembersRepository;
import cn.shencom.scloud.common.base.constant.RespCode;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.jpa.scpage.MyLink;
import cn.shencom.scloud.common.jpa.util.export.ScExport;
import cn.shencom.scloud.common.jpa.util.query.LinkUtil;
import cn.shencom.scloud.common.jpa.util.query.ScQueryUtil;
import cn.shencom.scloud.common.server.service.BaseImpl;
import cn.shencom.scloud.common.util.BeanUtil;
import cn.shencom.scloud.scloudapiuaa.dto.SysUsersDTO;
import cn.shencom.server.manager.UaaManager;
import cn.shencom.server.service.ISysRolesService;
import cn.shencom.server.service.IXsgcBusinessMembersService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.util.*;


/**
 * 小散工程-业务人员 的服务实现
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Service
@Slf4j
public class XsgcBusinessMembersServiceImpl extends BaseImpl implements IXsgcBusinessMembersService {

    @Autowired
    private XsgcBusinessMembersRepository xsgcBusinessMembersRepository;

    @Autowired
    private XsgcBusinessMembersRelateRepository xsgcBusinessMembersRelateRepository;

    @Autowired
    private UaaManager uaaManager;

    @Autowired
    private ISysRolesService sysRolesService;


    public static final Map<Integer,String> ROLE_MAP;
    static {
        ROLE_MAP = new HashMap<>();
        //技术人员
        ROLE_MAP.put(1,"technical:director");
        //商务人员
        ROLE_MAP.put(2,"commercial:affairs");
        //销售人员
        ROLE_MAP.put(3,"Head:Sales");
        //安装人员
        ROLE_MAP.put(4,"Installation:Manager");
    }


    @Override
    public Page<XsgcBusinessMembersRespDTO> query(XsgcBusinessMembersQueryDTO bean) {
        Map<String, MyLink> linkMap = LinkUtil.convertLink(XsgcBusinessMembers.class);
        Page<XsgcBusinessMembers> res = xsgcBusinessMembersRepository.findAll((root, query, builder) -> {
            //用于拼接条件
            List<Predicate> ps = new ArrayList<>();

            return ScQueryUtil.getPredicate(ps, bean, linkMap, root, query, builder);
        }, PageRequest.of(bean.getPage(), bean.getSize()));
        return ScQueryUtil.handle(res, linkMap, XsgcBusinessMembersRespDTO.class);
    }

    @Override
    public XsgcBusinessMembersRespDTO show(ScShowDTO bean) {
        Optional<XsgcBusinessMembers> option = xsgcBusinessMembersRepository.findById(bean.getId());
        if (!option.isPresent()) {
            return null;
        }
        XsgcBusinessMembers entity = option.get();
        Map<String, MyLink> linkMap = LinkUtil.convertLink(XsgcBusinessMembers.class);
        return ScQueryUtil.handleOne(entity, linkMap, XsgcBusinessMembersRespDTO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public XsgcBusinessMembers create(XsgcBusinessMembersCreateDTO bean) {

        String mobile = bean.getMobile();
        //查询手机号是否存在
        XsgcBusinessMembers byMemberMobile = xsgcBusinessMembersRepository.findFirstByMobile(mobile);
        if (byMemberMobile!=null){
            throw new ScException("当前手机号已经绑定业务员！");
        }

        //根据手机号查询用户信息
        SysUsersDTO sysUsersDTO = uaaManager.findByPhone(mobile);
        if (sysUsersDTO!=null){
            //校验当前系统用户是否已经绑定业务成员
            XsgcBusinessMembers firstMemberByUserId = xsgcBusinessMembersRepository.findFirstByUserId(sysUsersDTO.getId());
            if (firstMemberByUserId!=null){
                throw new ScException("当前系统用户已经绑定业务员！业务员手机号："+ firstMemberByUserId.getMobile());
            }
        }else {

            SysUsersDTO dto =new SysUsersDTO();
            dto.setIsLock(1);
            dto.setRealname(bean.getRealname());
            dto.setPhone(mobile);
            sysUsersDTO = uaaManager.userInit(dto);
        }

        String userId = sysUsersDTO.getId();

        XsgcBusinessMembers entity = new XsgcBusinessMembers();
        BeanUtil.copyProperties(bean, entity);

        entity.setUserId(userId);

        String roleName = ROLE_MAP.get(entity.getType());
        assert roleName != null;

        //为用户添加角色
        sysRolesService.addRoleForUser(roleName, userId);

        return xsgcBusinessMembersRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public XsgcBusinessMembers update(XsgcBusinessMembersUpdateDTO bean) {
        XsgcBusinessMembers entity = xsgcBusinessMembersRepository.findById(bean.getId())
                .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));


        //校验修改后的手机号码是否重复
        String newMobile = bean.getMobile();
        if (!entity.getMobile().equals(newMobile)){
            //查询手机号是否存在
            XsgcBusinessMembers byMemberMobile = xsgcBusinessMembersRepository.findFirstByMobile(newMobile);
            if (byMemberMobile!=null){
                throw new ScException("当前手机号已经绑定业务员！");
            }
        }


        String userId = entity.getUserId();
        if (bean.getType()!=null&&!bean.getType().equals(entity.getType())){
            //如果要修改业务人员的职位，需要进行校验
            if ( checkIfHasReference(entity.getId())){
                throw new ScException(String.format("业务人员【%s】有关联的客户，无法修改职位！", entity.getRealname()));
            }
            //删除旧角色，添加新角色
            String oldRole =  ROLE_MAP.get(entity.getType());
            String newRole = ROLE_MAP.get(bean.getType());
            assert oldRole!=null && newRole!=null;
            sysRolesService.addRoleForUser(newRole, userId);
            sysRolesService.deleteRoleForUser(oldRole, userId);
        }

        //更新有效状态
        if (bean.getStatus()!=null&&!bean.getStatus().equals(entity.getStatus())){
            //删除旧角色，添加新角色
            String oldRole =  ROLE_MAP.get(entity.getType());
            String newRole = ROLE_MAP.get(bean.getType());
            assert oldRole!=null && newRole!=null;
            sysRolesService.addRoleForUser(newRole, userId);
            sysRolesService.deleteRoleForUser(oldRole, userId);
        }

        BeanUtil.copyProperties(bean, entity);
        return xsgcBusinessMembersRepository.save(entity);
    }


    private SysUsersDTO checkMobileAndInitUser(String mobile,String realname){
        //查询手机号是否存在
        XsgcBusinessMembers byMemberMobile = xsgcBusinessMembersRepository.findFirstByMobile(mobile);
        if (byMemberMobile!=null){
            throw new ScException("当前手机号已经绑定业务员！");
        }

        //根据手机号查询用户信息
        SysUsersDTO sysUsersDTO = uaaManager.findByPhone(mobile);
        if (sysUsersDTO!=null){
            //校验当前系统用户是否已经绑定业务成员
            XsgcBusinessMembers firstMemberByUserId = xsgcBusinessMembersRepository.findFirstByUserId(sysUsersDTO.getId());
            if (firstMemberByUserId!=null){
                throw new ScException("当前系统用户已经绑定业务员！业务员手机号："+ firstMemberByUserId.getMobile());
            }
        }else {

            SysUsersDTO dto =new SysUsersDTO();
            dto.setIsLock(1);
            dto.setRealname(realname);
            dto.setPhone(mobile);
            sysUsersDTO = uaaManager.userInit(dto);
        }

        return sysUsersDTO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            ids.forEach(id -> xsgcBusinessMembersRepository.findById(id).ifPresent(entity -> {
                entity.setIsDeleted(1);
                entity.setDeletedAt(new Date());

                if ( checkIfHasReference(entity.getId())){
                    throw new ScException(String.format("业务人员【%s】有关联的客户，无法删除！", entity.getRealname()));
                }

                String roleName = ROLE_MAP.get(entity.getType());
                assert roleName != null;
                //为用户清除角色
                sysRolesService.deleteRoleForUser(roleName, entity.getUserId());

                xsgcBusinessMembersRepository.save(entity);
            }));
        }
    }


    /**
     * 查询是否有关联的客户
     * @return 如果有关联的客户，返回true
     */
    private boolean checkIfHasReference(String memberId){
        //查询是否有关联的客户
        return xsgcBusinessMembersRelateRepository.existsByMemberId(memberId);
    }


    @Override
    public void export(XsgcBusinessMembersQueryDTO bean) {
        List<XsgcBusinessMembersRespDTO> dtos = new ArrayList<>(query(bean).getContent());
        ScExport.export(bean.getCfg(), dtos, XsgcBusinessMembersRespDTO.class);
    }
}