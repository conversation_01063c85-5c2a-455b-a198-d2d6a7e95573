package cn.shencom.server.service.impl;

import cn.shencom.model.AiotCategory;
import cn.shencom.model.dto.AiotEventPushDTO;
import cn.shencom.model.dto.create.AiotCategoryCreateDTO;
import cn.shencom.model.dto.query.AiotCategoryQueryDTO;
import cn.shencom.model.dto.resp.AiotCategoryRespDTO;
import cn.shencom.model.dto.update.AiotCategoryUpdateDTO;
import cn.shencom.repos.AiotCategoryRepository;
import cn.shencom.scloud.common.base.constant.RespCode;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.jpa.scpage.MyLink;
import cn.shencom.scloud.common.jpa.util.export.ScExport;
import cn.shencom.scloud.common.jpa.util.query.LinkUtil;
import cn.shencom.scloud.common.jpa.util.query.ScQueryUtil;
import cn.shencom.scloud.common.server.service.BaseImpl;
import cn.shencom.scloud.common.util.BeanUtil;
import cn.shencom.server.service.IAiotCategoryService;
import cn.shencom.server.service.IAiotSceneCategoryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.util.*;


/**
 * aiot场景分类 的服务实现
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Service
@Slf4j
public class AiotCategoryServiceImpl extends BaseImpl implements IAiotCategoryService {

    @Autowired
    private AiotCategoryRepository aiotCategoryRepository;

    @Resource
    private IAiotSceneCategoryService aiotSceneCategoryService;

    @Override
    public Page<AiotCategoryRespDTO> query(AiotCategoryQueryDTO bean) {
        Integer level = bean.getLevel();
        Map<String, MyLink> linkMap = LinkUtil.convertLink(AiotCategory.class);
        Page<AiotCategory> res = aiotCategoryRepository.findAll((root, query, builder) -> {
            //用于拼接条件
            List<Predicate> ps = new ArrayList<>();
            if(Objects.nonNull(level)){
                ps.add(builder.equal(root.get("level"), level));
            }
            return ScQueryUtil.getPredicate(ps, bean, linkMap, root, query, builder);
        }, PageRequest.of(bean.getPage(), bean.getSize()));
        return ScQueryUtil.handle(res, linkMap, AiotCategoryRespDTO.class);
    }

    @Override
    public AiotCategoryRespDTO show(ScShowDTO bean) {
        Optional<AiotCategory> option = aiotCategoryRepository.findById(bean.getId());
        if (!option.isPresent()) {
            return null;
        }
        AiotCategory entity = option.get();
        Map<String, MyLink> linkMap = LinkUtil.convertLink(AiotCategory.class);
        return ScQueryUtil.handleOne(entity, linkMap, AiotCategoryRespDTO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public AiotCategory create(AiotCategoryCreateDTO bean) {
        AiotCategory entity = new AiotCategory();
        BeanUtil.copyProperties(bean, entity);
        return aiotCategoryRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public AiotCategory update(AiotCategoryUpdateDTO bean) {
        AiotCategory entity = aiotCategoryRepository.findById(bean.getId())
                .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));
        BeanUtil.copyProperties(bean, entity);
        return aiotCategoryRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            ids.forEach(id -> aiotCategoryRepository.findById(id).ifPresent(entity -> {
                entity.setIsDeleted(1);
                entity.setDeletedAt(new Date());
                aiotCategoryRepository.save(entity);
            }));
        }
    }

    @Override
    public void export(AiotCategoryQueryDTO bean) {
        List<AiotCategoryRespDTO> dtos = new ArrayList<>(query(bean).getContent());
        ScExport.export(bean.getCfg(), dtos, AiotCategoryRespDTO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchUpdateOrCreateAiCategories(List<AiotEventPushDTO.SceneCategoriesDTO> sceneCategories, String sceneCode){
        for (AiotEventPushDTO.SceneCategoriesDTO sceneCategory : sceneCategories) {
            AiotCategory category = aiotCategoryRepository.findFirstByCodeWithDeleted(sceneCategory.getCategoryCode());
            if(Objects.isNull(category)){
                category = new AiotCategory();
                category.setCode(sceneCategory.getCategoryCode());
                category.setLevel(0);
                category.setName(sceneCategory.getCategoryName());
            }
            category.setIsDeleted(0);
            category.setDeletedAt(null);
            aiotCategoryRepository.save(category);
            //创建分类与具体场景的关联关系
            aiotSceneCategoryService.updateOrCreate(category.getCode(), sceneCode);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchUpdateOrCreateAiScenes(List<AiotEventPushDTO.AiScenesDTO> aiScenes, String sceneCode){
        for (AiotEventPushDTO.AiScenesDTO aiScene : aiScenes) {
            AiotCategory category = aiotCategoryRepository.findFirstByCodeWithDeleted(aiScene.getSceneCode());
            if(Objects.isNull(category)){
                category = new AiotCategory();
                category.setCode(aiScene.getSceneCode());
                category.setLevel(1);
                category.setName(aiScene.getSceneName());
            }
            category.setIsDeleted(0);
            category.setDeletedAt(null);
            aiotCategoryRepository.save(category);
            //创建分类与具体场景的关联关系
            aiotSceneCategoryService.updateOrCreate(category.getCode(), sceneCode);
        }
    }
}