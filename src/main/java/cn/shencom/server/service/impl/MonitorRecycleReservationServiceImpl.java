package cn.shencom.server.service.impl;

import cn.shencom.model.MonitorRecycleReservation;
import cn.shencom.model.dto.create.MonitorRecycleReservationCreateDTO;
import cn.shencom.model.dto.query.MonitorRecycleReservationQueryDTO;
import cn.shencom.model.dto.resp.MonitorRecycleReservationRespDTO;
import cn.shencom.model.dto.update.MonitorRecycleReservationUpdateDTO;
import cn.shencom.repos.MonitorRecycleReservationRepository;
import cn.shencom.scloud.common.base.constant.RespCode;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.jpa.scpage.MyLink;
import cn.shencom.scloud.common.jpa.util.export.ScExport;
import cn.shencom.scloud.common.jpa.util.query.LinkUtil;
import cn.shencom.scloud.common.jpa.util.query.ScQueryUtil;
import cn.shencom.scloud.common.server.service.BaseImpl;
import cn.shencom.scloud.common.util.BeanUtil;
import cn.shencom.server.service.IMonitorRecycleReservationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.util.*;


/**
 * 小散工程-监管工单流程-回收预约详情 的服务实现
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@Service
@Slf4j
public class MonitorRecycleReservationServiceImpl extends BaseImpl implements IMonitorRecycleReservationService {

    @Autowired
    private MonitorRecycleReservationRepository monitorRecycleReservationRepository;

    @Override
    public Page<MonitorRecycleReservationRespDTO> query(MonitorRecycleReservationQueryDTO bean) {
        Map<String, MyLink> linkMap = LinkUtil.convertLink(MonitorRecycleReservation.class);
        Page<MonitorRecycleReservation> res = monitorRecycleReservationRepository.findAll((root, query, builder) -> {
            //用于拼接条件
            List<Predicate> ps = new ArrayList<>();

            return ScQueryUtil.getPredicate(ps, bean, linkMap, root, query, builder);
        }, PageRequest.of(bean.getPage(), bean.getSize()));
        return ScQueryUtil.handle(res, linkMap, MonitorRecycleReservationRespDTO.class);
    }

    @Override
    public MonitorRecycleReservationRespDTO show(ScShowDTO bean) {
        Optional<MonitorRecycleReservation> option = monitorRecycleReservationRepository.findById(bean.getId());
        if (!option.isPresent()) {
            return null;
        }
        MonitorRecycleReservation entity = option.get();
        Map<String, MyLink> linkMap = LinkUtil.convertLink(MonitorRecycleReservation.class);
        return ScQueryUtil.handleOne(entity, linkMap, MonitorRecycleReservationRespDTO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public MonitorRecycleReservation create(MonitorRecycleReservationCreateDTO bean) {
        MonitorRecycleReservation entity = new MonitorRecycleReservation();
        BeanUtil.copyProperties(bean, entity);
        return monitorRecycleReservationRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public MonitorRecycleReservation update(MonitorRecycleReservationUpdateDTO bean) {
        MonitorRecycleReservation entity = monitorRecycleReservationRepository.findById(bean.getId())
                .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));
        BeanUtil.copyProperties(bean, entity);
        return monitorRecycleReservationRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            ids.forEach(id -> monitorRecycleReservationRepository.deleteById(id));
        }
    }

    @Override
    public void export(MonitorRecycleReservationQueryDTO bean) {
        List<MonitorRecycleReservationRespDTO> dtos = new ArrayList<>(query(bean).getContent());
        ScExport.export(bean.getCfg(), dtos, MonitorRecycleReservationRespDTO.class);
    }
}