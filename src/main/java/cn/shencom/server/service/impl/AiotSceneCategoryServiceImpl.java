package cn.shencom.server.service.impl;

import cn.shencom.model.AiotSceneCategory;
import cn.shencom.model.dto.create.AiotSceneCategoryCreateDTO;
import cn.shencom.model.dto.query.AiotSceneCategoryQueryDTO;
import cn.shencom.model.dto.resp.AiotSceneCategoryRespDTO;
import cn.shencom.model.dto.update.AiotSceneCategoryUpdateDTO;
import cn.shencom.repos.AiotSceneCategoryRepository;
import cn.shencom.scloud.common.base.constant.RespCode;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.jpa.scpage.MyLink;
import cn.shencom.scloud.common.jpa.util.export.ScExport;
import cn.shencom.scloud.common.jpa.util.query.LinkUtil;
import cn.shencom.scloud.common.jpa.util.query.ScQueryUtil;
import cn.shencom.scloud.common.server.service.BaseImpl;
import cn.shencom.scloud.common.util.BeanUtil;
import cn.shencom.server.service.IAiotSceneCategoryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.util.*;


/**
 * aiot_scene_category 的服务实现
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Service
@Slf4j
public class AiotSceneCategoryServiceImpl extends BaseImpl implements IAiotSceneCategoryService {

    @Autowired
    private AiotSceneCategoryRepository aiotSceneCategoryRepository;

    @Override
    public Page<AiotSceneCategoryRespDTO> query(AiotSceneCategoryQueryDTO bean) {
        Map<String, MyLink> linkMap = LinkUtil.convertLink(AiotSceneCategory.class);
        Page<AiotSceneCategory> res = aiotSceneCategoryRepository.findAll((root, query, builder) -> {
            //用于拼接条件
            List<Predicate> ps = new ArrayList<>();

            return ScQueryUtil.getPredicate(ps, bean, linkMap, root, query, builder);
        }, PageRequest.of(bean.getPage(), bean.getSize()));
        return ScQueryUtil.handle(res, linkMap, AiotSceneCategoryRespDTO.class);
    }

    @Override
    public AiotSceneCategoryRespDTO show(ScShowDTO bean) {
        Optional<AiotSceneCategory> option = aiotSceneCategoryRepository.findById(bean.getId());
        if (!option.isPresent()) {
            return null;
        }
        AiotSceneCategory entity = option.get();
        Map<String, MyLink> linkMap = LinkUtil.convertLink(AiotSceneCategory.class);
        return ScQueryUtil.handleOne(entity, linkMap, AiotSceneCategoryRespDTO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public AiotSceneCategory create(AiotSceneCategoryCreateDTO bean) {
        AiotSceneCategory entity = new AiotSceneCategory();
        BeanUtil.copyProperties(bean, entity);
        return aiotSceneCategoryRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public AiotSceneCategory update(AiotSceneCategoryUpdateDTO bean) {
        AiotSceneCategory entity = aiotSceneCategoryRepository.findById(bean.getId())
                .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));
        BeanUtil.copyProperties(bean, entity);
        return aiotSceneCategoryRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            ids.forEach(id -> aiotSceneCategoryRepository.deleteById(id));
        }
    }

    @Override
    public void export(AiotSceneCategoryQueryDTO bean) {
        List<AiotSceneCategoryRespDTO> dtos = new ArrayList<>(query(bean).getContent());
        ScExport.export(bean.getCfg(), dtos, AiotSceneCategoryRespDTO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public AiotSceneCategory updateOrCreate(String cateCode, String sceneCode) {
        AiotSceneCategory sceneCategory = aiotSceneCategoryRepository.findFirstByCateCodeAndSceneCode(cateCode, sceneCode);
        if(Objects.isNull(sceneCategory)){
            sceneCategory = new AiotSceneCategory();
            sceneCategory.setCateCode(cateCode);
            sceneCategory.setSceneCode(sceneCode);
            aiotSceneCategoryRepository.save(sceneCategory);
        }
        return sceneCategory;
    }
}