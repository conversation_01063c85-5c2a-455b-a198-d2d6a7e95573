package cn.shencom.server.service.impl;

import cn.shencom.constant.AimSceneManagementConstant;
import cn.shencom.constant.AimSceneRespCode;
import cn.shencom.model.AimFirmSceneManagement;
import cn.shencom.model.AimSceneFirmRelationship;
import cn.shencom.model.AimSceneManagement;
import cn.shencom.model.dto.AimSceneRelevanceFirmDTO;
import cn.shencom.model.dto.create.AimSceneManagementCreateDTO;
import cn.shencom.model.dto.query.AimSceneManagementQueryDTO;
import cn.shencom.model.dto.resp.AimSceneCategoryDTO;
import cn.shencom.model.dto.resp.AimSceneManagementRespDTO;
import cn.shencom.model.dto.resp.AimSceneStatisticsDTO;
import cn.shencom.model.dto.update.AimSceneManagementUpdateDTO;
import cn.shencom.repos.*;
import cn.shencom.scloud.common.base.constant.RespCode;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.jpa.scpage.MyLink;
import cn.shencom.scloud.common.jpa.scpage.ScExp;
import cn.shencom.scloud.common.jpa.scpage.ScQuery;
import cn.shencom.scloud.common.jpa.util.export.ScExport;
import cn.shencom.scloud.common.jpa.util.query.LinkUtil;
import cn.shencom.scloud.common.jpa.util.query.ScQueryUtil;
import cn.shencom.scloud.common.server.service.BaseImpl;
import cn.shencom.scloud.common.util.BeanUtil;
import cn.shencom.scloud.common.util.DateUtil;
import cn.shencom.server.service.IAimSceneManagementService;
import cn.shencom.utils.CodeUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import java.util.*;


/**
 * AI场景管理 的服务实现
 *
 * <AUTHOR>
 * @since 2022-07-26
 */
@Service
@Slf4j
public class AimSceneManagementServiceImpl extends BaseImpl implements IAimSceneManagementService {

    @Autowired
    private AimSceneManagementRepository aimSceneManagementRepository;

    @Autowired
    private AimSceneFirmRelationshipRepository aimSceneFirmRelationshipRepository;

    @Autowired
    private AimFirmSceneManagementRepository aimFirmSceneManagementRepository;


    @Autowired
    private EventOrderRepository eventOrderRepository;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private AiotSceneMgrRepository aiotSceneMgrRepository;




    @Override
    public Page<AimSceneManagementRespDTO> query(AimSceneManagementQueryDTO bean) {
        String firmId = bean.getFirmId();
        Integer isLinked = bean.getIsLinked();

        String sceneCategoryId = bean.getSceneCategoryId();
        Map<String, MyLink> linkMap = LinkUtil.convertLink(AimSceneManagement.class);
        Page<AimSceneManagement> res = aimSceneManagementRepository.findAll((root, query, builder) -> {
            //用于拼接条件
            List<Predicate> ps = new ArrayList<>();
            //树形传在外面
            if (StringUtils.isNotBlank(sceneCategoryId)) {
                ps.add(builder.equal(root.get("sceneCategoryId"), sceneCategoryId));
            }
            //查询关联列表时
            if (isLinked != null) {
                Join<Object, Object> join = root.join("aimSceneFirmRelationships", JoinType.LEFT);
                if (isLinked.equals(1)) {
                    ps.add(builder.and(builder.equal(join.get("firmId"), firmId)));
                } else if (isLinked.equals(0)) {
                    join.on(builder.equal(join.get("firmId"), firmId));
                    ps.add(builder.isNull(join.get("sceneId")));
                }
            }
            return ScQueryUtil.getPredicate(ps, bean, linkMap, root, query, builder);
        }, PageRequest.of(bean.getPage(), bean.getSize()));
        List<AimSceneManagementRespDTO> storeList = getStoreList();
        return ScQueryUtil.handle(res, linkMap, AimSceneManagementRespDTO.class, (re, dto) -> {
            formatStoreData(dto, storeList);
        });
    }

    @Override
    public void storeManagementList() {
        try {
            // 1. 查询数据
            AimSceneManagementQueryDTO bean = new AimSceneManagementQueryDTO();
            List<AimSceneManagementRespDTO> managementList = list(bean);

            // 2. 使用Fastjson序列化
            String jsonData = JSON.toJSONString(managementList);

            stringRedisTemplate.opsForValue().set(
                    AimSceneManagementConstant.RedisKey,
                    jsonData);
        } catch (Exception e) {
            log.error("Redis缓存数据失败", e);
            throw new RuntimeException("缓存数据失败", e);
        }
    }

    public void formatStoreData(AimSceneManagementRespDTO dto, List<AimSceneManagementRespDTO> storeList) {
        Optional<AimSceneManagementRespDTO> sceneManagementRespDTOOptional = storeList.stream()
                .filter(scene -> scene.getId().equals(dto.getId()))
                .findFirst();
        if (sceneManagementRespDTOOptional.isPresent()) {
            AimSceneManagementRespDTO management = sceneManagementRespDTOOptional.get();
            dto.setEventNum(management.getEventNum());
            dto.setFirmScene(management.getFirmScene());
            dto.setEventWrongNum(management.getEventWrongNum());
            dto.setHandledEventNum(management.getHandledEventNum());
            dto.setPendingEventNum(management.getPendingEventNum());
            dto.setTimeOutEventNum(management.getTimeOutEventNum());
            dto.setEventPreciseNum(management.getEventPreciseNum());
            dto.setEventInaccurateNum(management.getEventInaccurateNum());

            //事件识别准确率
            if (dto.getHandledEventNum() == 0L) {
                dto.setEventAccuracy("0.00%");
            } else {
                double rate = (dto.getHandledEventNum() * 1.0 / dto.getHandledEventNum()) * 100;
                dto.setEventAccuracy(String.format("%.2f", rate) + "%");
            }
        }

        if (dto.getSceneCategoryId()!=null){
            //查询场景类别信息
            aiotSceneMgrRepository.findById(dto.getSceneCategoryId()).ifPresent(item -> {
                dto.setSceneCategory(item.getSceneName());
            });
        }

    }

    public List<AimSceneManagementRespDTO> getStoreList() {
        try {
            String jsonData = stringRedisTemplate.opsForValue().get(AimSceneManagementConstant.RedisKey);
            if (StringUtils.isEmpty(jsonData)) {
                return null;
            }
            return JSON.parseArray(jsonData, AimSceneManagementRespDTO.class);
        } catch (Exception e) {
            log.error("从Redis读取数据失败", e);
            throw new RuntimeException("读取缓存数据失败", e);
        }
    }

    public List<AimSceneManagementRespDTO> list(AimSceneManagementQueryDTO bean) {
        String firmId = bean.getFirmId();
        Integer isLinked = bean.getIsLinked();

        String sceneCategoryId = bean.getSceneCategoryId();
        Map<String, MyLink> linkMap = LinkUtil.convertLink(AimSceneManagement.class);
        List<AimSceneManagement> res = aimSceneManagementRepository.findAll((root, query, builder) -> {
            //用于拼接条件
            List<Predicate> ps = new ArrayList<>();
            //树形传在外面
            if (StringUtils.isNotBlank(sceneCategoryId)) {
                ps.add(builder.equal(root.get("sceneCategoryId"), sceneCategoryId));
            }
            //查询关联列表时
            if (isLinked != null) {
                Join<Object, Object> join = root.join("aimSceneFirmRelationships", JoinType.LEFT);
                if (isLinked.equals(1)) {
                    ps.add(builder.and(builder.equal(join.get("firmId"), firmId)));
                } else if (isLinked.equals(0)) {
                    join.on(builder.equal(join.get("firmId"), firmId));
                    ps.add(builder.isNull(join.get("sceneId")));
                }
            }
            return ScQueryUtil.getPredicate(ps, bean, linkMap, root, query, builder);
        });
        return ScQueryUtil.handle(res, linkMap, AimSceneManagementRespDTO.class, (re, dto) -> {
            dealDtoData(dto);
        });
    }

    private void dealDtoData(AimSceneManagementRespDTO dto) {
        aiotSceneMgrRepository.findById(dto.getSceneCategoryId()).ifPresent( r ->
                dto.setSceneCategory(r.getSceneName())
        );
        dealEventData(dto);
    }

    private void dealEventData(AimSceneManagementRespDTO dto) {
        //违规类型编号
        List<String> typeCodeList = Lists.newArrayList();
        //厂商场景
        StringBuilder sb = new StringBuilder();
        //该场景关联的厂商场景
        List<AimSceneFirmRelationship> sceneFirmRelationships = aimSceneFirmRelationshipRepository.findBySceneId(dto.getId());
        for (AimSceneFirmRelationship firmRelationship : sceneFirmRelationships) {
            Map<String, String> firmData = aimFirmSceneManagementRepository.getNameAndCodeById(firmRelationship.getFirmId());
            typeCodeList.add(firmData.get("code"));
            sb.append(firmData.get("name")).append("、");
        }
        dto.setFirmScene(sb.length() > 0 ? sb.substring(0, sb.length() - 1) : sb.toString());

        //todo ai事件统计
        AimSceneManagementRespDTO eventData = new AimSceneManagementRespDTO();
        if (CollectionUtils.isNotEmpty(typeCodeList)) {
            eventData = eventOrderRepository.getEventData(typeCodeList);
        }
        long eventNum = eventData.getEventNum() == null ? 0L : eventData.getEventNum();
        long handledEventNum = eventData.getHandledEventNum() == null ? 0L : eventData.getHandledEventNum();
        long pendingEventNum = eventData.getPendingEventNum() == null ? 0L : eventData.getPendingEventNum();
        long timeOutEventNum = eventData.getTimeOutEventNum() == null ? 0L : eventData.getTimeOutEventNum();
        long eventPreciseNum = eventData.getEventPreciseNum() == null ? 0L : eventData.getEventPreciseNum();
        long eventInaccurateNum = eventData.getEventInaccurateNum() == null ? 0L : eventData.getEventInaccurateNum();
        long eventWrongNum = eventData.getEventWrongNum() == null ? 0L : eventData.getEventWrongNum();
        dto.setEventNum(eventNum);
        dto.setHandledEventNum(handledEventNum);
        dto.setPendingEventNum(pendingEventNum);
        dto.setTimeOutEventNum(timeOutEventNum);
        dto.setEventPreciseNum(eventPreciseNum);
        dto.setEventInaccurateNum(eventInaccurateNum);
        dto.setEventWrongNum(eventWrongNum);

        //事件识别准确率
        if (handledEventNum == 0L) {
            dto.setEventAccuracy("0.00%");
        } else {
            double rate = (eventPreciseNum * 1.0 / handledEventNum) * 100;
            dto.setEventAccuracy(String.format("%.2f", rate) + "%");
        }

    }

    @Override
    public AimSceneManagementRespDTO show(ScShowDTO bean) {
        Optional<AimSceneManagement> option = aimSceneManagementRepository.findById(bean.getId());
        if (!option.isPresent()) {
            return null;
        }
        AimSceneManagement entity = option.get();
        Map<String, MyLink> linkMap = LinkUtil.convertLink(AimSceneManagement.class);
        AimSceneManagementRespDTO respDTO = ScQueryUtil.handleOne(entity, linkMap, AimSceneManagementRespDTO.class);
        dealDtoData(respDTO);
        return respDTO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public AimSceneManagement create(AimSceneManagementCreateDTO bean) {
        AimSceneManagement entity = new AimSceneManagement();
        BeanUtil.copyProperties(bean, entity);
        //查最大编号
        String year = DateUtil.getYear(new Date());
        String maxCode = aimSceneManagementRepository.getMaxCode();
        if (StringUtils.isBlank(maxCode)) {
            maxCode = AimSceneManagementConstant.SCAI_PREFIX + year + "000";
        }
        entity.setCode(CodeUtil.getCodeBySc(maxCode));
        return aimSceneManagementRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public AimSceneManagement update(AimSceneManagementUpdateDTO bean) {
        AimSceneManagement entity = aimSceneManagementRepository.findById(bean.getId())
                .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));
        BeanUtil.copyProperties(bean, entity);
        return aimSceneManagementRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            ids.forEach(id -> aimSceneManagementRepository.findById(id).ifPresent(entity -> {
                //是否还存在关联厂商
                Integer isExist = aimSceneFirmRelationshipRepository.existsBySceneId(id);
                if (isExist != null) {
                    throw new ScException(AimSceneRespCode.EXIST_FIRM);
                }
                entity.setIsDeleted(1);
                aimSceneManagementRepository.save(entity);
            }));
        }
    }

    @Override
    public void export(AimSceneManagementQueryDTO bean) {
        List<AimSceneManagementRespDTO> dtos = new ArrayList<>(query(bean).getContent());
        ScExport.export(bean.getCfg(), dtos, AimSceneManagementRespDTO.class);
    }

    @Transactional
    @Override
    public void sceneRelevance(AimSceneRelevanceFirmDTO bean) {
        String sceneId = bean.getSceneId();
        AimSceneManagement aimSceneManagement = aimSceneManagementRepository.findById(sceneId)
                .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));
        for (String firmId : bean.getFirmIds()) {
            AimSceneFirmRelationship aimSceneFirmRelationship = new AimSceneFirmRelationship();
            aimSceneFirmRelationship.setFirmId(firmId);
            aimSceneFirmRelationship.setSceneId(sceneId);
            aimSceneFirmRelationshipRepository.save(aimSceneFirmRelationship);
            //修改ai场景关联厂商场景数
            aimSceneManagement.setFirmNum(aimSceneManagement.getFirmNum() + 1);
            //修改厂商关联ai场景数
            AimFirmSceneManagement aimFirmSceneManagement = aimFirmSceneManagementRepository.findById(firmId)
                    .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));
            aimFirmSceneManagement.setSceneNum(aimFirmSceneManagement.getSceneNum() + 1);
        }
    }

    @Transactional
    @Override
    public void sceneDisassociate(AimSceneRelevanceFirmDTO bean) {
        String sceneId = bean.getSceneId();
        AimSceneManagement aimSceneManagement = aimSceneManagementRepository.findById(sceneId)
                .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));
        for (String firmId : bean.getFirmIds()) {
            aimSceneFirmRelationshipRepository.deleteBySceneIdAndFirmId(sceneId, firmId);
            //修改ai场景关联厂商场景数
            aimSceneManagement.setFirmNum(aimSceneManagement.getFirmNum() - 1);
            //修改厂商关联ai场景数
            AimFirmSceneManagement aimFirmSceneManagement = aimFirmSceneManagementRepository.findById(firmId)
                    .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));
            aimFirmSceneManagement.setSceneNum(aimFirmSceneManagement.getSceneNum() - 1);
        }
    }

    @Override
    public AimSceneStatisticsDTO sceneStatistics(AimSceneManagementQueryDTO bean) {
        dealSimpleQuery(bean);
        if (StringUtils.isBlank(bean.getSimpleUsed())) {
            ScQueryUtil.getValuesRmAndSetBean(bean.getQuery(), Arrays.asList("firmId", "name", "code", "sceneCategoryId", "tags"), bean);
            dealBean(bean);
        } else {
            bean.setSimpleUsed("%" + bean.getSimpleUsed() + "%");
        }
        return aimSceneManagementRepository.getSceneStatisticsData(bean);
    }

    @Override
    public List<String> codes(String aimSceneId) {
        return aimSceneManagementRepository.findCodesById(aimSceneId);
    }

    @Override
    public List<AimSceneCategoryDTO> getSceneAll() {
        return aimSceneManagementRepository.getAiSceneAll();
    }

    private void dealBean(AimSceneManagementQueryDTO bean) {
        if (StringUtils.isNotBlank(bean.getName())) {
            bean.setName("%" + bean.getName() + "%");
        }
        if (StringUtils.isNotBlank(bean.getCode())) {
            bean.setCode("%" + bean.getCode() + "%");
        }
        if (StringUtils.isNotBlank(bean.getTags())) {
            bean.setTags("%" + bean.getTags() + "%");
        }
    }

    private AimSceneManagementQueryDTO dealSimpleQuery(AimSceneManagementQueryDTO bean) {
        List<ScQuery> query = bean.getQuery();
        if (CollectionUtils.isEmpty(query)) {
            return bean;
        }
        List<ScExp> scExpList = query.get(0).getExps();
        if (CollectionUtils.isEmpty(scExpList)) {
            return bean;
        }
        ScExp scExp = query.get(0).getExps().get(0);
        if ("or".equals(scExp.getLr())) {
            bean.setSimpleUsed(scExp.getValue());
        }
        return bean;
    }
}