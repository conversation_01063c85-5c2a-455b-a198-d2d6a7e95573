package cn.shencom.server.service.impl;

import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.hutool.core.collection.CollectionUtil;
import cn.shencom.constant.CommonConstant;
import cn.shencom.constant.InviteCodeConstant;
import cn.shencom.enums.EngineeringRoleEnum;
import cn.shencom.enums.MonitorFlowEnum;
import cn.shencom.model.*;
import cn.shencom.model.dto.create.MonitorFlowCreateDTO;
import cn.shencom.model.dto.create.SporadicProjectCreateDTO;
import cn.shencom.model.dto.excel.SporadicProjectExcelDTO;
import cn.shencom.model.dto.query.SporadicProjectMobileQueryDTO;
import cn.shencom.model.dto.query.SporadicProjectQueryDTO;
import cn.shencom.model.dto.resp.FnRmsv3MembersTypeRelateRespDTO;
import cn.shencom.model.dto.resp.InviteCodeRespDTO;
import cn.shencom.model.dto.resp.MonitorFlowRespDTO;
import cn.shencom.model.dto.resp.MonitorOrderRespDTO;
import cn.shencom.model.dto.resp.SporadicProjectRespDTO;
import cn.shencom.model.dto.resp.SporadicProjectRegionStatisticsRespDTO;
import cn.shencom.model.dto.update.ContractingUnitOrganizationRelateUpdateDTO;
import cn.shencom.model.dto.update.ContractingUnitUpdateDTO;
import cn.shencom.model.dto.update.EngineeringMembersUpdateDTO;
import cn.shencom.model.dto.update.MonitorFlowUpdateDTO;
import cn.shencom.model.dto.update.SporadicProjectUpdateDTO;
import cn.shencom.repos.*;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.base.constant.RespCode;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.scloud.common.base.util.BizAssert;
import cn.shencom.scloud.common.core.utils.UploadUtilV2;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.jpa.scpage.MyLink;
import cn.shencom.scloud.common.jpa.util.export.ScExport;
import cn.shencom.scloud.common.jpa.util.query.LinkUtil;
import cn.shencom.scloud.common.jpa.util.query.ScQueryUtil;
import cn.shencom.scloud.common.server.service.BaseImpl;
import cn.shencom.scloud.common.util.BeanUtil;
import cn.shencom.scloud.common.util.DateUtil;
import cn.shencom.scloud.common.util.GaoDeMapUtil;
import cn.shencom.scloud.common.util.export.ExportWordUtil2007;
import cn.shencom.scloud.common.util.export.ScExcelExportUtil;
import cn.shencom.scloud.scloudapifile.dto.ByteArrayMultipartFile;
import cn.shencom.scloud.scloudapifile.dto.FileUploadResourceDTO;
import cn.shencom.scloud.scloudapifile.service.FileService;
import cn.shencom.scloud.security.core.ScContext;
import cn.shencom.server.service.*;
import cn.shencom.utils.InviteCodeUtil;
import cn.shencom.utils.UserUtil;
import cn.shencom.utils.UserUtil.UserUtilsProjectQueryDTO;
import cn.shencom.utils.UserUtil.UserUtilsRegions;
import cn.shencom.utils.UserUtil.UtilsRegion;
import cn.shencom.utils.XsgcContext;
import com.xxl.job.core.context.XxlJobHelper;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 小散工程表 的服务实现
 *
 * <AUTHOR>
 * @since 2025-05-29
 */
@Service
@Slf4j
public class SporadicProjectServiceImpl extends BaseImpl implements ISporadicProjectService {

    @Autowired
    private SporadicProjectRepository sporadicProjectRepository;

    @Resource
    private UploadUtilV2 uploadUtilV2;

    @Resource
    private Validator validator;

    @Resource
    private SporadicProjectCategoryRepository sporadicProjectCategoryRepository;

    @Resource
    private ComRegionRepository comRegionRepository;

    @Resource
    private FileService fileService;

    @Resource
    private GaoDeMapUtil gaoDeMapUtil;

    @Autowired
    private GisPoiRepository gisPoiRepository;

    @Autowired
    private IEngineeringMembersService engineeringMembersService;

    @Autowired
    private IMonitorOrderService monitorOrderService;

    @Autowired
    private IMonitorFlowService monitorFlowService;

    @Autowired
    private XsgcBusinessMembersRepository xsgcBusinessMembersRepository;

    @Autowired
    private EventCameraPointDeviceRepository eventCameraPointDeviceRepository;

    @Autowired
    private MonitorOrderRepository monitorOrderRepository;

    @Autowired
    private IFnRmsv3MembersTypeRelateService fnRmsv3MembersTypeRelateService;

    @Autowired
    private EngineeringMembersRepository engineeringMembersRepository;

    @Autowired
    private EngineeringMembersProjectRelateRepository engineeringMembersProjectRelateRepository;

    @Autowired
    private IContractingUnitService contractingUnitService;

    @Autowired
    private IContractingUnitOrganizationRelateService contractingUnitOrganizationRelateService;

    @Autowired
    private UserUtil userUtil;

    @Autowired
    private ISporadicProjectMemoService sporadicProjectMemoService;


    @Autowired
    private MonitorFlowRepository monitorFlowRepository;

    @Autowired
    private MonitorAccessInfoRepository monitorAccessInfoRepository;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public Page<SporadicProjectRespDTO> query(SporadicProjectQueryDTO bean) {

        String catePid = ScQueryUtil.getValueAndRm(bean.getQuery(), "catePid");
        String cateId = ScQueryUtil.getValueAndRm(bean.getQuery(), "cateId");

        List<String> ids = new ArrayList<>();

        // 组织id
        String organizationId = XsgcContext.getOrganizationId();
        String userId = ScContext.getCurrentUserThrow().getId();

        // 如果组织id不为空，查询组织成员的区域信息
        FnRmsv3MembersTypeRelateRespDTO typeRelateRespDTO = null;
        if (organizationId != null) {
            typeRelateRespDTO = fnRmsv3MembersTypeRelateService.getMemberRelateByUserAndOrganization(userId,
                    organizationId);
            if (typeRelateRespDTO == null || typeRelateRespDTO.getRegionIds().isEmpty()) {
                return Page.empty(PageRequest.of(bean.getPage(), bean.getSize()));
            }
        } else {

            // 查询当前用户是否是业务人员
            // 判断当前是否是业务人员，如果是则不受限制
            XsgcBusinessMembers xsgcBusinessMembers = xsgcBusinessMembersRepository
                    .findFirstByUserIdAndStatus(userId, 1);

            if (xsgcBusinessMembers == null) {
                // 如果不是业务人员，则查询当前用户是否是施工人员
                EngineeringMembers engineeringMembers = engineeringMembersRepository.findFirstByUserId(userId);
                if (engineeringMembers != null) {
                    // 如果是施工人员，查询这个施工人员关联的项目
                    List<EngineeringMembersProjectRelate> projectRelateList = engineeringMembersProjectRelateRepository
                            .findByMemberId(engineeringMembers.getId());
                    if (projectRelateList.isEmpty()) {
                        return Page.empty(PageRequest.of(bean.getPage(), bean.getSize()));
                    }
                    ids = projectRelateList.stream().map(EngineeringMembersProjectRelate::getProjectId)
                            .collect(Collectors.toList());
                }
            }
        }

        Map<String, MyLink> linkMap = LinkUtil.convertLink(SporadicProject.class);
        FnRmsv3MembersTypeRelateRespDTO finalTypeRelateRespDTO = typeRelateRespDTO;
        List<String> finalIds = ids;
        Page<SporadicProject> res = sporadicProjectRepository.findAll((root, query, builder) -> {
            // 用于拼接条件
            List<Predicate> ps = new ArrayList<>();

            // 根据组织id进行筛选
            if (StringUtil.isNotEmpty(organizationId)) {
                ps.add(builder.equal(root.get("organizationId"), organizationId));
            }

            if (finalTypeRelateRespDTO != null) {
                // 根据区域进行筛选
                Integer level = finalTypeRelateRespDTO.getLevel();
                List<FnRmsv3MembersTypeRelateBinding> regionIds = finalTypeRelateRespDTO.getRegionIds();
                // 根据区域筛选
                if (!regionIds.isEmpty() && level != null) {

                    if (level == 1) {
                        List<String> regionIdValues = regionIds.stream()
                                .map(FnRmsv3MembersTypeRelateBinding::getRegionPid)
                                .collect(Collectors.toList());
                        // 根据regionPid进行筛选
                        ps.add(builder.in(root.get("regionPid")).value(regionIdValues));
                    } else if (level == 2) {
                        List<String> regionIdValues = regionIds.stream()
                                .map(FnRmsv3MembersTypeRelateBinding::getRegionId)
                                .collect(Collectors.toList());
                        // 根据regionId进行筛选
                        ps.add(builder.in(root.get("regionId")).value(regionIdValues));
                    } else if (level == 3) {
                        List<String> regionIdValues = regionIds.stream()
                                .map(FnRmsv3MembersTypeRelateBinding::getRegionCid)
                                .collect(Collectors.toList());
                        // 根据regionCid进行筛选
                        ps.add(builder.in(root.get("regionCid")).value(regionIdValues));
                    }
                }
            }

            if (!CollectionUtil.isEmpty(finalIds)) {
                ps.add(builder.in(root.get("id")).value(finalIds));
            }

            if (StringUtils.isNotEmpty(catePid)) {
                Join<Object, Object> pCateJoin = root.join("pCate", JoinType.LEFT);
                ps.add(builder.equal(pCateJoin.get("id"), catePid));
            }
            if (StringUtils.isNotEmpty(cateId)) {
                Join<Object, Object> pCateJoin = root.join("cate", JoinType.LEFT);
                ps.add(builder.equal(pCateJoin.get("id"), cateId));
            }
            return ScQueryUtil.getPredicate(ps, bean, linkMap, root, query, builder);
        }, PageRequest.of(bean.getPage(), bean.getSize()));
        return ScQueryUtil.handle(res, linkMap, SporadicProjectRespDTO.class,
                (entity, dto) -> handleSporadicProjectDto(dto));
    }

    @Override
    public Page<SporadicProjectRespDTO> queryByIds(SporadicProjectQueryDTO bean) {
        List<String> ids = bean.getIds();
        if (bean.getIds().isEmpty()) {
            return Page.empty(PageRequest.of(bean.getPage(), bean.getSize()));
        }
        Map<String, MyLink> linkMap = LinkUtil.convertLink(SporadicProject.class);
        Page<SporadicProject> res = sporadicProjectRepository.findAll((root, query, builder) -> {
            // 用于拼接条件
            List<Predicate> ps = new ArrayList<>();

            if (!CollectionUtil.isEmpty(ids)) {
                ps.add(builder.in(root.get("id")).value(ids));
            }

            return ScQueryUtil.getPredicate(ps, bean, linkMap, root, query, builder);
        }, PageRequest.of(bean.getPage(), bean.getSize()));
        return ScQueryUtil.handle(res, linkMap, SporadicProjectRespDTO.class,
                (entity, dto) -> handleSporadicProjectDto(dto));
    }

    @Override
    public SporadicProjectRespDTO show(ScShowDTO bean) {
        Optional<SporadicProject> option = sporadicProjectRepository.findById(bean.getId());
        if (!option.isPresent()) {
            return null;
        }
        SporadicProject entity = option.get();
        Map<String, MyLink> linkMap = LinkUtil.convertLink(SporadicProject.class);
        SporadicProjectRespDTO respDTO = ScQueryUtil.handleOne(entity, linkMap, SporadicProjectRespDTO.class,
                (r, dto) -> handleSporadicProjectDto(dto));

        //获取关联的设备id
        List<MonitorAccessInfo> monitorAccessInfoList = monitorAccessInfoRepository.findByOrderId(respDTO.getOrderId());
        if (!monitorAccessInfoList.isEmpty()){
            respDTO.setCameraDeviceIds(monitorAccessInfoList.stream().map(MonitorAccessInfo::getDeviceId).collect(Collectors.toList()));
            respDTO.setCameraSerialNos(monitorAccessInfoList.stream().map(MonitorAccessInfo::getSerialNo).collect(Collectors.toList()));
        }


        return respDTO;
    }

    protected void handleSporadicProjectDto(SporadicProjectRespDTO dto) {
        if (dto.getStatus() == 0) {
            dto.setStatusName("未开始");
        } else if (dto.getStatus() == 2) {
            dto.setStatusName("已结束");
        } else {
            dto.setStatusName("施工中");
        }

        // 查询关联的工单
        MonitorOrder monitorOrder = monitorOrderRepository.findByProjectId(dto.getId());
        if (monitorOrder != null) {
            dto.setFlow(monitorOrder.getFlow());
            dto.setFlowId(monitorOrder.getFlowId());
            dto.setOrderId(monitorOrder.getId());
        }

        //工单的备注信息
        dto.setMemoRespDTOList(sporadicProjectMemoService.findByProjectId(dto.getId()));
    }

    private int getProjectStatus(Date startAt, Date endAt) {
        Date start = DateUtil.getStartOfToday(LocalDate.now());
        Date end = DateUtil.getEndOfToday(LocalDate.now());
        if (end.compareTo(startAt) < 0) {
            return CommonConstant.PROJECT_STATUS_WAIT;
        } else if (start.compareTo(endAt) > 0) {
            return CommonConstant.PROJECT_STATUS_END;
        } else {
            return CommonConstant.PROJECT_STATUS_GOING;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public SporadicProject create(SporadicProjectCreateDTO bean) {

        String organizationId = XsgcContext.getOrganizationId();
        if (StringUtil.isBlank(organizationId)) {
            throw new ScException("组织id不能为空！");
        }

        String userId = ScContext.getCurrentUserThrow().getId();

        SporadicProject entity = new SporadicProject();
        BeanUtil.copyProperties(bean, entity);
        entity.setCreateUser(userId);
        // 更新经纬度
        GisPoi gisPoi = gisPoiRepository.findById(bean.getPoiId()).orElseThrow(() -> new ScException("点位不存在！"));
        entity.setLat(new BigDecimal(gisPoi.getLat()));
        entity.setLng(new BigDecimal(gisPoi.getLng()));
        entity.setOrganizationId(organizationId);

        // 检查当前组织人员所属区域
        if (!checkRegion(userId, organizationId, entity)) {
            throw new ScException("您无当前区域权限！");
        }

        // 获取施工状态
        entity.setStatus(getProjectStatus(entity.getStartAt(), entity.getEndAt()));

        SporadicProject save = sporadicProjectRepository.save(entity);
        // 创建或者更新工程成员
        createOrUpdateEngineer(save);
        // 创建监管流程工单
        monitorOrderService.createOrder(save);

        //填充工程分类和工程类别
        entity.setPCateName(bean.getPCateName());
        entity.setCateName(bean.getCateName());
        delProjectCate(entity);

        return save;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public SporadicProject update(SporadicProjectUpdateDTO bean) {

        // 组织id
        String organizationId = XsgcContext.getOrganizationId();
        String userId = ScContext.getCurrentUserThrow().getId();
        if (StringUtil.isBlank(organizationId)) {
            throw new ScException("组织id不能为空！");
        }

        SporadicProject entity = sporadicProjectRepository.findById(bean.getId())
                .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));
        BeanUtil.copyProperties(bean, entity);

        // 检查当前组织人员所属区域
        if (!checkRegion(userId, organizationId, entity)) {
            throw new ScException("您无当前区域权限！");
        }

        // 更新施工状态
        entity.setStatus(getProjectStatus(entity.getStartAt(), entity.getEndAt()));

        // 更新经纬度
        GisPoi gisPoi = gisPoiRepository.findById(bean.getPoiId()).orElseThrow(() -> new ScException("点位不存在！"));
        entity.setLat(new BigDecimal(gisPoi.getLat()));
        entity.setLng(new BigDecimal(gisPoi.getLng()));

        // 创建或者更新工程成员
        createOrUpdateEngineer(entity);


        //填充工程分类和工程类别
        entity.setPCateName(bean.getPCateName());
        entity.setCateName(bean.getCateName());
        delProjectCate(entity);

        return sporadicProjectRepository.save(entity);
    }


    /**
     * 处理工程分类和工程类别
     */
    private void delProjectCate(SporadicProject project){

        String pCateName = project.getPCateName();
        String cateName = project.getCateName();
        if (StringUtil.isNotBlank(pCateName)){
            SporadicProjectCategory cate = sporadicProjectCategoryRepository.findFirstBypIdAndName("0",pCateName);
            if (cate==null){
                throw new ScException("该工程分类不存在！");
            }
            project.setCatePid(cate.getId());
        }else {
            throw new ScException("工程分类不能为空！");
        }

        if (project.getCatePid()!=null&&StringUtil.isNotBlank(cateName)){
            SporadicProjectCategory cate = sporadicProjectCategoryRepository.findFirstBypIdAndName(project.getCatePid(),cateName);
            if (cate==null){
                SporadicProjectCategory category = new SporadicProjectCategory();
                category.setActive(1);
                category.setPId(project.getCatePid());
                category.setName(cateName);
                cate = sporadicProjectCategoryRepository.save(category);
            }
            project.setCateId(cate.getId());
        }else {
            project.setCateId(null);
        }
    }



    /**
     * 检查当前组织人员所属区域
     * 
     * @param userId
     * @param organizationId
     * @param project
     */
    private boolean checkRegion(String userId, String organizationId, SporadicProject project) {
        FnRmsv3MembersTypeRelateRespDTO typeRelateRespDTO = fnRmsv3MembersTypeRelateService
                .getMemberRelateByUserAndOrganization(userId, organizationId);
        if (typeRelateRespDTO == null || typeRelateRespDTO.getRegionIds().isEmpty()
                || typeRelateRespDTO.getLevel() == null) {
            throw new ScException("当前组织用户无所属区域！");
        }
        String regionPid = project.getRegionPid();
        String regionId = project.getRegionId();
        String regionCid = project.getRegionCid();
        int level = typeRelateRespDTO.getLevel();
        List<FnRmsv3MembersTypeRelateBinding> regionIds = typeRelateRespDTO.getRegionIds();
        if (level == 1) {
            if (!regionIds.stream().anyMatch(region -> regionPid.equals(region.getRegionPid()))) {
                return false;
            }
        } else if (level == 2) {
            if (!regionIds.stream().anyMatch(region -> regionId.equals(region.getRegionId()))) {
                return false;
            }
        } else if (level == 3) {
            if (!regionIds.stream().anyMatch(region -> regionCid.equals(region.getRegionCid()))) {
                return false;
            }
        }
        return true;
    }

    @Override
    @Transactional
    public void updateStatus(SporadicProjectUpdateDTO bean) {

        // 只允许修改状态为结束施工
        if (bean.getStatus() == 2) {
            SporadicProject entity = sporadicProjectRepository.findById(bean.getId())
                    .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));

            if (entity.getStatus() == 0) {
                throw new ScException("当前工程施工未开始");
            }

            closeProjectStatus(entity);
        }

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            // 接入监控的无法删除
            List<SporadicProject> projects = sporadicProjectRepository.findByIdIn(ids);
            boolean isExistMonitorFlag = projects.stream().anyMatch(project -> project.getMonitorFlag() == 1);
            if (isExistMonitorFlag) {
                if (projects.size() > 1) {
                    throw new ScException("存在工程已接入监管，请先联系安装人员回收设备！");
                } else {
                    throw new ScException("该工程已接入监管，请先联系安装人员回收设备！");
                }
            }

            ids.forEach(id -> sporadicProjectRepository.findById(id).ifPresent(entity -> {
                entity.setIsDeleted(1);
                entity.setDeletedAt(new Date());
                sporadicProjectRepository.save(entity);

                // 删除工程时，移除关联的工程人员
                engineeringMembersService.removeMemberRelateByProjectId(entity.getId());

                // 删除工程时，删除工单
                monitorOrderRepository.deleteByProjectId(entity.getId());

            }));
        }
    }

    @Override
    public void export(SporadicProjectQueryDTO bean) {
        List<SporadicProjectRespDTO> dtos = new ArrayList<>(query(bean).getContent());
        ScExport.export(bean.getCfg(), dtos, SporadicProjectRespDTO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> importExcel(MultipartFile file) {

        String organizationId = XsgcContext.getOrganizationId();
        if (StringUtil.isBlank(organizationId)) {
            throw new ScException("组织id不能为空！");
        }

        String userId = ScContext.getCurrentUserThrow().getId();

        try {
            List<SporadicProjectExcelDTO> importData;
            int saveNum = 0;
            List<SporadicProjectExcelDTO> errorList = new ArrayList<>();
            try {
                importData = ExportWordUtil2007.importExcel(file, 0, 1, SporadicProjectExcelDTO.class);
            } catch (Exception e) {
                e.printStackTrace();
                throw new ScException("读取excel错误");
            }
            for (SporadicProjectExcelDTO record : importData) {
                SporadicProject entity = handleSingleRecord(record, userId, organizationId);
                if (entity == null) {
                    errorList.add(record);
                    continue;
                }
                entity.setCreateUser(userId);
                entity.setOrganizationId(organizationId);

                // 更新施工状态
                entity.setStatus(getProjectStatus(entity.getStartAt(), entity.getEndAt()));
                SporadicProject save = sporadicProjectRepository.save(entity);

                // 创建或者更新工程成员
                createOrUpdateEngineer(save);

                // 创建监管流程工单
                monitorOrderService.createOrder(save);

                saveNum++;
            }
            int errors = errorList.size();
            if (errors > 0) {
                List<String> cloList = Arrays.asList(
                        "name", "pCateName", "cateName", "amount", "area",
                        "startAt", "endAt", "district", "street", "community", "address",
                        "constructorName", "constructorCharger", "ownerMobile",
                        "contractorName", "contractorCharger", "contractorChargerMobile", "projectNumber",
                        "error");
                String title = "导入失败记录表";
                Workbook workbook = ScExcelExportUtil.exportExcel(new ExportParams(title, title),
                        SporadicProjectExcelDTO.class, errorList, cloList);
                try {
                    String url = uploadWorkbook(workbook, title);
                    return new Result<>("4004",
                            saveNum > 0 ? saveNum + "条数据导入成功," + errors + "条数据导入失败" : errors + "条数据导入失败",
                            url);
                } catch (IOException e) {
                    throw new ScException(e.getMessage());
                }
            }
            return new Result<>("0000", saveNum + "条数据导入成功");
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ScException("导入失败");
        }
    }

    /**
     * 创建或者更新工程成员
     * 
     * @param bean
     */
    private void createOrUpdateEngineer(SporadicProject bean) {
        // 创建或更新业主
        EngineeringMembersUpdateDTO updateDTO = new EngineeringMembersUpdateDTO();

        // 先删除，再创建
        engineeringMembersService.removeMemberRelateByProjectId(bean.getId());

        // 项目id和组织id
        updateDTO.setProjectId(bean.getId());
        updateDTO.setOrganizationId(bean.getOrganizationId());

        updateDTO.setType(EngineeringRoleEnum.CONSTRUCTION_PARTY.getMemberType());
        updateDTO.setRealname(bean.getConstructorCharger());
        updateDTO.setMobile(bean.getOwnerMobile());
        engineeringMembersService.createOrUpdate(updateDTO);

        // 创建或更新施工单位负责人
        updateDTO.setType(EngineeringRoleEnum.CONSTRUCTION_UNIT_LEADER.getMemberType());
        updateDTO.setRealname(bean.getContractorCharger());
        updateDTO.setMobile(bean.getContractorChargerMobile());
        engineeringMembersService.createOrUpdate(updateDTO);

        // 创建施工单位
        ContractingUnitUpdateDTO contractorUpdateDTO = new ContractingUnitUpdateDTO();
        contractorUpdateDTO.setName(bean.getContractorName());
        ContractingUnit contractor = contractingUnitService.createOrUpdate(contractorUpdateDTO);
        bean.setContractorId(contractor.getId());

        // 创建施工单位组织关联
        ContractingUnitOrganizationRelateUpdateDTO relate = new ContractingUnitOrganizationRelateUpdateDTO();
        relate.setContractingUnitId(contractor.getId());
        relate.setOrganizationId(bean.getOrganizationId());
        contractingUnitOrganizationRelateService.createOrUpdate(relate);
    }

    /**
     * 将 Workbook 上传到 oss
     *
     * @param workbook
     */
    public String uploadWorkbook(Workbook workbook, String title) throws IOException {
        ByteArrayOutputStream stream = uploadUtilV2.transform(workbook);
        String fileName = title + ".xlsx";
        ByteArrayMultipartFile file = new ByteArrayMultipartFile(fileName, null, stream.toByteArray());
        FileUploadResourceDTO resDto = new FileUploadResourceDTO();
        resDto.setName(fileName);
        resDto.setOpen(true);
        resDto.setTarget(0);
        try {
            Result<FileUploadResourceDTO> result = fileService.upload(file, resDto, null);
            if (Objects.isNull(result) || !result.success()) {
                throw new ScException("上传" + fileName + "失败");
            }
            return result.getData().getUrl();
        } catch (Exception e) {
            throw new ScException("上传" + fileName + "失败");
        }
    }

    protected SporadicProject handleSingleRecord(SporadicProjectExcelDTO record, String userId, String organizationId) {
        try {
            Set<ConstraintViolation<SporadicProjectExcelDTO>> errors = validator.validate(record);
            if (!errors.isEmpty()) {
                BizAssert.isTrue(false, errors.iterator().next().getMessage());
            }
            BizAssert.isTrue(record.getEndAt().compareTo(record.getStartAt()) > 0, "项目结束时间必须大于开始时间");

            String regionPid = comRegionRepository.findIdByPidAndTitle(CommonConstant.REGION_SHENZHEN,
                    record.getDistrict());
            BizAssert.notNull(regionPid, "所属区不存在");
            String regionId = comRegionRepository.findIdByPidAndTitle(regionPid, record.getStreet());
            BizAssert.notNull(regionId, "所属街道不存在");
            String regionCid = comRegionRepository.findIdByPidAndTitle(regionId, record.getVillage());
            BizAssert.notNull(regionCid, "所属社区不存在");
            SporadicProject project = sporadicProjectRepository.findFirstByRegionPidAndRegionIdAndRegionCidAndName(
                    regionPid, regionId, regionCid, record.getName());
            BizAssert.isNull(project, "当前区域已存在此工程");
            setLngLat(record);

            SporadicProject entity = new SporadicProject();
            BeanUtil.copyProperties(record, entity);

            delProjectCate(entity);
            entity.setRegionPid(regionPid);
            entity.setRegionId(regionId);
            entity.setRegionCid(regionCid);

            // 检查当前组织人员所属区域
            if (!checkRegion(userId, organizationId, entity)) {
                throw new ScException("您无当前区域权限！");
            }

            // 新增gis_poi
            if (entity.getLat() != null && entity.getLng() != null) {
                GisPoi gisPoi = new GisPoi();
                gisPoi.setAddr(entity.getAddress());
                gisPoi.setLng(entity.getLng().toString());
                gisPoi.setLat(entity.getLat().toString());
                gisPoi.setCreatedAt(new Date());
                GisPoi save = gisPoiRepository.save(gisPoi);
                entity.setPoiId(save.getId());
            }

            return entity;
        } catch (Exception e) {
            record.setError("新增工程记录异常 - " + e.getMessage());
            return null;
        }
    }

    private void setLngLat(SporadicProjectExcelDTO bean) {
        String address = "深圳市" + bean.getDistrict() + bean.getStreet() + bean.getVillage() + bean.getAddress();
        String location = gaoDeMapUtil.getLngLat(address);
        if (StringUtils.isNotEmpty(location)) {
            String[] locs = location.split(",");
            bean.setLng(new BigDecimal(locs[0]));
            bean.setLat(new BigDecimal(locs[1]));
        }
    }

    public List<SporadicProjectRespDTO> mobileSelect() {

        SporadicProjectMobileQueryDTO bean = new SporadicProjectMobileQueryDTO();

        List<SporadicProjectRespDTO> list = sporadicProjectRepository.mobileIndex(bean);
        return list;
    }

    @Override
    public Page<SporadicProjectRespDTO> mobileIndex(SporadicProjectMobileQueryDTO bean) {

        SporadicProjectQueryDTO dto = new SporadicProjectQueryDTO();
        BeanUtils.copyProperties(bean, dto);

        UtilsRegion region = userUtil.new UtilsRegion();
        region.setRegionPid(dto.getRegionPid());
        region.setRegionId(dto.getRegionId());
        region.setRegionCid(dto.getRegionCid());
        UserUtilsProjectQueryDTO userProjectQueryDTO = userUtil.createUserProjectDto(region).orElse(null);

        if (userProjectQueryDTO == null) {
            return Page.empty(PageRequest.of(bean.getPage(), bean.getSize()));
        }

        BeanUtils.copyProperties(userProjectQueryDTO, bean);


        Page<SporadicProjectRespDTO> page = sporadicProjectRepository.mobileIndex(bean,
                PageRequest.of(bean.getPage(), bean.getSize()));

        for (SporadicProjectRespDTO respDTO : page.getContent()) {
            delData(respDTO);
            //查询备注列表
            respDTO.setMemoRespDTOList(sporadicProjectMemoService.findByProjectId(respDTO.getId()));
        }

        return page;
    }





    private void delData(SporadicProjectRespDTO dto){
        Integer flow = dto.getFlow();
        String orderId = dto.getOrderId();
        if (flow.equals(MonitorFlowEnum.TYPE2.getType())){
            dto.setInstallStatus(1);
            //查询预约时间
            MonitorFlow monitorFlow = monitorFlowService.findByOrderIdAndFlow(orderId, MonitorFlowEnum.TYPE1.getType());
            if (monitorFlow!=null){
                ScShowDTO scShowDTO = new ScShowDTO();
                scShowDTO.setId(monitorFlow.getId());
                MonitorFlowRespDTO flowRespDTO = monitorFlowService.show(scShowDTO);
                if (flowRespDTO!=null){
                    dto.setReservationTime(flowRespDTO.getReservationTime());
                }
            }


        }else if (flow.equals(MonitorFlowEnum.TYPE3.getType())){

            //查询预约时间
            MonitorFlow monitorFlow = monitorFlowService.findByOrderIdAndFlow(orderId, MonitorFlowEnum.TYPE2.getType());
            if (monitorFlow!=null){
                ScShowDTO scShowDTO = new ScShowDTO();
                scShowDTO.setId(monitorFlow.getId());
                MonitorFlowRespDTO flowRespDTO = monitorFlowService.show(scShowDTO);
                if (flowRespDTO!=null){
                    dto.setReservationTime(flowRespDTO.getReservationTime());
                }
            }


            dto.setInstallStatus(2);
        }else if (flow>MonitorFlowEnum.TYPE3.getType()){
            dto.setInstallStatus(3);
        }

        if (flow.equals(MonitorFlowEnum.TYPE7.getType())){
            //查询预约时间
            MonitorFlow monitorFlow = monitorFlowService.findByOrderIdAndFlow(orderId, MonitorFlowEnum.TYPE6.getType());
            if (monitorFlow!=null){
                ScShowDTO scShowDTO = new ScShowDTO();
                scShowDTO.setId(monitorFlow.getId());
                MonitorFlowRespDTO flowRespDTO = monitorFlowService.show(scShowDTO);
                if (flowRespDTO!=null){
                    dto.setReservationTime(flowRespDTO.getReservationTime());
                }
            }

            dto.setRecycleStatus(1);
        }else if (flow>MonitorFlowEnum.TYPE7.getType()){
            dto.setRecycleStatus(2);
        }

    }





    /**
     * 自动更新施工状态
     */
    @Override
    @Transactional
    public void autoUpdateStatus() {

        Date yesterday = DateUtil.yesterday();
        Date today = new Date();

        // 查询需要开始施工的项目
        List<SporadicProject> needToStart = sporadicProjectRepository.findByStartAtLessThanAndStatus(today, 0);
        // 查询需要结束施工的项目
        List<SporadicProject> needToEnd = sporadicProjectRepository.findByEndAtLessThanAndStatus(yesterday, 1);

        for (SporadicProject sporadicProject : needToStart) {
            sporadicProject.setStatus(1);
        }
        sporadicProjectRepository.saveAll(needToStart);

        // 需要结束的项目
        for (SporadicProject sporadicProject : needToEnd) {
            try {
                closeProjectStatus(sporadicProject);
            } catch (Exception e) {
                e.printStackTrace();
                XxlJobHelper.log("关闭施工状态失败！工程id：{},错误原因:{}", sporadicProject.getId(), e.getMessage());
            }

        }

    }


    /**
     * 关闭施工状态
     */
    private void closeProjectStatus(SporadicProject sporadicProject){
        // 查询工单
        MonitorOrder monitorOrder = monitorOrderService.findByProjectId(sporadicProject.getId());

            if (monitorOrder != null) {

                // 查询当前环节
                MonitorFlow monitorFlow = monitorFlowService.findByOrderIdAndFlow(monitorOrder.getId(),
                        monitorOrder.getFlow());

                if (monitorOrder.getFlow().equals(MonitorFlowEnum.TYPE5.getType())) {

                    // 如果当前处于施工完成状态
                    MonitorFlowUpdateDTO updateDTO = new MonitorFlowUpdateDTO();
                    updateDTO.setId(monitorFlow.getId());
                    updateDTO.setState(1);
                    monitorFlowService.update(updateDTO);
                } else {
                    //如果未走到施工完成状态，也需要施工完成
                    if (monitorOrder.getFlow()<MonitorFlowEnum.TYPE5.getType()){

                        //创建施工完成节点
                        MonitorFlowCreateDTO flowCreateDTO = new MonitorFlowCreateDTO();
                        flowCreateDTO.setFlow(MonitorFlowEnum.TYPE5.getType());
                        flowCreateDTO.setOrderId(monitorOrder.getId());
                        flowCreateDTO.setState(0);
                        flowCreateDTO.setProjectId(monitorOrder.getProjectId());
                        MonitorFlow monitorFlowType5 = monitorFlowService.create(flowCreateDTO);

                        //如果未安装，直接结束监管
                        if (monitorOrder.getFlow()<=MonitorFlowEnum.TYPE3.getType()){
                            monitorFlowType5.setState(1);
                            monitorFlowType5.setFinishTime(new Date());
                            monitorFlowRepository.save(monitorFlowType5);

                            //创建工单完结的节点
                            MonitorFlowCreateDTO finishFlowDTO = new MonitorFlowCreateDTO();
                            finishFlowDTO.setFlow(MonitorFlowEnum.TYPE9.getType());
                            finishFlowDTO.setOrderId(monitorOrder.getId());
                            finishFlowDTO.setState(1);
                            finishFlowDTO.setFinishTime(new Date());
                            finishFlowDTO.setProjectId(monitorOrder.getProjectId());
                            MonitorFlow finishFlow = monitorFlowService.create(finishFlowDTO);

                            monitorOrder.setFlow(finishFlow.getFlow());
                            monitorOrder.setFlowId(finishFlow.getId());
                            monitorOrderRepository.save(monitorOrder);

                        }else {

                            //设置当前节点为施工完成
                            monitorOrder.setFlow(monitorFlowType5.getFlow());
                            monitorOrder.setFlowId(monitorFlowType5.getId());
                            monitorOrderRepository.save(monitorOrder);

                            //如果已安装了就要继续走流程
                            MonitorFlowUpdateDTO updateDTO = new MonitorFlowUpdateDTO();
                            updateDTO.setId(monitorFlowType5.getId());
                            updateDTO.setState(1);
                            monitorFlowService.update(updateDTO);
                        }

                        //删除当前节点
                        monitorFlowRepository.delete(monitorFlow);

                    }
                }

            }
            // 修改工程状态为施工完成
            sporadicProject.setStatus(2);
            sporadicProjectRepository.save(sporadicProject);
    }


    /**
     * 获取用户对应的项目列表
     */
    public List<SporadicProjectRespDTO> getUserProjectList(UtilsRegion region, SporadicProjectQueryDTO dto) {

        UserUtilsProjectQueryDTO queryDTO = userUtil.createUserProjectDto(region).orElse(null);

        if (queryDTO == null) {
            return new ArrayList<>();
        }

        SporadicProjectMobileQueryDTO projectQueryDTO = new SporadicProjectMobileQueryDTO();

        if (dto != null) {
            BeanUtils.copyProperties(dto, projectQueryDTO);
        }

        BeanUtils.copyProperties(queryDTO, projectQueryDTO);

        List<SporadicProjectRespDTO> sporadicProjects = sporadicProjectRepository.queryProjects(projectQueryDTO);

        return sporadicProjects;
    }

    /**
     * 获取用户对应的项目列表
     */
    public List<SporadicProjectRespDTO> getUserProjectList(SporadicProjectQueryDTO dto) {
        return getUserProjectList(userUtil.new UtilsRegion(), dto);
    }

    /**
     * 获取用户对应的项目列表
     */
    public List<SporadicProjectRespDTO> getUserProjectList() {
        return getUserProjectList(userUtil.new UtilsRegion(), null);
    }

    @Override
    public List<SporadicProjectRegionStatisticsRespDTO> getRegionStatistics(
            SporadicProjectQueryDTO bean) {

        String organizationId = XsgcContext.getOrganizationId();

        String regionId = bean.getRegionId();
        String regionCid = bean.getRegionCid();
        String regionPid = bean.getRegionPid();

        if (regionId == null && regionCid == null && regionPid == null) {
            return new ArrayList<>();
        }

        UserUtilsRegions userUtilsRegions = userUtil.getOrganizationRegions().orElse(null);

        if (userUtilsRegions == null) {
            return new ArrayList<>();
        }

        Set<String> regionCidSet = userUtilsRegions.getRegionCidSet();
        Set<String> regionIdSet = userUtilsRegions.getRegionIdSet();
        Set<String> regionPidSet = userUtilsRegions.getRegionPidSet();

        SporadicProjectMobileQueryDTO dto = new SporadicProjectMobileQueryDTO();
        dto.setOrganizationId(organizationId);

        List<ComRegion> comRegion = null;

        if (regionCid != null) {
            comRegion = comRegionRepository.findAllById(Arrays.asList(regionCid));
        } else if (regionId != null) {
            comRegion = comRegionRepository.findBypId(regionId);
        } else if (regionPid != null) {
            comRegion = comRegionRepository.findBypId(regionPid);
        }

        if (comRegion == null) {
            return new ArrayList<>();
        }

        if (regionCidSet != null && !regionCidSet.isEmpty()) {
            dto.setRegionCidSet(regionCidSet);
        }
        if (regionIdSet != null && !regionIdSet.isEmpty()) {
            dto.setRegionIdSet(regionIdSet);
        }
        if (regionPidSet != null && !regionPidSet.isEmpty()) {
            dto.setRegionPidSet(regionPidSet);
        }

        if (regionCid != null) {
            dto.setRegionCid(regionCid);
        }
        if (regionId != null) {
            dto.setRegionId(regionId);
        }
        if (regionPid != null) {
            dto.setRegionPid(regionPid);
        }

        List<SporadicProjectRegionStatisticsRespDTO> projectList = sporadicProjectRepository.statisticsByRegions(dto);

        Map<String, Integer> projectCountMap = new HashMap<>();

        projectList.forEach(item -> {
            projectCountMap.put(item.getRegionId(), item.getProjectCount());
        });

        List<SporadicProjectRegionStatisticsRespDTO> sporadicProjectRegionStatistics = new ArrayList<>();

        comRegion.forEach(region -> {
            SporadicProjectRegionStatisticsRespDTO item = new SporadicProjectRegionStatisticsRespDTO();

            Integer count = projectCountMap.get(region.getId());
            if (count == null) {
                count = 0;
            }

            item.setProjectCount(count);
            item.setRegionId(region.getId());
            item.setTitle(region.getTitle());
            item.setType(region.getType());
            sporadicProjectRegionStatistics.add(item);
        });

        return sporadicProjectRegionStatistics;
    }

    /**
     * 生成邀请码
     * 
     */
    @Override
    public InviteCodeRespDTO generateInviteCode(String projectId) {
        // 验证工程是否存在
        SporadicProject project = sporadicProjectRepository.findById(projectId)
                .orElseThrow(() -> new ScException("工程不存在"));

        // 验证当前用户是否有权限生成邀请码
        if (!hasInviteCodePermission(project)) {
            throw new ScException("您没有权限生成该工程的邀请码");
        }

        // 生成邀请码
        String inviteCode = InviteCodeUtil.generateInviteCode();
        String redisKey = InviteCodeUtil.getRedisKey(inviteCode);

        // 存储到Redis，设置过期时间为1周
        stringRedisTemplate.opsForValue().set(redisKey, projectId,
                InviteCodeConstant.INVITE_CODE_EXPIRE_SECONDS, TimeUnit.SECONDS);

        // 构建响应
        InviteCodeRespDTO response = new InviteCodeRespDTO();
        response.setInviteCode(inviteCode);
        response.setProjectId(projectId);
        response.setProjectName(project.getName());
        response.setGeneratedAt(new Date());
        response.setExpireSeconds(InviteCodeConstant.INVITE_CODE_EXPIRE_SECONDS);

        // 计算过期时间
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.SECOND, (int) InviteCodeConstant.INVITE_CODE_EXPIRE_SECONDS);
        response.setExpireAt(calendar.getTime());

        return response;
    }

    @Override
    public SporadicProjectRespDTO getProjectByInviteCode(String inviteCode, String projectNumber) {
        String redisKey = InviteCodeUtil.getRedisKey(inviteCode);
        String projectId = stringRedisTemplate.opsForValue().get(redisKey);

        if (StringUtils.isEmpty(projectId)) {
            throw new ScException("邀请码无效或已过期");
        }

        // 查询工程信息
        ScShowDTO showDTO = new ScShowDTO();
        showDTO.setId(projectId);
        SporadicProjectRespDTO project = show(showDTO);

        if (project == null) {
            throw new ScException("工程不存在");
        }

        // 验证备案编码是否一致
        if (!projectNumber.equals(project.getProjectNumber())) {
            throw new ScException("备案编码不匹配，无法查看该工程信息");
        }

        return project;
    }

    @Override
    @Transactional
    public SporadicProjectRespDTO bindProjectByInviteCode(String inviteCode, String projectNumber) {
        // 1. 验证邀请码是否有效
        String redisKey = InviteCodeUtil.getRedisKey(inviteCode);
        String projectId = stringRedisTemplate.opsForValue().get(redisKey);

        if (StringUtils.isEmpty(projectId)) {
            throw new ScException("邀请码无效或已过期");
        }

        // 2. 获取当前用户信息
        String userId = ScContext.getCurrentUserThrow().getId();

        // 3. 查询工程信息
        ScShowDTO showDTO = new ScShowDTO();
        showDTO.setId(projectId);
        SporadicProjectRespDTO project = show(showDTO);
        if (project == null) {
            throw new ScException("工程不存在");
        }

        // 4. 验证备案编码是否一致
        if (!projectNumber.equals(project.getProjectNumber())) {
            throw new ScException("备案编码不匹配，无法绑定该工程");
        }

        EngineeringMembers currentMember = engineeringMembersRepository.findFirstByUserId(userId);

        // 5. 验证用户角色（必须是工程成员才能绑定）
        if (currentMember == null) {
            throw new ScException("您不是工程成员，无法绑定工程");
        }

        // 6. 检查当前用户是否已经绑定过该工程
        EngineeringMembersProjectRelate existingRelate =
                engineeringMembersProjectRelateRepository.findByMemberIdAndProjectId(
                        currentMember.getId(), projectId);
        if (existingRelate != null) {
            throw new ScException("您已经绑定过该工程");
        }

        // 7. 创建工程成员关联关系
        EngineeringMembersProjectRelate relate = new EngineeringMembersProjectRelate();
        relate.setProjectId(projectId);
        relate.setMemberId(currentMember.getId());

        engineeringMembersProjectRelateRepository.save(relate);

        return project;
    }

    /**
     * 验证当前用户是否有生成邀请码的权限
     * 只有施工负责人和建设方（业主）有权限
     */
    private boolean hasInviteCodePermission(SporadicProject project) {
        String userId = ScContext.getCurrentUserThrow().getId();

        // 查询当前用户是否是该工程的工程成员
        List<EngineeringMembersProjectRelate> projectRelateList =
                engineeringMembersProjectRelateRepository.findByProjectId(project.getId());

        for (EngineeringMembersProjectRelate relate : projectRelateList) {
            EngineeringMembers member = engineeringMembersRepository.findById(relate.getMemberId()).orElse(null);
            if (member != null && userId.equals(member.getUserId())) {
                // 检查是否是施工负责人或建设方（业主）
                Integer memberType = member.getType();
                return memberType.equals(EngineeringRoleEnum.CONSTRUCTION_PARTY.getMemberType()) ||
                       memberType.equals(EngineeringRoleEnum.CONSTRUCTION_UNIT_LEADER.getMemberType());
            }
        }

        return false;
    }
}