package cn.shencom.server.service.impl;

import cn.shencom.model.EventModelTypeConfig;
import cn.shencom.model.dto.create.EventModelTypeConfigCreateDTO;
import cn.shencom.model.dto.query.EventModelTypeConfigQueryDTO;
import cn.shencom.model.dto.resp.EventModelTypeConfigRespDTO;
import cn.shencom.model.dto.update.EventModelTypeConfigUpdateDTO;
import cn.shencom.repos.EventModelTypeConfigRepository;
import cn.shencom.scloud.common.base.constant.RespCode;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.jpa.scpage.MyLink;
import cn.shencom.scloud.common.jpa.util.export.ScExport;
import cn.shencom.scloud.common.jpa.util.query.LinkUtil;
import cn.shencom.scloud.common.jpa.util.query.ScQueryUtil;
import cn.shencom.scloud.common.server.service.BaseImpl;
import cn.shencom.scloud.common.util.BeanUtil;
import cn.shencom.server.service.IEventModelTypeConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.util.*;


/**
 * 设备型号配置表 的服务实现
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Service
@Slf4j
public class EventModelTypeConfigServiceImpl extends BaseImpl implements IEventModelTypeConfigService {

    @Autowired
    private EventModelTypeConfigRepository eventModelTypeConfigRepository;

    @Override
    public Page<EventModelTypeConfigRespDTO> query(EventModelTypeConfigQueryDTO bean) {
        Map<String, MyLink> linkMap = LinkUtil.convertLink(EventModelTypeConfig.class);
        Page<EventModelTypeConfig> res = eventModelTypeConfigRepository.findAll((root, query, builder) -> {
            //用于拼接条件
            List<Predicate> ps = new ArrayList<>();

            return ScQueryUtil.getPredicate(ps, bean, linkMap, root, query, builder);
        }, PageRequest.of(bean.getPage(), bean.getSize()));
        return ScQueryUtil.handle(res, linkMap, EventModelTypeConfigRespDTO.class);
    }

    @Override
    public EventModelTypeConfigRespDTO show(ScShowDTO bean) {
        Optional<EventModelTypeConfig> option = eventModelTypeConfigRepository.findById(bean.getId());
        if (!option.isPresent()) {
            return null;
        }
        EventModelTypeConfig entity = option.get();
        Map<String, MyLink> linkMap = LinkUtil.convertLink(EventModelTypeConfig.class);
        return ScQueryUtil.handleOne(entity, linkMap, EventModelTypeConfigRespDTO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public EventModelTypeConfig create(EventModelTypeConfigCreateDTO bean) {
        EventModelTypeConfig entity = new EventModelTypeConfig();
        BeanUtil.copyProperties(bean, entity);
        return eventModelTypeConfigRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public EventModelTypeConfig update(EventModelTypeConfigUpdateDTO bean) {
        EventModelTypeConfig entity = eventModelTypeConfigRepository.findById(bean.getId())
                .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));
        BeanUtil.copyProperties(bean, entity);
        return eventModelTypeConfigRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            ids.forEach(id -> eventModelTypeConfigRepository.findById(id).ifPresent(entity -> {
                entity.setIsDeleted(1);
                entity.setDeletedAt(new Date());
                eventModelTypeConfigRepository.save(entity);
            }));
        }
    }

    @Override
    public void export(EventModelTypeConfigQueryDTO bean) {
        List<EventModelTypeConfigRespDTO> dtos = new ArrayList<>(query(bean).getContent());
        ScExport.export(bean.getCfg(), dtos, EventModelTypeConfigRespDTO.class);
    }
}