package cn.shencom.server.service.impl;

import cn.shencom.model.dto.resp.ReportRespDTO.ProjectStatisticsRespDTO;
import cn.shencom.model.dto.resp.SporadicProjectRespDTO;
import cn.shencom.repos.ContractingUnitOrganizationRelateRepository;
import cn.shencom.scloud.common.server.service.BaseImpl;
import cn.shencom.server.service.IReportService;
import cn.shencom.server.service.ISporadicProjectService;
import cn.shencom.utils.XsgcContext;
import lombok.extern.slf4j.Slf4j;

import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * 统计报表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
@Service
@Slf4j
public class ReportServiceImpl extends BaseImpl implements IReportService {

    @Autowired
    private ISporadicProjectService sporadicProjectService;

    @Autowired
    private ContractingUnitOrganizationRelateRepository contractingUnitOrganizationRelateRepository;

    @Override
    public ProjectStatisticsRespDTO getProjectStatistics() {
        log.info("开始获取统计数据");

        // 统计工程总数
        Integer totalProjectCount = 0;

        // 统计施工中工程数量
        Integer ongoingProjectCount = 0;

        List<SporadicProjectRespDTO> projectList = sporadicProjectService.getUserProjectList();

        totalProjectCount = projectList.size();

        ongoingProjectCount = (int) projectList.stream().filter(project -> project.getStatus() == 1).count();

        ProjectStatisticsRespDTO res = ProjectStatisticsRespDTO.builder()
                .totalProjectCount(totalProjectCount)
                .ongoingProjectCount(ongoingProjectCount).build();

        String organizationId = XsgcContext.getOrganizationId();

        // 在组织中的统计
        if (Strings.isNotBlank(organizationId)) {
            // 统计施工单位数量
            Integer contractingUnitCount = countContractingUnits(organizationId);
            res.setContractingUnitCount(contractingUnitCount);

            // 统计工程总金额
            BigDecimal totalAmount = projectList.stream().map(SporadicProjectRespDTO::getAmount).reduce(BigDecimal.ZERO,
                    BigDecimal::add);
            res.setTotalAmount(totalAmount);
        } else {
            // 待整改工程数量
            res.setRectifyProjectCount(0);
        }

        return res;
    };

    /**
     * 统计施工单位数量
     *
     * @return 施工单位数量
     */
    private Integer countContractingUnits(String organizationId) {

        if (organizationId == null) {
            return 0;
        }

        Integer count = contractingUnitOrganizationRelateRepository
                .findDistinctContractingUnitIdByOrganizationIdCount(organizationId);

        if (count == null) {
            return 0;
        }

        return count;
    }

}
