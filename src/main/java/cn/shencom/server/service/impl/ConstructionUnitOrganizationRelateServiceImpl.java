package cn.shencom.server.service.impl;

import cn.shencom.model.ConstructionUnitOrganizationRelate;
import cn.shencom.model.dto.create.ConstructionUnitOrganizationRelateCreateDTO;
import cn.shencom.model.dto.query.ConstructionUnitOrganizationRelateQueryDTO;
import cn.shencom.model.dto.resp.ConstructionUnitOrganizationRelateRespDTO;
import cn.shencom.model.dto.update.ConstructionUnitOrganizationRelateUpdateDTO;
import cn.shencom.repos.ConstructionUnitOrganizationRelateRepository;
import cn.shencom.scloud.common.base.constant.RespCode;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.jpa.scpage.MyLink;
import cn.shencom.scloud.common.jpa.util.export.ScExport;
import cn.shencom.scloud.common.jpa.util.query.LinkUtil;
import cn.shencom.scloud.common.jpa.util.query.ScQueryUtil;
import cn.shencom.scloud.common.server.service.BaseImpl;
import cn.shencom.scloud.common.util.BeanUtil;
import cn.shencom.server.service.IConstructionUnitOrganizationRelateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.util.*;

/**
 * 施工单位组织关联表 的服务实现
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Service
@Slf4j
public class ConstructionUnitOrganizationRelateServiceImpl extends BaseImpl
        implements IConstructionUnitOrganizationRelateService {

    @Autowired
    private ConstructionUnitOrganizationRelateRepository constructionUnitOrganizationRelateRepository;

    @Override
    public Page<ConstructionUnitOrganizationRelateRespDTO> query(ConstructionUnitOrganizationRelateQueryDTO bean) {
        Map<String, MyLink> linkMap = LinkUtil.convertLink(ConstructionUnitOrganizationRelate.class);
        Page<ConstructionUnitOrganizationRelate> res = constructionUnitOrganizationRelateRepository
                .findAll((root, query, builder) -> {
                    // 用于拼接条件
                    List<Predicate> ps = new ArrayList<>();

                    return ScQueryUtil.getPredicate(ps, bean, linkMap, root, query, builder);
                }, PageRequest.of(bean.getPage(), bean.getSize()));
        return ScQueryUtil.handle(res, linkMap, ConstructionUnitOrganizationRelateRespDTO.class);
    }

    @Override
    public ConstructionUnitOrganizationRelateRespDTO show(ScShowDTO bean) {
        Optional<ConstructionUnitOrganizationRelate> option = constructionUnitOrganizationRelateRepository
                .findById(bean.getId());
        if (!option.isPresent()) {
            return null;
        }
        ConstructionUnitOrganizationRelate entity = option.get();
        Map<String, MyLink> linkMap = LinkUtil.convertLink(ConstructionUnitOrganizationRelate.class);
        return ScQueryUtil.handleOne(entity, linkMap, ConstructionUnitOrganizationRelateRespDTO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ConstructionUnitOrganizationRelate create(ConstructionUnitOrganizationRelateCreateDTO bean) {
        ConstructionUnitOrganizationRelate entity = new ConstructionUnitOrganizationRelate();
        BeanUtil.copyProperties(bean, entity);
        return constructionUnitOrganizationRelateRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ConstructionUnitOrganizationRelate update(ConstructionUnitOrganizationRelateUpdateDTO bean) {
        ConstructionUnitOrganizationRelate entity = constructionUnitOrganizationRelateRepository.findById(bean.getId())
                .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));
        BeanUtil.copyProperties(bean, entity);
        return constructionUnitOrganizationRelateRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            ids.forEach(id -> constructionUnitOrganizationRelateRepository.findById(id).ifPresent(entity -> {
                entity.setIsDeleted(1);
                entity.setDeletedAt(new Date());
                constructionUnitOrganizationRelateRepository.save(entity);
            }));
        }
    }

    @Override
    public void export(ConstructionUnitOrganizationRelateQueryDTO bean) {
        List<ConstructionUnitOrganizationRelateRespDTO> dtos = new ArrayList<>(query(bean).getContent());
        ScExport.export(bean.getCfg(), dtos, ConstructionUnitOrganizationRelateRespDTO.class);
    }
}