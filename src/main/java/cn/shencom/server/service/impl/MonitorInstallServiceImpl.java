package cn.shencom.server.service.impl;

import cn.shencom.model.MonitorInstall;
import cn.shencom.model.dto.create.MonitorInstallCreateDTO;
import cn.shencom.model.dto.query.MonitorInstallQueryDTO;
import cn.shencom.model.dto.resp.MonitorInstallRespDTO;
import cn.shencom.model.dto.update.MonitorInstallUpdateDTO;
import cn.shencom.repos.MonitorInstallRepository;
import cn.shencom.scloud.common.base.constant.RespCode;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.jpa.scpage.MyLink;
import cn.shencom.scloud.common.jpa.util.export.ScExport;
import cn.shencom.scloud.common.jpa.util.query.LinkUtil;
import cn.shencom.scloud.common.jpa.util.query.ScQueryUtil;
import cn.shencom.scloud.common.server.service.BaseImpl;
import cn.shencom.scloud.common.util.BeanUtil;
import cn.shencom.server.service.IMonitorInstallService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.util.*;


/**
 * 小散工程-监管工单流程-上门安装详情 的服务实现
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@Service
@Slf4j
public class MonitorInstallServiceImpl extends BaseImpl implements IMonitorInstallService {

    @Autowired
    private MonitorInstallRepository monitorInstallRepository;

    @Override
    public Page<MonitorInstallRespDTO> query(MonitorInstallQueryDTO bean) {
        Map<String, MyLink> linkMap = LinkUtil.convertLink(MonitorInstall.class);
        Page<MonitorInstall> res = monitorInstallRepository.findAll((root, query, builder) -> {
            //用于拼接条件
            List<Predicate> ps = new ArrayList<>();

            return ScQueryUtil.getPredicate(ps, bean, linkMap, root, query, builder);
        }, PageRequest.of(bean.getPage(), bean.getSize()));
        return ScQueryUtil.handle(res, linkMap, MonitorInstallRespDTO.class);
    }

    @Override
    public MonitorInstallRespDTO show(ScShowDTO bean) {
        Optional<MonitorInstall> option = monitorInstallRepository.findById(bean.getId());
        if (!option.isPresent()) {
            return null;
        }
        MonitorInstall entity = option.get();
        Map<String, MyLink> linkMap = LinkUtil.convertLink(MonitorInstall.class);
        return ScQueryUtil.handleOne(entity, linkMap, MonitorInstallRespDTO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public MonitorInstall create(MonitorInstallCreateDTO bean) {
        MonitorInstall entity = new MonitorInstall();
        BeanUtil.copyProperties(bean, entity);
        return monitorInstallRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public MonitorInstall update(MonitorInstallUpdateDTO bean) {
        MonitorInstall entity = monitorInstallRepository.findById(bean.getId())
                .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));
        BeanUtil.copyProperties(bean, entity);
        return monitorInstallRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            ids.forEach(id -> monitorInstallRepository.deleteById(id));
        }
    }

    @Override
    public void export(MonitorInstallQueryDTO bean) {
        List<MonitorInstallRespDTO> dtos = new ArrayList<>(query(bean).getContent());
        ScExport.export(bean.getCfg(), dtos, MonitorInstallRespDTO.class);
    }
}