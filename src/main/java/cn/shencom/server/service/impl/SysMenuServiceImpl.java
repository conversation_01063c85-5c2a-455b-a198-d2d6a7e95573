package cn.shencom.server.service.impl;

import cn.shencom.model.SysMenu;
import cn.shencom.model.dto.vo.SysMenuVO;
import cn.shencom.repos.SysMenuRepository;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.jpa.scpage.MyLink;
import cn.shencom.scloud.common.jpa.scpage.ScQuery;
import cn.shencom.scloud.common.jpa.scpage.ScSort;
import cn.shencom.scloud.common.jpa.util.query.LinkUtil;
import cn.shencom.scloud.common.jpa.util.query.ScQueryUtil;
import cn.shencom.scloud.common.server.service.BaseImpl;
import cn.shencom.scloud.common.util.BeanUtil;
import cn.shencom.scloud.common.util.TreeUtil;
import cn.shencom.scloud.security.core.ScContext;
import cn.shencom.scloud.security.core.SecurityUser;
import cn.shencom.server.service.ISysMenuService;
import cn.shencom.utils.XsgcContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


import javax.persistence.criteria.Join;
import javax.persistence.criteria.Order;
import javax.persistence.criteria.Predicate;
import java.util.*;

/**
 * SysMenu的服务接口的实现类
 *
 * <AUTHOR>
@Service
@Slf4j
public class SysMenuServiceImpl extends BaseImpl implements ISysMenuService {

    @Autowired
    private SysMenuRepository sysMenuRepository;


    public Result<List<SysMenuVO>> getMenuTree(SysMenu bean) {
        List<ScSort> sorts = bean.getSorts();
        List<ScQuery> queries = bean.getQuery();

        Map<String, MyLink> linkMap = LinkUtil.convertLink(bean.getClass());
        Map<String, Join<Object, Object>> joinMap = new HashMap<>();

        SecurityUser currentUser = ScContext.getCurrentUserThrow();
        String organizationId = XsgcContext.getOrganizationId();

        //用户拥有权限的菜单
        Integer type = currentUser.getType();
//        //2019.8.13修改type类型为string
        Set<String> menuIds;
        if (type != null && type == -1) {
            menuIds = sysMenuRepository.findAllMenuIds();
        } else {

            //如果组织id不为空
            if (organizationId!=null){
                menuIds = sysMenuRepository.findMenuIdsByUserIdAndOrganization(currentUser.getId(),organizationId);
            }else {
                menuIds = sysMenuRepository.findMenuIdsByUserId(currentUser.getId());
            }
        }

        List<SysMenu> res = sysMenuRepository.findAll((root, query, builder) -> {

            //用于拼接条件
            List<Predicate> ps = new ArrayList<>();

            Predicate predicate = builder.isNull(root.get("deletedAt"));
            ps.add(predicate);
            ps.add(builder.equal(root.get("active"), 1));
            //查询条件
            if (queries != null && queries.size() > 0) {
                Predicate constructPredicate = ScQueryUtil
                        .constructQuery(queries, root, builder, linkMap, joinMap);
                if (constructPredicate != null) {
                    ps.add(constructPredicate);
                }
            }
            if (menuIds != null && menuIds.size() > 0) {
                ps.add(root.get("id").in(menuIds));
            } else {
                ps.add(root.get("id").in(""));
            }
//            }

            //排序
            List<Order> orders = ScQueryUtil.constructSort(sorts, root, builder, linkMap, joinMap);
            if (orders != null && orders.size() > 0) {
                query.orderBy(orders);
            }

            if (ps.size() > 0) {
                query
                        .where(builder.and(ps.toArray(new Predicate[]{})));
            }

            return query.getRestriction();

        });

        dealWithTree(res, linkMap);

        List<SysMenuVO> sysMenuList = BeanUtil.toBeanList(res, SysMenuVO.class, "children", "parent");

        //构建树形
        List<SysMenuVO> sysMenus = TreeUtil.buildTreeByPid(sysMenuList);

        //计算子节点数
        calSysMenuVONumOfNode(sysMenus);

        return success(sysMenus);
    }

    //处理数据
    private void dealWithTree(List<SysMenu> res, Map<String, MyLink> linkMap) {
        ScQueryUtil.dealWith(res, linkMap);
    }

    private void calSysMenuVONumOfNode(List<SysMenuVO> allMenus) {
        for (SysMenuVO menu : allMenus) {
            calSysMenuVOOfNode(menu, menu);
        }
    }

    private void calSysMenuVOOfNode(SysMenuVO org, SysMenuVO current) {
        if (current != null) {
            List<SysMenuVO> children = current.getChildren();
            if (children != null) {
                org.setNum(children.size() + org.getNum());
                for (SysMenuVO child : children) {
                    calSysMenuVOOfNode(org, child);
                    calSysMenuVOOfNode(child, child);
                }
            }
        }
    }

}