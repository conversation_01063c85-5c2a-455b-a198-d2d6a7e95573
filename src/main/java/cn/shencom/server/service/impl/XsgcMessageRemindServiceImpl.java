package cn.shencom.server.service.impl;

import cn.shencom.enums.BusinessMemberTypeEnum;
import cn.shencom.enums.MessageRemindEnum;
import cn.shencom.model.*;
import cn.shencom.model.SporadicProject;
import cn.shencom.model.XsgcMessageRemind;
import cn.shencom.model.dto.SimpleMemberDTO;
import cn.shencom.model.dto.XsgcMessageRemindDTO;
import cn.shencom.model.dto.create.XsgcMessageRemindCreateDTO;
import cn.shencom.model.dto.query.XsgcMessageRemindQueryDTO;
import cn.shencom.model.dto.resp.AiotEventRespDTO;
import cn.shencom.model.dto.resp.XsgcMessageRemindRespDTO;
import cn.shencom.model.dto.update.XsgcMessageRemindUpdateDTO;
import cn.shencom.repos.*;
import cn.shencom.scloud.common.base.constant.RespCode;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.jpa.scpage.MyLink;
import cn.shencom.scloud.common.jpa.util.export.ScExport;
import cn.shencom.scloud.common.jpa.util.query.LinkUtil;
import cn.shencom.scloud.common.jpa.util.query.ScQueryUtil;
import cn.shencom.scloud.common.server.service.BaseImpl;
import cn.shencom.scloud.common.util.BeanUtil;
import cn.shencom.scloud.common.util.DateUtil;
import cn.shencom.scloud.security.core.ScContext;
import cn.shencom.server.service.IAiotEventService;
import cn.shencom.server.service.IMonitorFlowService;
import cn.shencom.server.service.IXsgcMessageRemindService;
import cn.shencom.utils.XsgcContext;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.util.*;


/**
 * 小散工程-消息提醒表 的服务实现
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Service
@Slf4j
public class XsgcMessageRemindServiceImpl extends BaseImpl implements IXsgcMessageRemindService {

    @Autowired
    private XsgcMessageRemindRepository xsgcMessageRemindRepository;

    @Autowired
    private EngineeringMembersProjectRelateRepository engineeringMembersProjectRelateRepository;

    @Autowired
    private XsgcBusinessMembersRepository xsgcBusinessMembersRepository;

    @Autowired
    private FnRmsv3MembersTypeRelateRepository rmsv3MembersTypeRelateRepository;

    @Autowired
    private SporadicProjectRepository sporadicProjectRepository;

    @Autowired
    private MonitorOrderRepository monitorOrderRepository;

    @Autowired
    private IAiotEventService aiotEventService;

    @Autowired
    private IMonitorFlowService monitorFlowService;



    private static final Set<Integer> MONITOR_MESSAGE_TYPE;

    /**
     * 需要通知工程关联的施工负责人 、建设方（业主） 的消息类型
     */
    private static final Set<Integer> TYPE_SET_ENGINEER_USER;
    /**
     * 需要通知 业务人员  的消息类型
     */
    private static final Set<Integer> TYPE_SET_BUSINESS_USER;
    /**
     * 需要通知 安装人员  的消息类型
     */
    private static final Set<Integer> TYPE_SET_INSTALL_USER;

    /**
     * 需要通知组织人员  的消息类型
     */
    private static final Set<Integer> TYPE_SET_ORGANIZATION_USER;

    /**
     * 初始化
     */
    static {
        TYPE_SET_ENGINEER_USER = new HashSet<>(Arrays.asList(1,2,6,7,8));
        TYPE_SET_BUSINESS_USER = new HashSet<>(Arrays.asList(1,2));
        TYPE_SET_INSTALL_USER = new HashSet<>(Arrays.asList(3,4,5));
        TYPE_SET_ORGANIZATION_USER = new HashSet<>(Arrays.asList(6,7));


        MONITOR_MESSAGE_TYPE =new HashSet<>(Arrays.asList(1,2,3,4,5,6,7));
    }

    @Override
    public Page<XsgcMessageRemindRespDTO> query(XsgcMessageRemindQueryDTO bean) {

        String organizationId = XsgcContext.getOrganizationId();

        String userId = ScContext.getCurrentUserThrow().getId();
        Integer queryType = bean.getQueryType();

        Map<String, MyLink> linkMap = LinkUtil.convertLink(XsgcMessageRemind.class);
        Page<XsgcMessageRemind> res = xsgcMessageRemindRepository.findAll((root, query, builder) -> {
            //用于拼接条件
            List<Predicate> ps = new ArrayList<>();

            if (queryType!=null){
                if (queryType==1){
                    ps.add(builder.in(root.get("type")).value(MONITOR_MESSAGE_TYPE));
                }else if (queryType==2){
                    ps.add(builder.equal(root.get("type"),MessageRemindEnum.TYPE8.getType()));
                }
            }

            if (organizationId!=null){
                ps.add(builder.in(root.get("organizationId")).value(organizationId));
            }

            //查询当前用户的消息
            ps.add(builder.equal(root.get("userId"),userId));

            return ScQueryUtil.getPredicate(ps, bean, linkMap, root, query, builder);
        }, PageRequest.of(bean.getPage(), bean.getSize()));
        return ScQueryUtil.handle(res, linkMap, XsgcMessageRemindRespDTO.class,this::delData);
    }

    @Override
    public XsgcMessageRemindRespDTO show(ScShowDTO bean) {
        Optional<XsgcMessageRemind> option = xsgcMessageRemindRepository.findById(bean.getId());
        if (!option.isPresent()) {
            return null;
        }
        XsgcMessageRemind entity = option.get();
        Map<String, MyLink> linkMap = LinkUtil.convertLink(XsgcMessageRemind.class);
        return ScQueryUtil.handleOne(entity, linkMap, XsgcMessageRemindRespDTO.class,this::delData);
    }



    private void delData(XsgcMessageRemind remind,XsgcMessageRemindRespDTO remindRespDTO){
        MonitorOrder order = monitorOrderRepository.findByProjectId(remind.getProjectId());
        if (order!=null){
            remindRespDTO.setOrderId(order.getId());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public XsgcMessageRemind create(XsgcMessageRemindCreateDTO bean) {
        XsgcMessageRemind entity = new XsgcMessageRemind();
        BeanUtil.copyProperties(bean, entity);
        return xsgcMessageRemindRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public XsgcMessageRemind update(XsgcMessageRemindUpdateDTO bean) {
        XsgcMessageRemind entity = xsgcMessageRemindRepository.findById(bean.getId())
                .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));

        //如果是已办的消息提醒，不修改更新时间
        Date date ;
        if (entity.getResolveFlag()==2){
            date=entity.getUpdatedAt();
        }else {
            date =new Date();
        }
        xsgcMessageRemindRepository.updateStatus(entity.getId(),bean.getStatus(),date);
        return entity;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            ids.forEach(id -> xsgcMessageRemindRepository.findById(id).ifPresent(entity -> {
                entity.setIsDeleted(1);
                entity.setDeletedAt(new Date());
                xsgcMessageRemindRepository.save(entity);
            }));
        }
    }

    @Override
    public void export(XsgcMessageRemindQueryDTO bean) {
        List<XsgcMessageRemindRespDTO> dtos = new ArrayList<>(query(bean).getContent());
        ScExport.export(bean.getCfg(), dtos, XsgcMessageRemindRespDTO.class);
    }


    @Override
    public void createRemind(XsgcMessageRemindDTO bean) {
        int type = bean.getType();
        String projectId =bean.getProjectId();
        String flowId =bean.getFlowId();
        String eventId =bean.getEventId();
        Date reservationTime = bean.getReservationTime();

        //查询当前工程
        Optional<SporadicProject> sporadicProjectOptional = sporadicProjectRepository.findById(projectId);
        if (!sporadicProjectOptional.isPresent()){
            return;
        }
        SporadicProject sporadicProject = sporadicProjectOptional.get();
        String organizationId = sporadicProject.getOrganizationId();


        Set<String>  remindUserIdList = getUserIds(projectId,type);
        if (remindUserIdList.isEmpty()){
            return;
        }

        AiotEventRespDTO aiotEvent;
        if (eventId!=null){
            ScShowDTO scShowDTO = new ScShowDTO();
            scShowDTO.setId(eventId);
            aiotEvent = aiotEventService.show(scShowDTO);
        }else {
            aiotEvent=new AiotEventRespDTO();
        }



        List<XsgcMessageRemind> remindList =new ArrayList<>();
        if (type==MessageRemindEnum.TYPE1.getType()){
            //预约安装提醒
            //通知 施工负责人 、建设方（业主）；安装工人

            String content = String.format("工程【%s】未接入监管",sporadicProject.getName());
            for (String userId : remindUserIdList) {
                XsgcMessageRemind remind = new XsgcMessageRemind();
                remind.setType(type);
                remind.setProjectId(projectId);
                remind.setRelateId(flowId);
                remind.setOrganizationId(organizationId);
                remind.setUserId(userId);
                remind.setContent(content);
                remindList.add(remind);
            }

        }else if (type==MessageRemindEnum.TYPE2.getType()){
            //预约回收提醒
            //通知 施工负责人 、建设方（业主）；安装工人
            String content = String.format("工程【%s】已结束",sporadicProject.getName());
            for (String userId : remindUserIdList) {
                XsgcMessageRemind remind = new XsgcMessageRemind();
                remind.setType(type);
                remind.setProjectId(projectId);
                remind.setRelateId(flowId);
                remind.setOrganizationId(organizationId);
                remind.setUserId(userId);
                remind.setContent(content);
                remindList.add(remind);
            }

        }else if (type==MessageRemindEnum.TYPE3.getType()){
            // 现场勘察提醒
            //通知 安装工人

            String content = String.format("工程【%s】期望上门时间%s",sporadicProject.getName(), DateUtil.defaultTime(reservationTime));
            for (String userId : remindUserIdList) {
                XsgcMessageRemind remind = new XsgcMessageRemind();
                remind.setType(type);
                remind.setProjectId(projectId);
                remind.setRelateId(flowId);
                remind.setOrganizationId(organizationId);
                remind.setUserId(userId);
                remind.setContent(content);
                remindList.add(remind);
            }

        }else if (type==MessageRemindEnum.TYPE4.getType()){
            //通知 安装工人
            String content = String.format("工程【%s】预约安装时间%s",sporadicProject.getName(),DateUtil.defaultTime(reservationTime));
            for (String userId : remindUserIdList) {
                XsgcMessageRemind remind = new XsgcMessageRemind();
                remind.setType(type);
                remind.setProjectId(projectId);
                remind.setRelateId(flowId);
                remind.setOrganizationId(organizationId);
                remind.setUserId(userId);
                remind.setContent(content);
                remindList.add(remind);
            }

        }else if (type==MessageRemindEnum.TYPE5.getType()){
            //上门安装提醒
            //通知 安装工人
            String content = String.format("工程【%s】预约回收时间%s",sporadicProject.getName(),DateUtil.defaultTime(reservationTime));
            for (String userId : remindUserIdList) {
                XsgcMessageRemind remind = new XsgcMessageRemind();
                remind.setType(type);
                remind.setProjectId(projectId);
                remind.setRelateId(flowId);
                remind.setOrganizationId(organizationId);
                remind.setUserId(userId);
                remind.setContent(content);
                remindList.add(remind);
            }

        }else if (type==MessageRemindEnum.TYPE6.getType()){
            //已接入监管的提醒
            //通知 该工程关联的施工负责人 、建设方（业主）；可查看该工程的组织成员，包括市/区/街道/社区/巡查人员
            String content = String.format("工程【%s】已接入监管",sporadicProject.getName());
            for (String userId : remindUserIdList) {
                XsgcMessageRemind remind = new XsgcMessageRemind();
                remind.setType(type);
                remind.setProjectId(projectId);
                remind.setRelateId(flowId);
                remind.setOrganizationId(organizationId);
                remind.setUserId(userId);
                remind.setContent(content);
                remindList.add(remind);
            }

        }else if (type==MessageRemindEnum.TYPE7.getType()){
            //结束监管的提醒
            //通知 该工程关联的施工负责人 、建设方（业主）；可查看该工程的组织成员，包括市/区/街道/社区/巡查人员
            String content = String.format("工程【%s】已结束监管",sporadicProject.getName());
            for (String userId : remindUserIdList) {
                XsgcMessageRemind remind = new XsgcMessageRemind();
                remind.setType(type);
                remind.setProjectId(projectId);
                remind.setRelateId(flowId);
                remind.setOrganizationId(organizationId);
                remind.setUserId(userId);
                remind.setContent(content);
                remindList.add(remind);
            }

        }else if (type==MessageRemindEnum.TYPE8.getType()){
            //违规告警的提醒
            //通知 该工程关联的施工负责人 、建设方（业主）
            String content = "违规告警提醒";
            AiotEventRespDTO  event = new AiotEventRespDTO();
            event.setTypeName(aiotEvent.getTypeName());
            event.setEventAt(aiotEvent.getEventAt());
            event.setProjectName(aiotEvent.getProjectName());
            event.setEventNo(aiotEvent.getEventNo());
            event.setPics(aiotEvent.getPics());
            String memo = JSON.toJSONString(event);
            for (String userId : remindUserIdList) {
                XsgcMessageRemind remind = new XsgcMessageRemind();
                remind.setType(type);
                remind.setProjectId(projectId);
                remind.setRelateId(eventId);
                remind.setOrganizationId(organizationId);
                remind.setUserId(userId);
                remind.setContent(content);
                /**
                 * 事件详情
                 */
                remind.setMemo(memo);

                remindList.add(remind);
            }

        }else {
            return;
        }

        xsgcMessageRemindRepository.saveAll(remindList);
    }



    private Set<String> getUserIds(String projectId,int type){
        Set<String> userIds = new HashSet<>();
        if (TYPE_SET_ENGINEER_USER.contains(type)){
            userIds.addAll(getAllEngineerUserIds(projectId));
        }
        if (TYPE_SET_BUSINESS_USER.contains(type)){
            userIds.addAll(getAllBusinessUser());
        }
        if (TYPE_SET_INSTALL_USER.contains(type)){
            userIds.addAll(getAllInstallWorker());
        }
        if (TYPE_SET_ORGANIZATION_USER.contains(type)){
            userIds.addAll(getAllOrganizationUserIds(projectId));
        }
        return userIds;
    }

    /**
     * 获取全部安装工人
     * @return
     */
    private Set<String> getAllInstallWorker(){
        return xsgcBusinessMembersRepository.findAllUserIdByType(BusinessMemberTypeEnum.INSTALLER.getCode());
    }



    /**
     * 获取全部业务人员
     * @return
     */
    private Set<String> getAllBusinessUser(){
        return xsgcBusinessMembersRepository.findAllUserId();
    }




    /**
     * 获取当前工程关联的施工负责人 、建设方（业主）
     */
    private Set<String> getAllEngineerUserIds(String projectId){
        return engineeringMembersProjectRelateRepository.findUserIdsByProjectId(projectId);
    }

    /**
     * 获取可以查看当前工程关联的  市/区/街道/社区/巡查人员
     */
    private Set<String> getAllOrganizationUserIds(String projectId){
        Set<String> userIds = new HashSet<>();
        List<SimpleMemberDTO> dtoList = rmsv3MembersTypeRelateRepository.findAllUserIdByProjectId(projectId);
        dtoList.forEach( d -> userIds.add(d.getUserId()));
        return userIds;
    }


    @Override
    @Transactional
    public void completeRemind(String relateId, String userId) {
        xsgcMessageRemindRepository.updateResolveFlagByUserId(2,relateId,userId);
        xsgcMessageRemindRepository.updateResolveFlagAndUserIdNotEQ(1,relateId,userId);
    }
}