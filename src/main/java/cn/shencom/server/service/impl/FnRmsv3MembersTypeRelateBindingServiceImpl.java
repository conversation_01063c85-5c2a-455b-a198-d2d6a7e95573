package cn.shencom.server.service.impl;

import cn.shencom.model.FnRmsv3MembersTypeRelateBinding;
import cn.shencom.model.dto.create.FnRmsv3MembersTypeRelateBindingCreateDTO;
import cn.shencom.model.dto.query.FnRmsv3MembersTypeRelateBindingQueryDTO;
import cn.shencom.model.dto.resp.FnRmsv3MembersTypeRelateBindingRespDTO;
import cn.shencom.model.dto.update.FnRmsv3MembersTypeRelateBindingUpdateDTO;
import cn.shencom.repos.FnRmsv3MembersTypeRelateBindingRepository;
import cn.shencom.scloud.common.base.constant.RespCode;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.jpa.scpage.MyLink;
import cn.shencom.scloud.common.jpa.util.export.ScExport;
import cn.shencom.scloud.common.jpa.util.query.LinkUtil;
import cn.shencom.scloud.common.jpa.util.query.ScQueryUtil;
import cn.shencom.scloud.common.server.service.BaseImpl;
import cn.shencom.scloud.common.util.BeanUtil;
import cn.shencom.server.service.IFnRmsv3MembersTypeRelateBindingService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.util.*;


/**
 * 小散工程-组织团队成员区域关联关系 的服务实现
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Service
@Slf4j
public class FnRmsv3MembersTypeRelateBindingServiceImpl extends BaseImpl implements IFnRmsv3MembersTypeRelateBindingService {

    @Autowired
    private FnRmsv3MembersTypeRelateBindingRepository fnRmsv3MembersTypeRelateBindingRepository;

    @Override
    public Page<FnRmsv3MembersTypeRelateBindingRespDTO> query(FnRmsv3MembersTypeRelateBindingQueryDTO bean) {
        Map<String, MyLink> linkMap = LinkUtil.convertLink(FnRmsv3MembersTypeRelateBinding.class);
        Page<FnRmsv3MembersTypeRelateBinding> res = fnRmsv3MembersTypeRelateBindingRepository.findAll((root, query, builder) -> {
            //用于拼接条件
            List<Predicate> ps = new ArrayList<>();

            return ScQueryUtil.getPredicate(ps, bean, linkMap, root, query, builder);
        }, PageRequest.of(bean.getPage(), bean.getSize()));
        return ScQueryUtil.handle(res, linkMap, FnRmsv3MembersTypeRelateBindingRespDTO.class);
    }

    @Override
    public FnRmsv3MembersTypeRelateBindingRespDTO show(ScShowDTO bean) {
        Optional<FnRmsv3MembersTypeRelateBinding> option = fnRmsv3MembersTypeRelateBindingRepository.findById(bean.getId());
        if (!option.isPresent()) {
            return null;
        }
        FnRmsv3MembersTypeRelateBinding entity = option.get();
        Map<String, MyLink> linkMap = LinkUtil.convertLink(FnRmsv3MembersTypeRelateBinding.class);
        return ScQueryUtil.handleOne(entity, linkMap, FnRmsv3MembersTypeRelateBindingRespDTO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public FnRmsv3MembersTypeRelateBinding create(FnRmsv3MembersTypeRelateBindingCreateDTO bean) {
        FnRmsv3MembersTypeRelateBinding entity = new FnRmsv3MembersTypeRelateBinding();
        BeanUtil.copyProperties(bean, entity);
        return fnRmsv3MembersTypeRelateBindingRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public FnRmsv3MembersTypeRelateBinding update(FnRmsv3MembersTypeRelateBindingUpdateDTO bean) {
        FnRmsv3MembersTypeRelateBinding entity = fnRmsv3MembersTypeRelateBindingRepository.findById(bean.getId())
                .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));
        BeanUtil.copyProperties(bean, entity);
        return fnRmsv3MembersTypeRelateBindingRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            ids.forEach(id -> fnRmsv3MembersTypeRelateBindingRepository.findById(id).ifPresent(entity -> {
                entity.setIsDeleted(1);
                fnRmsv3MembersTypeRelateBindingRepository.save(entity);
            }));
        }
    }

    @Override
    public void export(FnRmsv3MembersTypeRelateBindingQueryDTO bean) {
        List<FnRmsv3MembersTypeRelateBindingRespDTO> dtos = new ArrayList<>(query(bean).getContent());
        ScExport.export(bean.getCfg(), dtos, FnRmsv3MembersTypeRelateBindingRespDTO.class);
    }
}