package cn.shencom.server.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.shencom.log.ops.context.OpsLogContext;
import cn.shencom.model.*;
import cn.shencom.model.dto.SimpleRegionDTO;
import cn.shencom.model.dto.create.FnRmsv3MembersCreateDTO;
import cn.shencom.model.dto.create.FnRmsv3MembersTypeRelateCreateDTO;
import cn.shencom.model.dto.query.FnRmsv3MembersQueryDTO;
import cn.shencom.model.dto.resp.FnRmsv3MembersRespDTO;
import cn.shencom.model.dto.update.FnRmsv3MembersTypeRelateUpdateDTO;
import cn.shencom.model.dto.update.FnRmsv3MembersUpdateDTO;
import cn.shencom.repos.FnRmsv3MembersRepository;
import cn.shencom.repos.FnRmsv3MembersTypeRelateRepository;
import cn.shencom.repos.SysRolesRepository;
import cn.shencom.scloud.common.base.constant.RespCode;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.jpa.scpage.MyLink;
import cn.shencom.scloud.common.jpa.util.export.ScExport;
import cn.shencom.scloud.common.jpa.util.query.LinkUtil;
import cn.shencom.scloud.common.jpa.util.query.ScQueryUtil;
import cn.shencom.scloud.common.server.service.BaseImpl;
import cn.shencom.scloud.common.util.BeanUtil;
import cn.shencom.scloud.scloudapiuaa.dto.SysUsersDTO;
import cn.shencom.server.manager.UaaManager;
import cn.shencom.server.service.IFnRmsv3MembersService;
import cn.shencom.server.service.IFnRmsv3MembersTypeRelateService;
import cn.shencom.utils.XsgcContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 小散工程-组织团队成员表 的服务实现
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Service
@Slf4j
public class FnRmsv3MembersServiceImpl extends BaseImpl implements IFnRmsv3MembersService {

    @Autowired
    private FnRmsv3MembersRepository fnRmsv3MembersRepository;


    @Autowired
    private FnRmsv3MembersTypeRelateRepository fnRmsv3MembersTypeRelateRepository;

    @Autowired
    private IFnRmsv3MembersTypeRelateService fnRmsv3MembersTypeRelateService;

    @Autowired
    private UaaManager uaaManager;

    @Override
    public Page<FnRmsv3MembersRespDTO> query(FnRmsv3MembersQueryDTO bean) {

        String organizationId = XsgcContext.getOrganizationId();
        assert organizationId!=null;

        Map<String, MyLink> linkMap = LinkUtil.convertLink(FnRmsv3Members.class);
        Page<FnRmsv3Members> res = fnRmsv3MembersRepository.findAll((root, query, builder) -> {
            //用于拼接条件
            List<Predicate> ps = new ArrayList<>();
            ps.add(builder.equal(root.get("organizationId"),organizationId));

            return ScQueryUtil.getPredicate(ps, bean, linkMap, root, query, builder);
        }, PageRequest.of(bean.getPage(), bean.getSize()));
        return ScQueryUtil.handle(res, linkMap, FnRmsv3MembersRespDTO.class,this::dealWith);
    }

    @Override
    public FnRmsv3MembersRespDTO show(ScShowDTO bean) {

        String organizationId = XsgcContext.getOrganizationId();
        assert organizationId!=null;

        Optional<FnRmsv3Members> option = fnRmsv3MembersRepository.findById(bean.getId());
        if (!option.isPresent()) {
            return null;
        }
        FnRmsv3Members entity = option.get();
        Map<String, MyLink> linkMap = LinkUtil.convertLink(FnRmsv3Members.class);
        return ScQueryUtil.handleOne(entity, linkMap, FnRmsv3MembersRespDTO.class,this::dealWith);
    }


    private void dealWith(FnRmsv3Members res, FnRmsv3MembersRespDTO dto) {
        List<FnRmsv3MembersTypeRelate> relateList = res.getFnRmsv3MembersTypeRelateList();
        if (CollUtil.isNotEmpty(relateList)) {
            Set<String> types = relateList.stream().map(FnRmsv3MembersTypeRelate::getTypeId).collect(Collectors.toSet());
            dto.setTypeIds(types);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public FnRmsv3Members create(FnRmsv3MembersCreateDTO bean) {

        String organizationId = XsgcContext.getOrganizationId();
        assert organizationId!=null;

        bean.setOrganizationId(organizationId);

        FnRmsv3Members entity = new FnRmsv3Members();
        String mobile = bean.getMobile();
        String realname = bean.getRealname();

        //查询当前手机号是否已经被使用
        if (fnRmsv3MembersRepository.existsByMobileAndOrganizationId(mobile,organizationId)){
            throw new ScException("当前手机号已绑定成员！");
        }

        //根据手机号查询用户信息
        SysUsersDTO sysUsersDTO = uaaManager.findByPhone(mobile);
        if (sysUsersDTO!=null){
            //校验当前系统用户是否已经绑定业务成员
            FnRmsv3Members firstMemberByUserId = fnRmsv3MembersRepository.findByUserIdAndOrganizationId(sysUsersDTO.getId(),organizationId);
            if (firstMemberByUserId!=null){
                throw new ScException("当前系统用户已经绑定成员！成员手机号："+ firstMemberByUserId.getMobile());
            }
        }else {

            SysUsersDTO dto =new SysUsersDTO();
            dto.setIsLock(1);
            dto.setRealname(bean.getRealname());
            dto.setPhone(mobile);
            sysUsersDTO = uaaManager.userInit(dto);
        }

        String userId = sysUsersDTO.getId();
        BeanUtil.copyProperties(bean, entity);
        entity.setUserId(userId);


        return fnRmsv3MembersRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public FnRmsv3Members update(FnRmsv3MembersUpdateDTO bean) {


        String organizationId = XsgcContext.getOrganizationId();
        assert organizationId!=null;


        FnRmsv3Members entity = fnRmsv3MembersRepository.findById(bean.getId())
                .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));


        //校验修改后的手机号码是否重复
        String newMobile = bean.getMobile();
        if (!entity.getMobile().equals(newMobile)){
            //查询手机号是否存在
            if (fnRmsv3MembersRepository.existsByMobileAndOrganizationId(newMobile,organizationId)){
                throw new ScException("当前手机号已绑定成员！");
            }
        }

        BeanUtil.copyProperties(bean, entity);
        return fnRmsv3MembersRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            ids.forEach(id -> fnRmsv3MembersRepository.findById(id).ifPresent(entity -> {
                entity.setDeletedAt(new Date());
                entity.setIsDeleted(1);

                //把角色也删了
                fnRmsv3MembersTypeRelateService.deleteMember(entity);

                fnRmsv3MembersRepository.save(entity);
            }));
        }
    }

    @Override
    public void export(FnRmsv3MembersQueryDTO bean) {
        List<FnRmsv3MembersRespDTO> dtos = new ArrayList<>(query(bean).getContent());
        ScExport.export(bean.getCfg(), dtos, FnRmsv3MembersRespDTO.class);
    }
}