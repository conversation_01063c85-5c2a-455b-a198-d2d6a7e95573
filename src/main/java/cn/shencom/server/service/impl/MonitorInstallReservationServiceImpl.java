package cn.shencom.server.service.impl;

import cn.shencom.model.MonitorInstallReservation;
import cn.shencom.model.dto.create.MonitorInstallReservationCreateDTO;
import cn.shencom.model.dto.query.MonitorInstallReservationQueryDTO;
import cn.shencom.model.dto.resp.MonitorInstallReservationRespDTO;
import cn.shencom.model.dto.update.MonitorInstallReservationUpdateDTO;
import cn.shencom.repos.MonitorInstallReservationRepository;
import cn.shencom.scloud.common.base.constant.RespCode;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.jpa.scpage.MyLink;
import cn.shencom.scloud.common.jpa.util.export.ScExport;
import cn.shencom.scloud.common.jpa.util.query.LinkUtil;
import cn.shencom.scloud.common.jpa.util.query.ScQueryUtil;
import cn.shencom.scloud.common.server.service.BaseImpl;
import cn.shencom.scloud.common.util.BeanUtil;
import cn.shencom.server.service.IMonitorInstallReservationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.util.*;


/**
 * 小散工程-监管工单流程-安装预约详情 的服务实现
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@Service
@Slf4j
public class MonitorInstallReservationServiceImpl extends BaseImpl implements IMonitorInstallReservationService {

    @Autowired
    private MonitorInstallReservationRepository monitorInstallReservationRepository;

    @Override
    public Page<MonitorInstallReservationRespDTO> query(MonitorInstallReservationQueryDTO bean) {
        Map<String, MyLink> linkMap = LinkUtil.convertLink(MonitorInstallReservation.class);
        Page<MonitorInstallReservation> res = monitorInstallReservationRepository.findAll((root, query, builder) -> {
            //用于拼接条件
            List<Predicate> ps = new ArrayList<>();

            return ScQueryUtil.getPredicate(ps, bean, linkMap, root, query, builder);
        }, PageRequest.of(bean.getPage(), bean.getSize()));
        return ScQueryUtil.handle(res, linkMap, MonitorInstallReservationRespDTO.class);
    }

    @Override
    public MonitorInstallReservationRespDTO show(ScShowDTO bean) {
        Optional<MonitorInstallReservation> option = monitorInstallReservationRepository.findById(bean.getId());
        if (!option.isPresent()) {
            return null;
        }
        MonitorInstallReservation entity = option.get();
        Map<String, MyLink> linkMap = LinkUtil.convertLink(MonitorInstallReservation.class);
        return ScQueryUtil.handleOne(entity, linkMap, MonitorInstallReservationRespDTO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public MonitorInstallReservation create(MonitorInstallReservationCreateDTO bean) {
        MonitorInstallReservation entity = new MonitorInstallReservation();
        BeanUtil.copyProperties(bean, entity);
        return monitorInstallReservationRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public MonitorInstallReservation update(MonitorInstallReservationUpdateDTO bean) {
        MonitorInstallReservation entity = monitorInstallReservationRepository.findById(bean.getId())
                .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));
        BeanUtil.copyProperties(bean, entity);
        return monitorInstallReservationRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            ids.forEach(id -> monitorInstallReservationRepository.deleteById(id));
        }
    }

    @Override
    public void export(MonitorInstallReservationQueryDTO bean) {
        List<MonitorInstallReservationRespDTO> dtos = new ArrayList<>(query(bean).getContent());
        ScExport.export(bean.getCfg(), dtos, MonitorInstallReservationRespDTO.class);
    }
}