package cn.shencom.server.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.shencom.enums.MonitorFlowEnum;
import cn.shencom.model.*;
import cn.shencom.model.dto.create.EventCameraPointCreateDTO;
import cn.shencom.model.dto.create.EventCameraPointDeviceCreateDTO;
import cn.shencom.model.dto.query.EventCameraPointDeviceQueryDTO;
import cn.shencom.model.dto.query.EventCameraPointDeviceMobileQueryDTO;
import cn.shencom.model.dto.resp.EventCameraPointDeviceRespDTO;
import cn.shencom.model.dto.resp.EventCameraPointDeviceMobileRespDTO;
import cn.shencom.model.dto.update.EventCameraPointDeviceUpdateDTO;
import cn.shencom.model.dto.update.EventCameraPointUpdateDTO;
import cn.shencom.model.dto.update.MonitorFlowUpdateDTO;
import cn.shencom.repos.*;
import cn.shencom.scloud.common.base.constant.RespCode;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.jpa.scpage.MyLink;
import cn.shencom.scloud.common.jpa.util.export.ScExport;
import cn.shencom.scloud.common.jpa.util.query.LinkUtil;
import cn.shencom.scloud.common.jpa.util.query.ScQueryUtil;
import cn.shencom.scloud.common.server.service.BaseImpl;
import cn.shencom.scloud.common.util.BeanUtil;
import cn.shencom.scloud.common.util.LambdaBeanUtil;
import cn.shencom.scloud.security.core.ScContext;
import cn.shencom.scloud.security.core.SecurityUser;
import cn.shencom.server.service.IEventCameraPointDeviceService;
import cn.shencom.server.service.IEventCameraPointService;
import cn.shencom.server.service.IMonitorAccessInfoService;
import cn.shencom.server.service.IMonitorFlowService;
import cn.shencom.server.service.ISporadicProjectService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 摄像头表 的服务实现
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Service
@Slf4j
public class EventCameraPointDeviceServiceImpl extends BaseImpl implements IEventCameraPointDeviceService {

    @Autowired
    private EventCameraPointDeviceRepository eventCameraPointDeviceRepository;

    @Autowired
    private IEventCameraPointService eventCameraPointService;

    @Autowired
    private EventCameraPointRepository eventCameraPointRepository;

    @Autowired
    private SporadicProjectRepository sporadicProjectRepository;

    @Autowired
    private MonitorOrderRepository monitorOrderRepository;

    @Autowired
    private MonitorFlowRepository monitorFlowRepository;

    @Autowired
    private IMonitorFlowService monitorFlowService;

    @Autowired
    private IMonitorAccessInfoService monitorAccessInfoService;

    @Autowired
    private ISporadicProjectService sporadicProjectService;

    @Override
    public Page<EventCameraPointDeviceRespDTO> query(EventCameraPointDeviceQueryDTO bean) {
        ScQueryUtil.getValuesRmAndSetBean(bean.getQuery(), Arrays.asList("realStatus", "status", "isHealthy", "modelNo", "pointExist"), bean);
        Map<String, MyLink> linkMap = LinkUtil.convertLink(EventCameraPointDevice.class);
        Page<EventCameraPointDevice> res = eventCameraPointDeviceRepository.findAll((root, query, builder) -> {
            //用于拼接条件
            List<Predicate> ps = new ArrayList<>();

            if (Objects.nonNull(bean.getRealStatus())
                    || Objects.nonNull(bean.getStatus())
                    ||Objects.nonNull(bean.getIsHealthy())
                    ||StrUtil.isNotBlank(bean.getModelNo())) {
                Join<Object, Object> eventCameraPointList = root.join("eventCameraPointList", JoinType.LEFT);
                if(Objects.nonNull(bean.getRealStatus())){
                    ps.add(builder.equal(eventCameraPointList.get("realStatus"), bean.getRealStatus()));
                }
                if(Objects.nonNull(bean.getStatus())){
                    ps.add(builder.equal(eventCameraPointList.get("status"), bean.getStatus()));
                }
                if(Objects.nonNull(bean.getIsHealthy())){
                    ps.add(builder.equal(eventCameraPointList.get("isHealthy"), bean.getIsHealthy()));
                }
                if(Objects.nonNull(bean.getModelNo())){
                    ps.add(builder.equal(eventCameraPointList.get("modelNo"), bean.getModelNo()));
                }
            }

            return ScQueryUtil.getPredicate(ps, bean, linkMap, root, query, builder);
        }, PageRequest.of(bean.getPage(), bean.getSize()));
        Map<String, MyLink> linkMapCamera = LinkUtil.convertLink(EventCameraPoint.class);
        return ScQueryUtil.handle(res, linkMap, EventCameraPointDeviceRespDTO.class, (r, dto) -> {
            if (CollectionUtils.isNotEmpty(r.getEventCameraPointList())) {
                ScQueryUtil.dealWith(r.getEventCameraPointList(), linkMapCamera);
            }
        });
    }

    @Override
    public EventCameraPointDeviceRespDTO show(ScShowDTO bean) {
        Optional<EventCameraPointDevice> option = eventCameraPointDeviceRepository.findById(bean.getId());
        if (!option.isPresent()) {
            return null;
        }
        EventCameraPointDevice entity = option.get();
        Map<String, MyLink> linkMap = LinkUtil.convertLink(EventCameraPointDevice.class);
        Map<String, MyLink> linkMapCamera = LinkUtil.convertLink(EventCameraPoint.class);
        return ScQueryUtil.handleOne(entity, linkMap, EventCameraPointDeviceRespDTO.class,(r,dto)->{
            if (CollectionUtils.isNotEmpty(r.getEventCameraPointList())) {
                ScQueryUtil.dealWith(r.getEventCameraPointList(), linkMapCamera);
            }
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public EventCameraPointDevice create(EventCameraPointDeviceCreateDTO bean) {


        EventCameraPointDevice entity = new EventCameraPointDevice();
        // 提交的
        String oSerialNo = bean.getSerialNo();
        BeanUtil.copyProperties(bean, entity);
        EventCameraPointDevice cameraPointDevice = eventCameraPointDeviceRepository.findFirstBySerialNo(bean.getSerialNo());
        if (Objects.nonNull(cameraPointDevice)) {
            throw new ScException(StrUtil.format("序列号{}已存在，无法新增,对应12位序列号:{}",oSerialNo,entity.getSerialNo()));
        }

        if (bean.getType()==22){
            if (StringUtils.isBlank(bean.getSipUserId())){
                throw new ScException("sip不能为空！");
            }
            //判断sip不能重复
            EventCameraPointDevice firstDeviceBySipUserId = eventCameraPointDeviceRepository.findFirstBySipUserId(bean.getSipUserId());
            if (Objects.nonNull(firstDeviceBySipUserId)){
                throw new ScException(StrUtil.format("sip{}已存在，无法新增,对应12位序列号:{}",firstDeviceBySipUserId.getSerialNo()));
            }
        }

        if (CollectionUtils.isEmpty(bean.getChannelList())) {
            throw new ScException("通道列表不能为空");
        }
        EventCameraPointDevice pointDevice = eventCameraPointDeviceRepository.save(entity);
        bean.getChannelList().forEach(channel -> {
            BeanUtil.copyProperties(bean, channel);
            channel.setDeviceId(pointDevice.getId());
            eventCameraPointService.create(pointDevice, channel);
        });


        //更新监管流程工单关联的设备信息
        if (pointDevice.getProjectId()!=null){
            processMonitorOrder(pointDevice.getProjectId());
        }


        return pointDevice;
    }


    /**
     * 更新monitor_access_info表的信息
     */
    private void  processMonitorOrder(String projectId){

        //查询当前工程监管流程工单
        MonitorOrder monitorOrder = monitorOrderRepository.findByProjectId(projectId);
        if (monitorOrder==null){
            throw new ScException("当前项目监管流程工单不存在！");
        }

        //如果工单未完结，则更新工单关联的摄像头信息
        if (!monitorOrder.getFlow().equals(MonitorFlowEnum.TYPE9.getType())){
            //修改接入监管的信息
            monitorAccessInfoService.createOrUpdateInfo(monitorOrder.getProjectId(), monitorOrder.getId());
        }

    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public EventCameraPointDevice update(EventCameraPointDeviceUpdateDTO bean) {

        EventCameraPointDevice entity = eventCameraPointDeviceRepository.findById(bean.getId())
                .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));


        String oldProjectId = entity.getProjectId();
        String newProjectId = bean.getProjectId();

        BeanUtil.copyProperties(bean, entity);
        if (CollectionUtils.isEmpty(bean.getChannelList())) {
            throw new ScException("通道列表不能为空");
        }
        if (StringUtils.isBlank(newProjectId)){
            entity.setProjectId(null);
            newProjectId=null;
        }

        entity.setSipUserId(StringUtils.isBlank(bean.getSipUserId()) ? null : bean.getSipUserId());
        List<String> channelIdList = new ArrayList<>();
        // 找到对应的id
        bean.getChannelList().forEach(channel -> {
            LambdaBeanUtil.copyProperties(bean, channel, EventCameraPointUpdateDTO::getId);
            if (StringUtils.isBlank(channel.getId())) {
                channel.setDeviceId(bean.getId());
                EventCameraPointCreateDTO createDTO = new EventCameraPointCreateDTO();
                LambdaBeanUtil.copyProperties(channel, createDTO);
                String cameraId = eventCameraPointService.create(entity, createDTO);
                channelIdList.add(cameraId);
            } else {
                Optional<EventCameraPoint> pointOptional = eventCameraPointRepository.findById(channel.getId());
                if (pointOptional.isPresent()) {
                    eventCameraPointService.update(entity, channel);
                    channelIdList.add(channel.getId());
                }
            }
        });
        // 把没用到的删掉
        eventCameraPointRepository.deleteNoContainCamera(bean.getId(), channelIdList);

        EventCameraPointDevice save = eventCameraPointDeviceRepository.save(entity);

        //更新监管流程工单关联的设备信息
        if (oldProjectId!=null){
            processMonitorOrder(oldProjectId);
        }
        if (newProjectId!=null&&!newProjectId.equals(oldProjectId)){
            processMonitorOrder(newProjectId);
        }


        return save;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> ids) {

        if (CollectionUtils.isNotEmpty(ids)) {
            ids.forEach(id -> eventCameraPointDeviceRepository.findById(id).ifPresent(entity -> {
                String projectId = entity.getProjectId();
                entity.setIsDeleted(1);
                eventCameraPointDeviceRepository.save(entity);

                List<String> channelIdList = new ArrayList<>();
                eventCameraPointRepository.deleteByDeviceId(id);

                //更新监管流程工单关联的设备信息
                processMonitorOrder(projectId);
            }));
        }
    }

    @Override
    public void export(EventCameraPointDeviceQueryDTO bean) {
        List<EventCameraPointDeviceRespDTO> dtos = new ArrayList<>(query(bean).getContent());
        ScExport.export(bean.getCfg(), dtos, EventCameraPointDeviceRespDTO.class);
    }

    @Override
    public Page<EventCameraPointDeviceMobileRespDTO> mobileIndex(EventCameraPointDeviceMobileQueryDTO bean) {
        // 获取用户信息
        SecurityUser currentUser = ScContext.getCurrentUser();
        if (currentUser == null) {
            throw new ScException(RespCode.CURRENT_USER_NOT_EXIST);
        }

        List<String> projectIdList = sporadicProjectService.mobileSelect().stream().map(r -> r.getId())
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(projectIdList)) {
            return Page.empty(PageRequest.of(bean.getPage(), bean.getSize()));
        }

        if (StringUtils.isNotBlank(bean.getProjectId()) && !projectIdList.contains(bean.getProjectId())) {
            return Page.empty(PageRequest.of(bean.getPage(), bean.getSize()));
        }

        if (StringUtils.isNotBlank(bean.getProjectId())) {
            bean.setProjectIds(Arrays.asList(bean.getProjectId()));
        }else{
            bean.setProjectIds(projectIdList);
        }

        PageRequest pageable = PageRequest.of(bean.getPage(), bean.getSize());

        Page<EventCameraPointDeviceMobileRespDTO> page = eventCameraPointDeviceRepository.mobileIndex(bean, pageable);

        List<String> deviceIds = page.getContent().stream().map(r -> r.getId()).collect(Collectors.toList());

        List<EventCameraPoint> cameraList = eventCameraPointRepository.findByDeviceIdIn(deviceIds);

        Page<EventCameraPointDeviceMobileRespDTO> res = ScQueryUtil.handle(page, null,
                EventCameraPointDeviceMobileRespDTO.class,
                (r, dto) -> {
                    List<EventCameraPoint> cameras = cameraList.stream()
                            .filter(r1 -> r1.getDeviceId().equals(r.getId()))
                            .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(cameras)) {
                        dto.setEventCameraPointList(cameras);
                    }
                });

        return res;
    }


    @Override
    public Page<EventCameraPointDeviceRespDTO> notRelevance(EventCameraPointDeviceQueryDTO bean) {
        PageRequest pageable = PageRequest.of(bean.getPage(), bean.getSize());

        return eventCameraPointDeviceRepository.notRelevanceIndex(bean, pageable);
    }


    @Override
    @Transactional
    public void cancelRelevance(String projectId) {
        //查询工程
        Optional<SporadicProject> projectOptional = sporadicProjectRepository.findById(projectId);
        if (!projectOptional.isPresent()){
            throw new ScException("当前工程不存在！");
        }
        SporadicProject sporadicProject = projectOptional.get();
        if (sporadicProject.getMonitorFlag()==1){
            sporadicProject.setMonitorFlag(2);
            sporadicProjectRepository.save(sporadicProject);
        }

        List<EventCameraPointDevice> deviceList = eventCameraPointDeviceRepository.findByProjectId(projectId);
        if (!deviceList.isEmpty()){
            for (EventCameraPointDevice device : deviceList) {
                device.setProjectId(null);


                List<EventCameraPoint> eventCameraPoints = eventCameraPointRepository.findByDeviceId(device.getId());
                if (!eventCameraPoints.isEmpty()){
                    for (EventCameraPoint eventCameraPoint : eventCameraPoints) {
                        eventCameraPoint.setProjectId(null);
                    }
                    eventCameraPointRepository.saveAll(eventCameraPoints);
                }
            }

            eventCameraPointDeviceRepository.saveAll(deviceList);
        }
    }

    @Override
    @Transactional
    public void createRelevance(MonitorFlow monitorFlow, List<String> deviceIds) {

        String projectId = monitorFlow.getProjectId();
        String orderId = monitorFlow.getOrderId();

        //查询工程
        Optional<SporadicProject> projectOptional = sporadicProjectRepository.findById(projectId);
        if (!projectOptional.isPresent()){
            throw new ScException("工程不存在！");
        }
        SporadicProject sporadicProject = projectOptional.get();

        List<EventCameraPointDevice> deviceList = eventCameraPointDeviceRepository.findByIdIn(deviceIds);
        if (!deviceList.isEmpty()){
            for (EventCameraPointDevice device : deviceList) {
                if (device.getProjectId()!=null){
                    throw new ScException(String.format("设备%s已绑定工程！",device.getSerialNo()));
                }
                device.setProjectId(monitorFlow.getProjectId());

                List<EventCameraPoint> eventCameraPoints = eventCameraPointRepository.findByDeviceId(device.getId());
                if (!eventCameraPoints.isEmpty()){
                    for (EventCameraPoint eventCameraPoint : eventCameraPoints) {
                        eventCameraPoint.setProjectId(monitorFlow.getProjectId());
                    }
                    eventCameraPointRepository.saveAll(eventCameraPoints);
                }

            }
            eventCameraPointDeviceRepository.saveAll(deviceList);

            //设置监管状态
            sporadicProject.setMonitorFlag(1);
            sporadicProjectRepository.save(sporadicProject);
        }

        //更新工单关联的摄像头信息
        monitorAccessInfoService.createOrUpdateInfo(projectId,orderId);
    }
}