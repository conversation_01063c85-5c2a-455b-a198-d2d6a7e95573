package cn.shencom.server.service.impl;

import cn.shencom.model.XsgcCustomerServiceRecord;
import cn.shencom.model.dto.create.XsgcCustomerServiceRecordCreateDTO;
import cn.shencom.model.dto.query.XsgcCustomerServiceRecordQueryDTO;
import cn.shencom.model.dto.resp.XsgcCustomerServiceRecordRespDTO;
import cn.shencom.model.dto.update.XsgcCustomerServiceRecordUpdateDTO;
import cn.shencom.repos.XsgcCustomerServiceRecordRepository;
import cn.shencom.scloud.common.base.constant.RespCode;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.jpa.scpage.MyLink;
import cn.shencom.scloud.common.jpa.util.export.ScExport;
import cn.shencom.scloud.common.jpa.util.query.LinkUtil;
import cn.shencom.scloud.common.jpa.util.query.ScQueryUtil;
import cn.shencom.scloud.common.server.service.BaseImpl;
import cn.shencom.scloud.common.util.BeanUtil;
import cn.shencom.scloud.common.util.StringUtil;
import cn.shencom.server.service.IXsgcCustomerServiceRecordService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.util.*;


/**
 * 小散工程-客户服务开通记录 的服务实现
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Service
@Slf4j
public class XsgcCustomerServiceRecordServiceImpl extends BaseImpl implements IXsgcCustomerServiceRecordService {

    @Autowired
    private XsgcCustomerServiceRecordRepository xsgcCustomerServiceRecordRepository;

    @Override
    public Page<XsgcCustomerServiceRecordRespDTO> query(XsgcCustomerServiceRecordQueryDTO bean) {
        Map<String, MyLink> linkMap = LinkUtil.convertLink(XsgcCustomerServiceRecord.class);
        Page<XsgcCustomerServiceRecord> res = xsgcCustomerServiceRecordRepository.findAll((root, query, builder) -> {
            //用于拼接条件
            List<Predicate> ps = new ArrayList<>();

            return ScQueryUtil.getPredicate(ps, bean, linkMap, root, query, builder);
        }, PageRequest.of(bean.getPage(), bean.getSize()));
        return ScQueryUtil.handle(res, linkMap, XsgcCustomerServiceRecordRespDTO.class);
    }

    @Override
    public XsgcCustomerServiceRecord lastRecord(XsgcCustomerServiceRecordQueryDTO bean) {
        if (StringUtil.isBlank(bean.getCustomerId())){
            throw new ScException("客户id不能为空！");
        }
        return xsgcCustomerServiceRecordRepository.findFirstByCustomerIdOrderByIdDesc(bean.getCustomerId());
    }

    @Override
    public XsgcCustomerServiceRecordRespDTO show(ScShowDTO bean) {
        Optional<XsgcCustomerServiceRecord> option = xsgcCustomerServiceRecordRepository.findById(bean.getId());
        if (!option.isPresent()) {
            return null;
        }
        XsgcCustomerServiceRecord entity = option.get();
        Map<String, MyLink> linkMap = LinkUtil.convertLink(XsgcCustomerServiceRecord.class);
        return ScQueryUtil.handleOne(entity, linkMap, XsgcCustomerServiceRecordRespDTO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public XsgcCustomerServiceRecord create(XsgcCustomerServiceRecordCreateDTO bean) {
        XsgcCustomerServiceRecord entity = new XsgcCustomerServiceRecord();
        BeanUtil.copyProperties(bean, entity);
        return xsgcCustomerServiceRecordRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public XsgcCustomerServiceRecord update(XsgcCustomerServiceRecordUpdateDTO bean) {
        XsgcCustomerServiceRecord entity = xsgcCustomerServiceRecordRepository.findById(bean.getId())
                .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));
        BeanUtil.copyProperties(bean, entity);
        return xsgcCustomerServiceRecordRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            ids.forEach(id -> xsgcCustomerServiceRecordRepository.findById(id).ifPresent(entity -> {
                entity.setIsDeleted(1);
                entity.setDeletedAt(new Date());
                xsgcCustomerServiceRecordRepository.save(entity);
            }));
        }
    }

    @Override
    public void export(XsgcCustomerServiceRecordQueryDTO bean) {
        List<XsgcCustomerServiceRecordRespDTO> dtos = new ArrayList<>(query(bean).getContent());
        ScExport.export(bean.getCfg(), dtos, XsgcCustomerServiceRecordRespDTO.class);
    }
}