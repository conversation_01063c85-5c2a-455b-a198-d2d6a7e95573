package cn.shencom.server.service.impl;

import cn.shencom.model.MonitorOnSceneInspection;
import cn.shencom.model.dto.create.MonitorOnSceneInspectionCreateDTO;
import cn.shencom.model.dto.query.MonitorOnSceneInspectionQueryDTO;
import cn.shencom.model.dto.resp.MonitorOnSceneInspectionRespDTO;
import cn.shencom.model.dto.update.MonitorOnSceneInspectionUpdateDTO;
import cn.shencom.repos.MonitorOnSceneInspectionRepository;
import cn.shencom.scloud.common.base.constant.RespCode;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.jpa.scpage.MyLink;
import cn.shencom.scloud.common.jpa.util.export.ScExport;
import cn.shencom.scloud.common.jpa.util.query.LinkUtil;
import cn.shencom.scloud.common.jpa.util.query.ScQueryUtil;
import cn.shencom.scloud.common.server.service.BaseImpl;
import cn.shencom.scloud.common.util.BeanUtil;
import cn.shencom.server.service.IMonitorOnSceneInspectionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.util.*;


/**
 * 小散工程-监管工单流程-现场勘察详情 的服务实现
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@Service
@Slf4j
public class MonitorOnSceneInspectionServiceImpl extends BaseImpl implements IMonitorOnSceneInspectionService {

    @Autowired
    private MonitorOnSceneInspectionRepository monitorOnSceneInspectionRepository;

    @Override
    public Page<MonitorOnSceneInspectionRespDTO> query(MonitorOnSceneInspectionQueryDTO bean) {
        Map<String, MyLink> linkMap = LinkUtil.convertLink(MonitorOnSceneInspection.class);
        Page<MonitorOnSceneInspection> res = monitorOnSceneInspectionRepository.findAll((root, query, builder) -> {
            //用于拼接条件
            List<Predicate> ps = new ArrayList<>();

            return ScQueryUtil.getPredicate(ps, bean, linkMap, root, query, builder);
        }, PageRequest.of(bean.getPage(), bean.getSize()));
        return ScQueryUtil.handle(res, linkMap, MonitorOnSceneInspectionRespDTO.class);
    }

    @Override
    public MonitorOnSceneInspectionRespDTO show(ScShowDTO bean) {
        Optional<MonitorOnSceneInspection> option = monitorOnSceneInspectionRepository.findById(bean.getId());
        if (!option.isPresent()) {
            return null;
        }
        MonitorOnSceneInspection entity = option.get();
        Map<String, MyLink> linkMap = LinkUtil.convertLink(MonitorOnSceneInspection.class);
        return ScQueryUtil.handleOne(entity, linkMap, MonitorOnSceneInspectionRespDTO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public MonitorOnSceneInspection create(MonitorOnSceneInspectionCreateDTO bean) {
        MonitorOnSceneInspection entity = new MonitorOnSceneInspection();
        BeanUtil.copyProperties(bean, entity);
        return monitorOnSceneInspectionRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public MonitorOnSceneInspection update(MonitorOnSceneInspectionUpdateDTO bean) {
        MonitorOnSceneInspection entity = monitorOnSceneInspectionRepository.findById(bean.getId())
                .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));
        BeanUtil.copyProperties(bean, entity);
        return monitorOnSceneInspectionRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            ids.forEach(id -> monitorOnSceneInspectionRepository.deleteById(id));
        }
    }

    @Override
    public void export(MonitorOnSceneInspectionQueryDTO bean) {
        List<MonitorOnSceneInspectionRespDTO> dtos = new ArrayList<>(query(bean).getContent());
        ScExport.export(bean.getCfg(), dtos, MonitorOnSceneInspectionRespDTO.class);
    }
}