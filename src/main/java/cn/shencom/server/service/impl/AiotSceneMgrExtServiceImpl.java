package cn.shencom.server.service.impl;

import cn.shencom.model.AiotSceneMgr;
import cn.shencom.model.dto.query.AiotSceneMgrQueryDTO;
import cn.shencom.model.dto.resp.AiotSceneMgrTreeRespDTO;
import cn.shencom.repos.AiotSceneMgrRepository;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.jpa.scpage.MyLink;
import cn.shencom.scloud.common.jpa.scpage.ScQuery;
import cn.shencom.scloud.common.jpa.scpage.ScSort;
import cn.shencom.scloud.common.jpa.util.query.LinkUtil;
import cn.shencom.scloud.common.jpa.util.query.ScQueryUtil;
import cn.shencom.scloud.common.server.service.BaseImpl;
import cn.shencom.scloud.common.util.BeanUtil;
import cn.shencom.scloud.common.util.TreeUtil;
import cn.shencom.server.service.IAiotSceneMgrExtService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.Join;
import javax.persistence.criteria.Order;
import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 场景管理 的服务实现
 *
 * <AUTHOR>
 * @since 2024-08-03
 */
@Service
@Slf4j
public class AiotSceneMgrExtServiceImpl extends BaseImpl implements IAiotSceneMgrExtService {

    @Autowired
    AiotSceneMgrRepository aiotSceneMgrRepository;

    @Override
    public Result<List<AiotSceneMgrTreeRespDTO>> getMenuTreeByAll(AiotSceneMgrQueryDTO bean) {
        List<ScSort> sorts = bean.getSorts();
        List<ScQuery> queries = bean.getQuery();

        Map<String, MyLink> linkMap = LinkUtil.convertLink(bean.getClass());
        Map<String, Join<Object, Object>> joinMap = new HashMap<>();


        List<AiotSceneMgr> res = aiotSceneMgrRepository.findAll((root, query, builder) -> {

            //用于拼接条件
            List<Predicate> ps = new ArrayList<>();

            Predicate predicate = builder.equal(root.get("isDeleted"), 0);
            ps.add(predicate);
            //查询条件
            if (queries != null && queries.size() > 0) {
                Predicate constructPredicate = ScQueryUtil
                        .constructQuery(queries, root, builder, linkMap, joinMap);
                if (constructPredicate != null) {
                    ps.add(constructPredicate);
                }
            }

            //排序
            List<Order> orders = ScQueryUtil.constructSort(sorts, root, builder, linkMap, joinMap);
            if (orders != null && orders.size() > 0) {
                query.orderBy(orders);
            }

            if (ps.size() > 0) {
                query
                        .where(builder.and(ps.toArray(new Predicate[]{})));
            }

            return query.getRestriction();

        });

        dealWithTree(res, linkMap);

        List<AiotSceneMgrTreeRespDTO> treeList = BeanUtil.toBeanList(res, AiotSceneMgrTreeRespDTO.class, "children", "parent");

        treeList.forEach(tree -> tree.setPid(tree.getPId()));

        //构建树形
        List<AiotSceneMgrTreeRespDTO> trees = TreeUtil.buildTreeByPid(treeList);

        //计算子节点数
        calSysMenuVONumOfNode(trees);

        return success(trees);
    }

    //处理数据
    private void dealWithTree(List<AiotSceneMgr> res, Map<String, MyLink> linkMap) {
        ScQueryUtil.dealWith(res, linkMap);
    }

    private void calNumOfNode(List<AiotSceneMgr> allTrees) {
        for (AiotSceneMgr tree : allTrees) {
            calNumOfNode(tree, tree);
        }
    }

    private void calNumOfNode(AiotSceneMgr org, AiotSceneMgr current) {
        if (current != null) {
            List<AiotSceneMgr> children = current.getChildren();
            if (children != null) {
                org.setNum(children.size() + org.getNum());
                for (AiotSceneMgr child : children) {
                    calNumOfNode(org, child);
                    calNumOfNode(child, child);
                }
            }
        }
    }

    private void calSysMenuVONumOfNode(List<AiotSceneMgrTreeRespDTO> allTrees) {
        for (AiotSceneMgrTreeRespDTO tree : allTrees) {
            calSysMenuVOOfNode(tree, tree);
        }
    }

    private void calSysMenuVOOfNode(AiotSceneMgrTreeRespDTO org, AiotSceneMgrTreeRespDTO current) {
        if (current != null) {
            List<AiotSceneMgrTreeRespDTO> children = current.getChildren();
            if (children != null) {
                org.setNum(children.size() + org.getNum());
                for (AiotSceneMgrTreeRespDTO child : children) {
                    calSysMenuVOOfNode(org, child);
                    calSysMenuVOOfNode(child, child);
                }
            }
        }
    }

}