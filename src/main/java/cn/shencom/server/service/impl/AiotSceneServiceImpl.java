package cn.shencom.server.service.impl;

import cn.shencom.model.AiotScene;
import cn.shencom.model.dto.create.AiotSceneCreateDTO;
import cn.shencom.model.dto.query.AiotSceneQueryDTO;
import cn.shencom.model.dto.resp.AiotSceneRespDTO;
import cn.shencom.model.dto.update.AiotSceneUpdateDTO;
import cn.shencom.repos.AiotSceneRepository;
import cn.shencom.scloud.common.base.constant.RespCode;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.jpa.scpage.MyLink;
import cn.shencom.scloud.common.jpa.util.export.ScExport;
import cn.shencom.scloud.common.jpa.util.query.LinkUtil;
import cn.shencom.scloud.common.jpa.util.query.ScQueryUtil;
import cn.shencom.scloud.common.server.service.BaseImpl;
import cn.shencom.scloud.common.util.BeanUtil;
import cn.shencom.server.service.IAiotSceneService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.util.*;


/**
 * aiot场景分类 的服务实现
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Service
@Slf4j
public class AiotSceneServiceImpl extends BaseImpl implements IAiotSceneService {

    @Autowired
    private AiotSceneRepository aiotSceneRepository;

    @Override
    public Page<AiotSceneRespDTO> query(AiotSceneQueryDTO bean) {
        Map<String, MyLink> linkMap = LinkUtil.convertLink(AiotScene.class);
        Page<AiotScene> res = aiotSceneRepository.findAll((root, query, builder) -> {
            //用于拼接条件
            List<Predicate> ps = new ArrayList<>();

            return ScQueryUtil.getPredicate(ps, bean, linkMap, root, query, builder);
        }, PageRequest.of(bean.getPage(), bean.getSize()));
        return ScQueryUtil.handle(res, linkMap, AiotSceneRespDTO.class);
    }

    @Override
    public AiotSceneRespDTO show(ScShowDTO bean) {
        Optional<AiotScene> option = aiotSceneRepository.findById(bean.getId());
        if (!option.isPresent()) {
            return null;
        }
        AiotScene entity = option.get();
        Map<String, MyLink> linkMap = LinkUtil.convertLink(AiotScene.class);
        return ScQueryUtil.handleOne(entity, linkMap, AiotSceneRespDTO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public AiotScene create(AiotSceneCreateDTO bean) {
        AiotScene entity = new AiotScene();
        BeanUtil.copyProperties(bean, entity);
        return aiotSceneRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public AiotScene update(AiotSceneUpdateDTO bean) {
        AiotScene entity = aiotSceneRepository.findById(bean.getId())
                .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));
        BeanUtil.copyProperties(bean, entity);
        return aiotSceneRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            ids.forEach(id -> aiotSceneRepository.findById(id).ifPresent(entity -> {
                entity.setIsDeleted(1);
                entity.setDeletedAt(new Date());
                aiotSceneRepository.save(entity);
            }));
        }
    }

    @Override
    public void export(AiotSceneQueryDTO bean) {
        List<AiotSceneRespDTO> dtos = new ArrayList<>(query(bean).getContent());
        ScExport.export(bean.getCfg(), dtos, AiotSceneRespDTO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public AiotScene updateOrCreate(String code,String name) {
        AiotScene entity = aiotSceneRepository.findFirstByCodeWithDeleted(code);
        if (Objects.isNull(entity)) {
            entity = new AiotScene();
            entity.setCode(code);
        }
        entity.setName(name);
        entity.setIsDeleted(0);
        entity.setDeletedAt(null);
        return aiotSceneRepository.save(entity);
    }
}