package cn.shencom.server.service.impl;

import cn.shencom.enums.MonitorFlowEnum;
import cn.shencom.model.MonitorAccessInfo;
import cn.shencom.model.MonitorFlow;
import cn.shencom.model.MonitorOrder;
import cn.shencom.model.SporadicProject;

import cn.shencom.model.dto.create.MonitorOrderCreateDTO;
import cn.shencom.model.dto.query.MonitorOrderQueryDTO;
import cn.shencom.model.dto.resp.MonitorFlowRespDTO;
import cn.shencom.model.dto.resp.MonitorOrderRespDTO;
import cn.shencom.model.dto.resp.MonitorOrderRespOverviewDTO;
import cn.shencom.model.dto.resp.SporadicProjectRespDTO;
import cn.shencom.model.dto.update.MonitorOrderUpdateDTO;
import cn.shencom.repos.MonitorAccessInfoRepository;
import cn.shencom.repos.MonitorOrderRepository;
import cn.shencom.scloud.common.base.constant.RespCode;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.jpa.scpage.MyLink;
import cn.shencom.scloud.common.jpa.util.export.ScExport;
import cn.shencom.scloud.common.jpa.util.query.LinkUtil;
import cn.shencom.scloud.common.jpa.util.query.ScQueryUtil;
import cn.shencom.scloud.common.server.service.BaseImpl;
import cn.shencom.scloud.common.util.BeanUtil;
import cn.shencom.scloud.security.core.ScContext;
import cn.shencom.server.service.IMonitorFlowService;
import cn.shencom.server.service.IMonitorOrderService;
import cn.shencom.server.service.ISporadicProjectService;
import cn.shencom.utils.UserUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 小散工程-监管工单 的服务实现
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@Service
@Slf4j
public class MonitorOrderServiceImpl extends BaseImpl implements IMonitorOrderService {

    @Autowired
    private MonitorOrderRepository monitorOrderRepository;

    @Autowired
    private IMonitorFlowService monitorFlowService;

    @Autowired
    private MonitorAccessInfoRepository monitorAccessInfoRepository;

    @Autowired
    private ISporadicProjectService sporadicProjectService;

    @Override
    public Page<MonitorOrderRespDTO> query(MonitorOrderQueryDTO bean) {

        String userId = ScContext.getCurrentUserThrow().getId();

        List<String>  orderIds = new ArrayList<>();
        //如果是业主/施工负责人   只能看到自己提交的预约
        if (bean.getQueryType()!=null&&bean.getQueryType()==2){
            if (bean.getInstallType()!=null){
                orderIds = monitorFlowService.findOwnerOrderIds(userId,MonitorFlowEnum.TYPE1.getType() );
                if (orderIds.isEmpty()){
                    return Page.empty(PageRequest.of(bean.getPage(), bean.getSize()));
                }
            }else if (bean.getRecycleType()!=null){
                orderIds = monitorFlowService.findOwnerOrderIds(userId,MonitorFlowEnum.TYPE6.getType() );
                if (orderIds.isEmpty()){
                    return Page.empty(PageRequest.of(bean.getPage(), bean.getSize()));
                }
            }
        }


        Map<String, MyLink> linkMap = LinkUtil.convertLink(MonitorOrder.class);
        List<String> finalOrderIds = orderIds;
        Page<MonitorOrder> res = monitorOrderRepository.findAll((root, query, builder) -> {
            //用于拼接条件
            List<Predicate> ps = new ArrayList<>();

            if (!finalOrderIds.isEmpty()){
                ps.add(builder.in(root.get("id")).value(finalOrderIds));
            }


            //安装情况查询
            Integer installType = bean.getInstallType();
            if (installType!=null){
                if (installType==1){
                    ps.add(builder.equal(root.get("flow"), MonitorFlowEnum.TYPE2.getType()));
                }else if (installType==2){
                    ps.add(builder.equal(root.get("flow"), MonitorFlowEnum.TYPE3.getType()));
                }else if (installType==3){
                    ps.add(builder.greaterThan(root.get("flow"), MonitorFlowEnum.TYPE3.getType()));
                }else if (installType ==0 ){
                    ps.add(builder.greaterThan(root.get("flow"), MonitorFlowEnum.TYPE1.getType()));
                }
            }


            //回收状态查询
            Integer recycleType = bean.getRecycleType();
            if (recycleType!=null){
                if (recycleType==1){
                    ps.add(builder.equal(root.get("flow"), MonitorFlowEnum.TYPE7.getType()));
                }else if (recycleType==2){
                    ps.add(builder.greaterThan(root.get("flow"), MonitorFlowEnum.TYPE7.getType()));
                }else if (recycleType ==0 ){
                    ps.add(builder.greaterThan(root.get("flow"), MonitorFlowEnum.TYPE6.getType()));
                }
            }

            return ScQueryUtil.getPredicate(ps, bean, linkMap, root, query, builder);
        }, PageRequest.of(bean.getPage(), bean.getSize()));
        return ScQueryUtil.handle(res, linkMap, MonitorOrderRespDTO.class,this::delData);
    }



    private void delData(MonitorOrder re,MonitorOrderRespDTO dto){
        Integer flow = re.getFlow();

        MonitorFlow monitorFlow = null;
        if (flow.equals(MonitorFlowEnum.TYPE2.getType())){
            dto.setInstallStatus(1);
            //查询预约时间
            monitorFlow = monitorFlowService.findByOrderIdAndFlow(re.getId(), MonitorFlowEnum.TYPE1.getType());

        }else if (flow.equals(MonitorFlowEnum.TYPE3.getType())){

            //查询预约时间
            monitorFlow = monitorFlowService.findByOrderIdAndFlow(re.getId(), MonitorFlowEnum.TYPE2.getType());

            dto.setInstallStatus(2);
        }else if (flow>MonitorFlowEnum.TYPE3.getType()){
            dto.setInstallStatus(3);
        }

        if (flow.equals(MonitorFlowEnum.TYPE7.getType())){
            //查询预约时间
             monitorFlow = monitorFlowService.findByOrderIdAndFlow(re.getId(), MonitorFlowEnum.TYPE6.getType());

            dto.setRecycleStatus(1);
        }else if (flow>MonitorFlowEnum.TYPE7.getType()){
            dto.setRecycleStatus(2);
        }


        //填充上一个环节的信息
        if (monitorFlow!=null){
            ScShowDTO scShowDTO = new ScShowDTO();
            scShowDTO.setId(monitorFlow.getId());
            MonitorFlowRespDTO flowRespDTO = monitorFlowService.show(scShowDTO);
            if (flowRespDTO!=null){
                dto.setReservationTime(flowRespDTO.getReservationTime());
                dto.setContactName(flowRespDTO.getContactName());
                dto.setContactMobile(flowRespDTO.getContactMobile());
                dto.setInspectTime(flowRespDTO.getInspectTime());
                dto.setInstallCnt(flowRespDTO.getInstallCnt());
            }
        }

    }



    @Override
    public MonitorOrderRespDTO show(ScShowDTO bean) {
        Optional<MonitorOrder> option = monitorOrderRepository.findById(bean.getId());
        if (!option.isPresent()) {
            return null;
        }
        MonitorOrder entity = option.get();
        Map<String, MyLink> linkMap = LinkUtil.convertLink(MonitorOrder.class);

        MonitorOrderRespDTO respDTO = ScQueryUtil.handleOne(entity, linkMap, MonitorOrderRespDTO.class, this::delData);


        //获取关联的设备id
        List<MonitorAccessInfo> monitorAccessInfoList = monitorAccessInfoRepository.findByOrderId(respDTO.getId());
        if (!monitorAccessInfoList.isEmpty()){
            respDTO.setCameraDeviceIds(monitorAccessInfoList.stream().map(MonitorAccessInfo::getDeviceId).collect(Collectors.toList()));
            respDTO.setCameraSerialNos(monitorAccessInfoList.stream().map(MonitorAccessInfo::getSerialNo).collect(Collectors.toList()));
        }


        return respDTO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public MonitorOrder create(MonitorOrderCreateDTO bean) {
        MonitorOrder entity = new MonitorOrder();
        BeanUtil.copyProperties(bean, entity);


        MonitorOrder save = monitorOrderRepository.save(entity);

        return save;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public MonitorOrder update(MonitorOrderUpdateDTO bean) {
        MonitorOrder entity = monitorOrderRepository.findById(bean.getId())
                .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));
        BeanUtil.copyProperties(bean, entity);
        return monitorOrderRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            ids.forEach(id -> monitorOrderRepository.findById(id).ifPresent(entity -> {
                entity.setIsDeleted(1);
                monitorOrderRepository.save(entity);
            }));
        }
    }

    @Override
    public void export(MonitorOrderQueryDTO bean) {
        List<MonitorOrderRespDTO> dtos = new ArrayList<>(query(bean).getContent());
        ScExport.export(bean.getCfg(), dtos, MonitorOrderRespDTO.class);
    }

    @Override
    public MonitorOrderRespOverviewDTO getDataOverview() {
        // 获取所有未删除的监管工单
        List<MonitorOrder> allOrders = monitorOrderRepository.findAll((root, query, builder) -> {
            List<Predicate> ps = new ArrayList<>();
            ps.add(builder.equal(root.get("isDeleted"), 0));
            return builder.and(ps.toArray(new Predicate[0]));
        });

        // 待预约：流程状态为1（安装预约）的工单数量
        long waitingReservation = allOrders.stream()
                .filter(order -> order.getFlow().equals(MonitorFlowEnum.TYPE1.getType()))
                .count();

        // 待勘查：流程状态为2（现场勘察）的工单数量
        long waitingInspection = allOrders.stream()
                .filter(order -> order.getFlow().equals(MonitorFlowEnum.TYPE2.getType()))
                .count();

        // 待安装：流程状态为3（上门安装）的工单数量
        long waitingInstallation = allOrders.stream()
                .filter(order -> order.getFlow().equals(MonitorFlowEnum.TYPE3.getType()))
                .count();

        // 待回收：流程状态为6（回收预约）或7（上门回收）的工单数量
        long waitingRecycle = allOrders.stream()
                .filter(order -> order.getFlow().equals(MonitorFlowEnum.TYPE6.getType()) ||
                               order.getFlow().equals(MonitorFlowEnum.TYPE7.getType()))
                .count();

        // 待服务：流程状态为4（接入监管）或5（施工完成）的工单数量
        long waitingService = 0L;

        MonitorOrderRespOverviewDTO overviewDTO = MonitorOrderRespOverviewDTO.builder()
                .waitingReservation(waitingReservation)
                .waitingInspection(waitingInspection)
                .waitingInstallation(waitingInstallation)
                .waitingRecycle(waitingRecycle)
                .waitingService(waitingService)
                .build();

        return overviewDTO;
    }

    @Override
    public MonitorOrderRespOverviewDTO getDataOverviewUser() {

        List<SporadicProjectRespDTO> userProjectList = sporadicProjectService.getUserProjectList();

        List<String> projectIds = userProjectList.stream().map(SporadicProjectRespDTO::getId).collect(Collectors.toList());

        // 获取所有未删除的监管工单
        List<MonitorOrder> allOrders = monitorOrderRepository.findAll((root, query, builder) -> {
            List<Predicate> ps = new ArrayList<>();
            ps.add(builder.in(root.get("projectId")).value(projectIds));
            ps.add(builder.equal(root.get("isDeleted"), 0));
            return builder.and(ps.toArray(new Predicate[0]));
        });

        // 待预约：流程状态为1（安装预约）或6（回收预约）的工单数量
        long waitingReservation = allOrders.stream()
                .filter(order -> order.getFlow().equals(MonitorFlowEnum.TYPE1.getType())
                        || order.getFlow().equals(MonitorFlowEnum.TYPE6.getType()))
                .count();

        // 待勘查：流程状态为2（现场勘察）的工单数量
        long waitingInspection = allOrders.stream()
                .filter(order -> order.getFlow().equals(MonitorFlowEnum.TYPE2.getType()))
                .count();

        // 待安装：流程状态为3（上门安装）的工单数量
        long waitingInstallation = allOrders.stream()
                .filter(order -> order.getFlow().equals(MonitorFlowEnum.TYPE3.getType()))
                .count();

        // 待回收：流程状态为6（回收预约）或7（上门回收）的工单数量
        long waitingRecycle = allOrders.stream()
                .filter(order -> order.getFlow().equals(MonitorFlowEnum.TYPE7.getType()))
                .count();

        // 待服务：
        long waitingService = 0L;

        MonitorOrderRespOverviewDTO overviewDTO = MonitorOrderRespOverviewDTO.builder()
                .waitingReservation(waitingReservation)
                .waitingInspection(waitingInspection)
                .waitingInstallation(waitingInstallation)
                .waitingRecycle(waitingRecycle)
                .waitingService(waitingService)
                .build();

        return overviewDTO;
    }

    @Override
    @Transactional
    public void createOrder(SporadicProject sporadicProject) {
        MonitorOrderCreateDTO createDTO = new MonitorOrderCreateDTO();
        //填充属性
        createDTO.setOrganizationId(sporadicProject.getOrganizationId());
        createDTO.setCreatedUser(sporadicProject.getCreateUser());
        createDTO.setProjectId(sporadicProject.getId());
        createDTO.setRegionPid(sporadicProject.getRegionPid());
        createDTO.setRegionId(sporadicProject.getRegionId());
        createDTO.setRegionCid(sporadicProject.getRegionCid());
        MonitorOrder order = create(createDTO);

        monitorFlowService.initFlow(order);
    }


    @Override
    public MonitorOrder findByProjectId(String projectId) {
        return monitorOrderRepository.findByProjectId(projectId);
    }
}