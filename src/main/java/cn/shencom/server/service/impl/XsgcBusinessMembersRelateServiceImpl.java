package cn.shencom.server.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.shencom.model.XsgcBusinessMembersRelate;
import cn.shencom.model.XsgcCustomerInfo;
import cn.shencom.model.dto.create.XsgcBusinessMembersRelateCreateDTO;
import cn.shencom.model.dto.query.XsgcBusinessMembersRelateQueryDTO;
import cn.shencom.model.dto.resp.XsgcBusinessMembersRelateRespDTO;
import cn.shencom.model.dto.update.XsgcBusinessMembersRelateUpdateDTO;
import cn.shencom.repos.XsgcBusinessMembersRelateRepository;
import cn.shencom.repos.XsgcCustomerInfoRepository;
import cn.shencom.scloud.common.base.constant.RespCode;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.jpa.scpage.MyLink;
import cn.shencom.scloud.common.jpa.util.export.ScExport;
import cn.shencom.scloud.common.jpa.util.query.LinkUtil;
import cn.shencom.scloud.common.jpa.util.query.ScQueryUtil;
import cn.shencom.scloud.common.server.service.BaseImpl;
import cn.shencom.scloud.common.util.BeanUtil;
import cn.shencom.server.service.IXsgcBusinessMembersRelateService;
import cn.shencom.utils.XsgcContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.util.*;


/**
 * 小散工程-业务人员客户关联表 的服务实现
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Service
@Slf4j
public class XsgcBusinessMembersRelateServiceImpl extends BaseImpl implements IXsgcBusinessMembersRelateService {

    @Autowired
    private XsgcBusinessMembersRelateRepository xsgcBusinessMembersRelateRepository;


    @Autowired
    private XsgcCustomerInfoRepository xsgcCustomerInfoRepository;

    @Override
    public Page<XsgcBusinessMembersRelateRespDTO> query(XsgcBusinessMembersRelateQueryDTO bean) {
        Map<String, MyLink> linkMap = LinkUtil.convertLink(XsgcBusinessMembersRelate.class);
        Page<XsgcBusinessMembersRelate> res = xsgcBusinessMembersRelateRepository.findAll((root, query, builder) -> {
            //用于拼接条件
            List<Predicate> ps = new ArrayList<>();

            return ScQueryUtil.getPredicate(ps, bean, linkMap, root, query, builder);
        }, PageRequest.of(bean.getPage(), bean.getSize()));
        return ScQueryUtil.handle(res, linkMap, XsgcBusinessMembersRelateRespDTO.class);
    }

    @Override
    public XsgcBusinessMembersRelateRespDTO show(ScShowDTO bean) {
        Optional<XsgcBusinessMembersRelate> option = xsgcBusinessMembersRelateRepository.findById(bean.getId());
        if (!option.isPresent()) {
            return null;
        }
        XsgcBusinessMembersRelate entity = option.get();
        Map<String, MyLink> linkMap = LinkUtil.convertLink(XsgcBusinessMembersRelate.class);
        return ScQueryUtil.handleOne(entity, linkMap, XsgcBusinessMembersRelateRespDTO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public XsgcBusinessMembersRelate create(XsgcBusinessMembersRelateCreateDTO bean) {
        XsgcBusinessMembersRelate entity = new XsgcBusinessMembersRelate();
        BeanUtil.copyProperties(bean, entity);
        return xsgcBusinessMembersRelateRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public XsgcBusinessMembersRelate update(XsgcBusinessMembersRelateUpdateDTO bean) {
        XsgcBusinessMembersRelate entity = xsgcBusinessMembersRelateRepository.findById(bean.getId())
                .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));
        BeanUtil.copyProperties(bean, entity);
        return xsgcBusinessMembersRelateRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            ids.forEach(id -> xsgcBusinessMembersRelateRepository.findById(id).ifPresent(entity -> {
                entity.setIsDeleted(1);
                entity.setDeletedAt(new Date());
                xsgcBusinessMembersRelateRepository.save(entity);
            }));
        }
    }

    @Override
    public void export(XsgcBusinessMembersRelateQueryDTO bean) {
        List<XsgcBusinessMembersRelateRespDTO> dtos = new ArrayList<>(query(bean).getContent());
        ScExport.export(bean.getCfg(), dtos, XsgcBusinessMembersRelateRespDTO.class);
    }


    @Override
    public Page<XsgcBusinessMembersRelateRespDTO> relevanceIndex(XsgcBusinessMembersRelateQueryDTO bean) {
        return xsgcBusinessMembersRelateRepository.relevanceIndex(bean,PageRequest.of(bean.getPage(),bean.getSize()));
    }

    @Override
    public Page<XsgcBusinessMembersRelateRespDTO> notRelevanceIndex(XsgcBusinessMembersRelateQueryDTO bean) {
        return xsgcBusinessMembersRelateRepository.notRelevanceIndex(bean,PageRequest.of(bean.getPage(),bean.getSize()));
    }


    @Override
    public void relevance(XsgcBusinessMembersRelateUpdateDTO bean) {
        if (bean.getOrganizationId()==null){
            throw new ScException("组织id不能为空！");
        }
        if (bean.getType()==null){
            throw new ScException("职位不能为空！");
        }
        String organizationId = bean.getOrganizationId();
        Integer type = bean.getType();
        //查询客户
        XsgcCustomerInfo xsgcCustomerInfo = xsgcCustomerInfoRepository.findFirstByOrganizationId(organizationId);
        String customerId = xsgcCustomerInfo.getId();
        //检查关联关系和填充必要字段
        if (!CollectionUtil.isEmpty(bean.getRelateList())){
            for (XsgcBusinessMembersRelateUpdateDTO dto : bean.getRelateList()) {
                dto.setCustomerId(customerId);
                dto.setOrganizationId(organizationId);
                dto.setType(type);
                assert StringUtils.isNotBlank(dto.getMemberId()) ;
            }
        }


        //新的关联关系
        List<XsgcBusinessMembersRelateUpdateDTO> relateList = bean.getRelateList();

        //查询当前用户绑定的所有记录
        List<XsgcBusinessMembersRelate> oldRelateList = xsgcBusinessMembersRelateRepository.findByOrganizationIdAndType(organizationId,type);
        //删除旧的关联关系
        xsgcBusinessMembersRelateRepository.deleteAll(oldRelateList);


        //需要新增的关联关系
        List<XsgcBusinessMembersRelate>  newRelateList= new ArrayList<>();
        relateList.forEach(dto -> {
            XsgcBusinessMembersRelate relate = new XsgcBusinessMembersRelate();
            BeanUtil.copyProperties(dto,relate);
            newRelateList.add(relate);
        });

        //创建新的关联关系
        xsgcBusinessMembersRelateRepository.saveAll(newRelateList);

    }


    @Override
    @Transactional
    public void moreRelevance(List<XsgcBusinessMembersRelateUpdateDTO> bean) {
        for (XsgcBusinessMembersRelateUpdateDTO updateDTO : bean) {
            relevance(updateDTO);
        }
    }
}