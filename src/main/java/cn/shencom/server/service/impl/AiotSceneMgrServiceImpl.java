package cn.shencom.server.service.impl;

import cn.shencom.log.ops.context.LogRecordContext;
import cn.shencom.model.AiotSceneMgr;
import cn.shencom.model.dto.create.AiotSceneMgrCreateDTO;
import cn.shencom.model.dto.query.AiotSceneMgrQueryDTO;
import cn.shencom.model.dto.resp.AiotSceneMgrRespDTO;
import cn.shencom.model.dto.update.AiotSceneMgrUpdateDTO;
import cn.shencom.repos.AiotSceneMgrRepository;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.base.constant.RespCode;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.scloud.common.base.snowflake.SnowflakeGenerator;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.jpa.scpage.MyLink;
import cn.shencom.scloud.common.jpa.scpage.ScExp;
import cn.shencom.scloud.common.jpa.scpage.ScQuery;
import cn.shencom.scloud.common.jpa.scpage.ScSort;
import cn.shencom.scloud.common.jpa.util.export.ScExport;
import cn.shencom.scloud.common.jpa.util.query.LinkUtil;
import cn.shencom.scloud.common.jpa.util.query.ScQueryUtil;
import cn.shencom.scloud.common.server.service.BaseImpl;
import cn.shencom.scloud.common.util.BeanUtil;
import cn.shencom.scloud.common.util.StringUtil;
import cn.shencom.scloud.common.util.thread.ExportThreadPool;
import cn.shencom.scloud.security.core.ScContext;
import cn.shencom.scloud.security.core.SecurityUser;
import cn.shencom.server.service.IAiotSceneMgrService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import javax.persistence.criteria.Join;
import javax.persistence.criteria.Order;
import javax.persistence.criteria.Predicate;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 场景管理 的服务实现
 *
 * <AUTHOR>
 * @since 2024-08-03
 */
@Service
@Slf4j
public class AiotSceneMgrServiceImpl extends BaseImpl implements IAiotSceneMgrService {

    @Autowired
    private AiotSceneMgrRepository aiotSceneMgrRepository;
//
//    @Autowired
//    private AiotSceneDetailedRepository aiotSceneDetailedRepository;

    @Override
    public Result query(AiotSceneMgrQueryDTO bean) {

        List<ScSort> sorts = bean.getSorts();
        List<ScQuery> queries = bean.getQuery();

        Map<String, MyLink> linkMap = LinkUtil.convertLink(bean.getClass());
        Map<String, Join<Object, Object>> joinMap = new HashMap<>();

        Page<AiotSceneMgr> res = aiotSceneMgrRepository.findAll((root, query, builder) -> {

            //用于拼接条件
            List<Predicate> ps = new ArrayList<>();

            Predicate predicate = builder.equal(root.get("isDeleted"), 0);
            ps.add(predicate);
            if (bean.getPId() != null && !"".equals(bean.getPId())) {
                ScQuery scQuery = new ScQuery();
                scQuery.setLr("and");
                List<ScExp> scExps = new ArrayList<>();
                ScExp scExp = new ScExp();
                scExp.setLr("and");
                scExp.setOperate("eq");
                scExp.setProp("pId");
                scExp.setValue(bean.getPId());
                scExps.add(scExp);
                scQuery.setExps(scExps);
                if (queries == null || queries.size() == 0) {
                    List<ScQuery> querie = new ArrayList<ScQuery>();
                    querie.add(scQuery);
                    Predicate constructPredicate = ScQueryUtil.constructQuery(querie, root, builder, linkMap, joinMap);
                    if (constructPredicate != null) {
                        ps.add(constructPredicate);
                    }
                } else {
                    queries.add(scQuery);
                }
            }
            //查询条件
            if (queries != null && queries.size() > 0) {
                Predicate constructPredicate = ScQueryUtil.constructQuery(queries, root, builder, linkMap, joinMap);
                if (constructPredicate != null) {
                    ps.add(constructPredicate);
                }
            }

            //排序
            List<Order> orders = ScQueryUtil.constructSort(sorts, root, builder, linkMap, joinMap);
            if (orders.size() > 0) {
                query.orderBy(orders);
            }

            query
                    .distinct(true)
                    .where(builder.and(ps.toArray(new Predicate[]{})));

            return query.getRestriction();

        }, PageRequest.of(bean.getPage(), bean.getSize()));

        calNumOfNode(res.getContent());

        dealWith(res, linkMap);
        return success(res);
    }

    private void calNumOfNode(List<AiotSceneMgr> allNodes) {
        for (AiotSceneMgr node : allNodes) {
            calNumOfNode(node, node);
        }
    }

    private void calNumOfNode(AiotSceneMgr org, AiotSceneMgr current) {
        if (current != null) {
            List<AiotSceneMgr> children = current.getChildren();
            if (children != null) {
                org.setNum(children.size() + org.getNum());
                for (AiotSceneMgr child : children) {
                    calNumOfNode(org, child);
                    calNumOfNode(child, child);
                }
            }
        }
    }

    //处理数据
    private void dealWith(Page<AiotSceneMgr> res, Map<String, MyLink> linkMap) {
        ScQueryUtil.dealWith(res, linkMap);
    }

    @Override
    public AiotSceneMgrRespDTO show(ScShowDTO bean) {
        Optional<AiotSceneMgr> option = aiotSceneMgrRepository.findById(bean.getId());
        if (!option.isPresent()) {
            return null;
        }
        AiotSceneMgr entity = option.get();
        Map<String, MyLink> linkMap = LinkUtil.convertLink(AiotSceneMgr.class);
        AiotSceneMgrRespDTO respDTO = ScQueryUtil.handleOne(entity, linkMap, AiotSceneMgrRespDTO.class);
        List<String> allParentsIds = this.findParentIds(entity.getId());
        if (CollectionUtils.isNotEmpty(allParentsIds)) {
            String allParentIds = allParentsIds.stream().collect(Collectors.joining(","));
            respDTO.setAllParentId(allParentIds);
        }
        return respDTO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public AiotSceneMgr create(AiotSceneMgrCreateDTO bean) {
        AiotSceneMgr entity = new AiotSceneMgr();
        BeanUtil.copyProperties(bean, entity);
        SecurityUser currentUser = ScContext.getCurrentUser();
        if (currentUser == null) {
            throw new ScException(RespCode.CURRENT_USER_NOT_EXIST);
        }

        entity.setSceneCode(SnowflakeGenerator.generate());
        entity.setCreatedBy(currentUser.getId());
        entity.setCreatedByName(currentUser.getUsername());
        aiotSceneMgrRepository.save(entity);
        if (StringUtils.isNotBlank(bean.getPId()) && !bean.getPId().equals("0")) {
            // 找到上一层级
            Optional<AiotSceneMgr> sceneMgrOptional = aiotSceneMgrRepository.findById(bean.getPId());
            if (sceneMgrOptional.isPresent()) {
                AiotSceneMgr aiotSceneMgr = sceneMgrOptional.get();
                entity.setWholeName(aiotSceneMgr.getWholeName() + "," + entity.getSceneName());
                entity.setWholeIds(aiotSceneMgr.getWholeIds() + "," + entity.getId());
            }
        } else {
            entity.setWholeName(entity.getSceneName());
            entity.setWholeIds(entity.getId());
        }
        AiotSceneMgr save = aiotSceneMgrRepository.save(entity);
        LogRecordContext.putVariable("sceneCode", entity.getSceneCode());
        LogRecordContext.putVariable("sceneId", entity.getId());
        return save;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public AiotSceneMgr update(AiotSceneMgrUpdateDTO bean) {
        AiotSceneMgr entity = aiotSceneMgrRepository.findById(bean.getId())
                .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));
        log.error("已查询查询：{}", entity.getSceneName());
        BeanUtil.copyProperties(bean, entity);
        log.error("已复制：{}", entity.getSceneName());
        if (StringUtil.isBlank(entity.getPId())) {
            entity.setPId("0");
        }
        if (StringUtils.isNotBlank(bean.getPId()) && !bean.getPId().equals("0")) {
            // 找到上一层级
            Optional<AiotSceneMgr> sceneMgrOptional = aiotSceneMgrRepository.findById(bean.getPId());
            if (sceneMgrOptional.isPresent()) {
                AiotSceneMgr aiotSceneMgr = sceneMgrOptional.get();
                entity.setWholeName(aiotSceneMgr.getWholeName() + "," + entity.getSceneName());
                entity.setWholeIds(aiotSceneMgr.getWholeIds() + "," + entity.getId());
            }
        } else {
            entity.setWholeName(entity.getSceneName());
            entity.setWholeIds(entity.getId());
        }
        LogRecordContext.putVariable("sceneCode", entity.getSceneCode());
        return aiotSceneMgrRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> ids) {
        SecurityUser currentUser = ScContext.getCurrentUser();
        if (currentUser == null) {
            throw new ScException(RespCode.CURRENT_USER_NOT_EXIST);
        }
        if (CollectionUtils.isNotEmpty(ids)) {
            ids.forEach(id -> aiotSceneMgrRepository.findById(id).ifPresent(entity -> {
                //判断删除的场景类别下是否关联了子类别或者存在场景，如果有则不能删除
                Long senceCount = aiotSceneMgrRepository.countBypId(id);
                if (senceCount > 0) {
                    throw new ScException("该类别关联了子类别，不允许删除");
                }
//                Long senceDetailedCount = aiotSceneDetailedRepository.countBypId(id);
//                if (senceDetailedCount > 0) {
//                    throw new ScException("该类别关联了场景，不允许删除");
//                }
                entity.setIsDeleted(1);
                entity.setUpdatedBy(currentUser.getId());
                entity.setUpdatedByName(currentUser.getUsername());
                aiotSceneMgrRepository.save(entity);
            }));
        }
    }

    @Override
    public boolean export(AiotSceneMgrQueryDTO bean) {

        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        return ScExport.exportAsync(bean.getCfg(),AiotSceneMgr.class, ExportThreadPool.getInstance(),()->{
            List<AiotSceneMgr> content;
            try {
                RequestContextHolder.setRequestAttributes(requestAttributes,true);
                content = ((Page<AiotSceneMgr>) query(bean).getData()).getContent();
            } catch (Exception e) {
                e.printStackTrace();
                throw new ScException("查询失败");
            }
            return new ArrayList<>(
                    content
            );
        });


    }


    public List<String> findParentIds(String id) {
        List<String> parentIds = new ArrayList<>();
        findParentIdsRecursively(id, parentIds);
        return parentIds;
    }

    private void findParentIdsRecursively(String id, List<String> parentIds) {
        AiotSceneMgr aiotSceneMgr = aiotSceneMgrRepository.findById(id).orElse(null);
        if (null != aiotSceneMgr) {
            String parentId = aiotSceneMgr.getPId();
            if (parentId != null) {
                parentIds.add(parentId);
                findParentIdsRecursively(parentId, parentIds);
            }
        }
    }


    @Override
    public List<AiotSceneMgrRespDTO> list(AiotSceneMgrQueryDTO bean) {
        List<AiotSceneMgr> byPIdNot = aiotSceneMgrRepository.findBypIdNot("0");
        if (byPIdNot==null){
            return new ArrayList<>();
        }
        return byPIdNot.stream().map(r-> {
            AiotSceneMgrRespDTO respDTO = new AiotSceneMgrRespDTO();
            BeanUtil.copyProperties(r,respDTO);
            return respDTO;
        }).collect(Collectors.toList());
    }
}