package cn.shencom.server.service.impl;

import cn.shencom.enums.XsgcRespCodeEnum;
import cn.shencom.model.XsgcSubscription;
import cn.shencom.model.dto.create.XsgcSubscriptionCreateDTO;
import cn.shencom.model.dto.query.XsgcSubscriptionQueryDTO;
import cn.shencom.model.dto.resp.XsgcSubscriptionRespDTO;
import cn.shencom.model.dto.update.XsgcSubscriptionUpdateDTO;
import cn.shencom.repos.XsgcCustomerInfoRepository;
import cn.shencom.repos.XsgcCustomerServiceRecordRepository;
import cn.shencom.repos.XsgcSubscriptionRepository;
import cn.shencom.scloud.common.base.constant.RespCode;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.jpa.scpage.MyLink;
import cn.shencom.scloud.common.jpa.util.export.ScExport;
import cn.shencom.scloud.common.jpa.util.query.LinkUtil;
import cn.shencom.scloud.common.jpa.util.query.ScQueryUtil;
import cn.shencom.scloud.common.server.service.BaseImpl;
import cn.shencom.scloud.common.util.BeanUtil;
import cn.shencom.scloud.security.core.ScContext;
import cn.shencom.server.service.IXsgcSubscriptionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.util.*;


/**
 * 小散工程-套餐 的服务实现
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Service
@Slf4j
public class XsgcSubscriptionServiceImpl extends BaseImpl implements IXsgcSubscriptionService {

    @Autowired
    private XsgcSubscriptionRepository xsgcSubscriptionRepository;

    @Autowired
    private XsgcCustomerInfoRepository xsgcCustomerInfoRepository;

    @Autowired
    private XsgcCustomerServiceRecordRepository serviceRecordRepository;

    @Override
    public Page<XsgcSubscriptionRespDTO> query(XsgcSubscriptionQueryDTO bean) {
        Map<String, MyLink> linkMap = LinkUtil.convertLink(XsgcSubscription.class);
        Page<XsgcSubscription> res = xsgcSubscriptionRepository.findAll((root, query, builder) -> {
            //用于拼接条件
            List<Predicate> ps = new ArrayList<>();

            return ScQueryUtil.getPredicate(ps, bean, linkMap, root, query, builder);
        }, PageRequest.of(bean.getPage(), bean.getSize()));
        return ScQueryUtil.handle(res, linkMap, XsgcSubscriptionRespDTO.class);
    }

    @Override
    public XsgcSubscriptionRespDTO show(ScShowDTO bean) {
        Optional<XsgcSubscription> option = xsgcSubscriptionRepository.findById(bean.getId());
        if (!option.isPresent()) {
            return null;
        }
        XsgcSubscription entity = option.get();
        Map<String, MyLink> linkMap = LinkUtil.convertLink(XsgcSubscription.class);
        return ScQueryUtil.handleOne(entity, linkMap, XsgcSubscriptionRespDTO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public XsgcSubscription create(XsgcSubscriptionCreateDTO bean) {
        String userId = ScContext.getCurrentUserThrow().getId();

        //todo 当前默认创建高级套餐
        bean.setType(1);

        if (xsgcSubscriptionRepository.findFirstByName(bean.getName())!=null){
            throw new ScException(XsgcRespCodeEnum.SUBSCRIPTION_NAME_REPEAT.getErrorCode(),XsgcRespCodeEnum.SUBSCRIPTION_NAME_REPEAT.getMsg());
        }

        XsgcSubscription entity = new XsgcSubscription();
        BeanUtil.copyProperties(bean, entity);
        entity.setCreatedUser(userId);
        return xsgcSubscriptionRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public XsgcSubscription update(XsgcSubscriptionUpdateDTO bean) {
        String userId = ScContext.getCurrentUserThrow().getId();

        //todo 当前默认创建高级套餐
        bean.setType(1);

        XsgcSubscription entity = xsgcSubscriptionRepository.findById(bean.getId())
                .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));
        if (bean.getStatus()!=null&&bean.getStatus()==0){
            if (!checkIfCanClose(entity.getId())){
                throw new ScException(XsgcRespCodeEnum.CAN_NOT_CLOSE_SUBSCRIPTION.getErrorCode(),XsgcRespCodeEnum.CAN_NOT_CLOSE_SUBSCRIPTION.getMsg() );
            }
        }

        if (!entity.getName().equals(bean.getName())){
            if (xsgcSubscriptionRepository.findFirstByName(bean.getName())!=null){
                throw new ScException(XsgcRespCodeEnum.SUBSCRIPTION_NAME_REPEAT.getErrorCode(),XsgcRespCodeEnum.SUBSCRIPTION_NAME_REPEAT.getMsg());
            }
        }

        BeanUtil.copyProperties(bean, entity);

        entity.setUpdatedUser(userId);

        return xsgcSubscriptionRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            ids.forEach(id -> xsgcSubscriptionRepository.findById(id).ifPresent(entity -> {
                if (!checkIfCanClose(entity.getId())){
                    throw new ScException(XsgcRespCodeEnum.CAN_NOT_CLOSE_SUBSCRIPTION.getErrorCode(),XsgcRespCodeEnum.CAN_NOT_CLOSE_SUBSCRIPTION.getMsg() );
                }
                entity.setIsDeleted(1);
                entity.setDeletedAt(new Date());
                xsgcSubscriptionRepository.save(entity);
            }));
        }
    }


    /**
     * 校验是否可以删除或者关闭套餐
     * 如果不可以关闭，返回false
     */
    private boolean checkIfCanClose(String subId){
//        //查询是否有客户正在使用当前套餐
//        if (xsgcCustomerInfoRepository.existsByOptionIdAndStatus(subId,1)){
//            throw  new ScException("该套餐当前使用中，无法关闭/删除");
//        }
        //查询是否有服务开通记录
        if (serviceRecordRepository.existsByOptionId(subId)){
            //只要有过开通记录就不能删除/关闭套餐
            return false;
        }

        return true;
    }



    @Override
    public void export(XsgcSubscriptionQueryDTO bean) {
        List<XsgcSubscriptionRespDTO> dtos = new ArrayList<>(query(bean).getContent());
        ScExport.export(bean.getCfg(), dtos, XsgcSubscriptionRespDTO.class);
    }
}