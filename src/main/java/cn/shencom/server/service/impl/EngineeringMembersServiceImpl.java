package cn.shencom.server.service.impl;

import cn.shencom.enums.EngineeringRoleEnum;
import cn.shencom.model.EngineeringMembers;
import cn.shencom.model.EngineeringMembersProjectRelate;
import cn.shencom.model.FnRmsv3MembersTypeRelateBinding;
import cn.shencom.model.SporadicProject;
import cn.shencom.model.dto.create.EngineeringMembersCreateDTO;
import cn.shencom.model.dto.create.InviteCodeBindDTO;
import cn.shencom.model.dto.query.EngineeringMembersQueryDTO;
import cn.shencom.model.dto.query.SporadicProjectQueryDTO;
import cn.shencom.model.dto.resp.EngineeringMembersRespDTO;
import cn.shencom.model.dto.resp.FnRmsv3MembersTypeRelateRespDTO;
import cn.shencom.model.dto.resp.InviteCodeBindRespDTO;
import cn.shencom.model.dto.resp.SporadicProjectRespDTO;
import cn.shencom.model.dto.update.EngineeringMembersUpdateDTO;
import cn.shencom.repos.EngineeringMembersProjectRelateRepository;
import cn.shencom.repos.EngineeringMembersRepository;
import cn.shencom.scloud.common.base.constant.RespCode;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.jpa.scpage.MyLink;
import cn.shencom.scloud.common.jpa.util.export.ScExport;
import cn.shencom.scloud.common.jpa.util.query.LinkUtil;
import cn.shencom.scloud.common.jpa.util.query.ScQueryUtil;
import cn.shencom.scloud.common.server.service.BaseImpl;
import cn.shencom.scloud.common.util.BeanUtil;
import cn.shencom.scloud.scloudapiuaa.dto.SysUsersDTO;
import cn.shencom.scloud.security.core.ScContext;
import cn.shencom.server.manager.UaaManager;
import cn.shencom.server.service.IEngineeringMembersService;
import cn.shencom.server.service.IFnRmsv3MembersTypeRelateService;
import cn.shencom.server.service.ISporadicProjectService;
import cn.shencom.server.service.ISysRolesService;
import cn.shencom.utils.InviteCodeUtil;
import cn.shencom.utils.XsgcContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 小散工程-工程人员 的服务实现
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Service
@Slf4j
public class EngineeringMembersServiceImpl extends BaseImpl implements IEngineeringMembersService {

    @Autowired
    private EngineeringMembersRepository engineeringMembersRepository;



    @Autowired
    private EngineeringMembersProjectRelateRepository engineeringMembersProjectRelateRepository;

    @Autowired
    private UaaManager uaaManager;

    @Autowired
    private ISysRolesService sysRolesService;

    @Autowired
    private ISporadicProjectService sporadicProjectService;


    @Autowired
    private IFnRmsv3MembersTypeRelateService fnRmsv3MembersTypeRelateService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;



    @Override
    public Page<EngineeringMembersRespDTO> query(EngineeringMembersQueryDTO bean) {

        //组织id
        String organizationId = XsgcContext.getOrganizationId();
        String userId = ScContext.getCurrentUserThrow().getId();

        FnRmsv3MembersTypeRelateRespDTO typeRelateRespDTO = null;
        if (organizationId!=null){
            typeRelateRespDTO = fnRmsv3MembersTypeRelateService.getMemberRelateByUserAndOrganization(userId, organizationId);
            if (typeRelateRespDTO==null||typeRelateRespDTO.getRegionIds().isEmpty()){
                return Page.empty(PageRequest.of(bean.getPage(), bean.getSize()));
            }
        }


        Map<String, MyLink> linkMap = LinkUtil.convertLink(EngineeringMembers.class);
        FnRmsv3MembersTypeRelateRespDTO finalTypeRelateRespDTO = typeRelateRespDTO;
        Page<EngineeringMembers> res = engineeringMembersRepository.findAll((root, query, builder) -> {
            //用于拼接条件
            List<Predicate> ps = new ArrayList<>();

            if (bean.getType()!=null){
                ps.add(builder.equal(root.get("type"),bean.getType()));
            }


            if (finalTypeRelateRespDTO !=null){
                Integer level = finalTypeRelateRespDTO.getLevel();
                List<FnRmsv3MembersTypeRelateBinding> regionIds = finalTypeRelateRespDTO.getRegionIds();
                // 联表
                Join<EngineeringMembers, EngineeringMembersProjectRelate> projectRelateJoin =
                        root.join("membersProjectRelateList", JoinType.INNER);

                // 2. 关联到 SporadicProject
                Join<EngineeringMembersProjectRelate, SporadicProject> projectJoin =
                        projectRelateJoin.join("project", JoinType.INNER);

                //根据组织id筛选
                ps.add(builder.equal(projectRelateJoin.get("organizationId"),organizationId));


                //根据区域筛选
                if (!regionIds.isEmpty()&&level!=null) {

                    if (level == 1) {
                        List<String> regionIdValues = regionIds.stream()
                                .map(FnRmsv3MembersTypeRelateBinding::getRegionPid)
                                .collect(Collectors.toList());
                        // 根据regionPid进行筛选
                        ps.add(builder.in(projectJoin.get("regionPid")).value(regionIdValues));
                    } else if (level == 2) {
                        List<String> regionIdValues = regionIds.stream()
                                .map(FnRmsv3MembersTypeRelateBinding::getRegionId)
                                .collect(Collectors.toList());
                        // 根据regionId进行筛选
                        ps.add(builder.in(projectJoin.get("regionId")).value(regionIdValues));
                    } else if (level == 3) {
                        List<String> regionIdValues = regionIds.stream()
                                .map(FnRmsv3MembersTypeRelateBinding::getRegionCid)
                                .collect(Collectors.toList());
                        // 根据regionCid进行筛选
                        ps.add(builder.in(projectJoin.get("regionCid")).value(regionIdValues));
                    }
                }

            }

            return ScQueryUtil.getPredicate(ps, bean, linkMap, root, query, builder);
        }, PageRequest.of(bean.getPage(), bean.getSize()));
        return ScQueryUtil.handle(res, linkMap, EngineeringMembersRespDTO.class);
    }

    @Override
    public EngineeringMembersRespDTO show(ScShowDTO bean) {
        Optional<EngineeringMembers> option = engineeringMembersRepository.findById(bean.getId());
        if (!option.isPresent()) {
            return null;
        }
        EngineeringMembers entity = option.get();
        Map<String, MyLink> linkMap = LinkUtil.convertLink(EngineeringMembers.class);
        return ScQueryUtil.handleOne(entity, linkMap, EngineeringMembersRespDTO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public EngineeringMembers create(EngineeringMembersCreateDTO bean) {
        EngineeringMembers entity = new EngineeringMembers();
        BeanUtil.copyProperties(bean, entity);
        return engineeringMembersRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public EngineeringMembers update(EngineeringMembersUpdateDTO bean) {
        EngineeringMembers entity = engineeringMembersRepository.findById(bean.getId())
                .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));
        BeanUtil.copyProperties(bean, entity);
        return engineeringMembersRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            ids.forEach(id -> engineeringMembersRepository.findById(id).ifPresent(entity -> {
                entity.setIsDeleted(1);
                entity.setDeletedAt(new Date());

                //查询当前成员关联的全部组织
                List<EngineeringMembersProjectRelate> membersRelateList = engineeringMembersProjectRelateRepository.findByMemberId(entity.getId());

                if (!membersRelateList.isEmpty()){
                    //删除成员时，删除全部关联
                    for (EngineeringMembersProjectRelate relate : membersRelateList) {
                        relate.setDeletedAt(new Date());
                        relate.setIsDeleted(1);
                    }
                    engineeringMembersProjectRelateRepository.saveAll(membersRelateList);
                }

                //移除成员的权限
                sysRolesService.deleteRoleForUser(EngineeringRoleEnum.getRoleByType(entity.getType()),entity.getUserId());

                engineeringMembersRepository.save(entity);
            }));
        }
    }

    @Override
    public void export(EngineeringMembersQueryDTO bean) {
        List<EngineeringMembersRespDTO> dtos = new ArrayList<>(query(bean).getContent());
        ScExport.export(bean.getCfg(), dtos, EngineeringMembersRespDTO.class);
    }


    /**
     * 创建或更新
     * @param bean
     * @return
     */
    @Override
    @Transactional
    public EngineeringMembers createOrUpdate(EngineeringMembersUpdateDTO bean) {

        String projectId = bean.getProjectId();
        String mobile = bean.getMobile();
        String realname =bean.getRealname();
        Integer type= bean.getType();
        String organizationId = bean.getOrganizationId();


        //根据电话和职位查询成员是否存在
        EngineeringMembers members = engineeringMembersRepository.findFirstByMobile( mobile);
        if (members!=null&&!members.getType().equals(type)){
            throw new ScException(String.format("创建工程失败！当前手机号绑定的用户已经是 %s ,无法再成为 %s ",
                    EngineeringRoleEnum.getNameByType(members.getType()), EngineeringRoleEnum.getNameByType(type)));
        }

        //如果不存在就创建
        if (members==null){
            //查询系统用户是否存在
            SysUsersDTO sysUsersDTO = uaaManager.findByPhone(mobile);
            if (sysUsersDTO==null){
                //如果系统用户不存在就创建用户
                SysUsersDTO dto =new SysUsersDTO();
                dto.setIsLock(1);
                dto.setRealname(bean.getRealname());
                dto.setPhone(mobile);
                sysUsersDTO = uaaManager.userInit(dto);
            }
            String userId = sysUsersDTO.getId();


            //创建成员
            members = new EngineeringMembers();
            members.setRealname(realname);
            members.setMobile(mobile);
            members.setType(type);
            members.setUserId(userId);
            members = engineeringMembersRepository.save(members);


            //为用户添加权限
            sysRolesService.addRoleForUser(EngineeringRoleEnum.getRoleByType(members.getType()),members.getUserId());

        }else {
            //更改手机号码和姓名
            members.setMobile(mobile);
            members.setRealname(realname);

        }

        //关联成员和项目
        EngineeringMembersProjectRelate projectRelate = engineeringMembersProjectRelateRepository.findByMemberIdAndProjectId( members.getId(),projectId);
        if (projectRelate==null){
            //如果未创建成员和项目的关联关系，则创建
            projectRelate = new EngineeringMembersProjectRelate();
            projectRelate.setProjectId(projectId);
            projectRelate.setMemberId(members.getId());
            projectRelate.setOrganizationId(organizationId);
            engineeringMembersProjectRelateRepository.save(projectRelate);
        }

        return members;
    }


    @Override
    public Page<SporadicProjectRespDTO> projectPage(EngineeringMembersQueryDTO bean) {
        List<EngineeringMembersProjectRelate> projectRelateList = engineeringMembersProjectRelateRepository.findByMemberId(bean.getId());
        if (projectRelateList.isEmpty()){
            return Page.empty(PageRequest.of(bean.getPage(), bean.getSize()));
        }

        List<String> projectIds = projectRelateList.stream().map(EngineeringMembersProjectRelate::getProjectId).collect(Collectors.toList());


        SporadicProjectQueryDTO queryDTO = new SporadicProjectQueryDTO();
        queryDTO.setIds(projectIds);
        queryDTO.setPage(bean.getPage());
        queryDTO.setSize(bean.getSize());
        Page<SporadicProjectRespDTO> page = sporadicProjectService.queryByIds(queryDTO);

        return page;
    }


    @Override
    @Transactional
    public void removeMemberRelateByProjectId(String projectId) {
        List<EngineeringMembersProjectRelate> relateList = engineeringMembersProjectRelateRepository.findByProjectId(projectId);
        if (!relateList.isEmpty()){
            for (EngineeringMembersProjectRelate relate : relateList) {
                relate.setIsDeleted(1);
                relate.setDeletedAt(new Date());
            }
            engineeringMembersProjectRelateRepository.saveAll(relateList);
        }

    }

    @Override
    @Transactional
    public InviteCodeBindRespDTO bindProjectByInviteCode(InviteCodeBindDTO bean) {
        // 1. 验证邀请码是否有效
        String redisKey = InviteCodeUtil.getRedisKey(bean.getInviteCode());
        String projectId = stringRedisTemplate.opsForValue().get(redisKey);

        if (StringUtils.isEmpty(projectId)) {
            throw new ScException("邀请码无效或已过期");
        }

        // 2. 验证职位类型是否有权限绑定
        if (!hasBindPermission(bean.getType())) {
            throw new ScException("该职位类型无权限绑定工程");
        }

        // 3. 查询工程信息
        ScShowDTO showDTO = new ScShowDTO();
        showDTO.setId(projectId);
        SporadicProjectRespDTO projectResp = sporadicProjectService.show(showDTO);
        if (projectResp == null) {
            throw new ScException("工程不存在");
        }

        // 4. 获取当前用户信息
        String userId = ScContext.getCurrentUserThrow().getId();

        // 5. 检查是否已经绑定过该工程
        EngineeringMembers existingMember = engineeringMembersRepository.findFirstByUserId(userId);
        if (existingMember != null) {
            // 检查是否已经绑定过该工程
            EngineeringMembersProjectRelate existingRelate =
                    engineeringMembersProjectRelateRepository.findByMemberIdAndProjectId(
                            existingMember.getId(), projectId);
            if (existingRelate != null) {
                throw new ScException("您已经绑定过该工程");
            }
        }

        // 6. 创建或更新工程成员
        boolean isNewMember = false;
        EngineeringMembers member;

        if (existingMember != null) {
            // 更新现有成员信息
            member = existingMember;
            member.setRealname(bean.getRealname());
            member.setMobile(bean.getMobile());
            member.setIdCard(bean.getIdCard());
            member.setWorkTypeName(bean.getWorkTypeName());
            member.setCertificateNumber(bean.getCertificateNumber());
            member.setCertificateStartDate(bean.getCertificateStartDate());
            member.setCertificateEndDate(bean.getCertificateEndDate());
            member.setCertificatePic(bean.getCertificatePic());
            member.setDesc(bean.getDesc());
        } else {
            // 创建新成员
            isNewMember = true;
            member = new EngineeringMembers();
            member.setUserId(userId);
            member.setRealname(bean.getRealname());
            member.setMobile(bean.getMobile());
            member.setType(bean.getType());
            member.setIdCard(bean.getIdCard());
            member.setWorkTypeName(bean.getWorkTypeName());
            member.setCertificateNumber(bean.getCertificateNumber());
            member.setCertificateStartDate(bean.getCertificateStartDate());
            member.setCertificateEndDate(bean.getCertificateEndDate());
            member.setCertificatePic(bean.getCertificatePic());
            member.setDesc(bean.getDesc());
            member.setStatus(1); // 有效状态

            // 为用户添加权限
            sysRolesService.addRoleForUser(EngineeringRoleEnum.getRoleByType(bean.getType()), userId);
        }

        member = engineeringMembersRepository.save(member);

        // 7. 创建工程成员关联关系
        EngineeringMembersProjectRelate relate = new EngineeringMembersProjectRelate();
        relate.setMemberId(member.getId());
        relate.setProjectId(projectId);
        relate.setCreatedAt(new Date());
        relate.setUpdatedAt(new Date());
        relate.setIsDeleted(0);

        engineeringMembersProjectRelateRepository.save(relate);

        // 8. 构建响应
        InviteCodeBindRespDTO response = new InviteCodeBindRespDTO();
        response.setMemberId(member.getId());
        response.setProjectId(projectId);
        response.setProjectName(projectResp.getName());
        response.setRealname(member.getRealname());
        response.setMobile(member.getMobile());
        response.setType(member.getType());
        response.setTypeName(EngineeringRoleEnum.getNameByType(member.getType()));
        response.setBindTime(new Date());
        response.setIsNewMember(isNewMember);

        return response;
    }

    /**
     * 验证职位类型是否有绑定权限
     * 施工负责人、施工工人、建筑方（业主）可以绑定工程
     */
    private boolean hasBindPermission(Integer type) {
        return type.equals(EngineeringRoleEnum.CONSTRUCTION_PARTY.getMemberType()) ||
               type.equals(EngineeringRoleEnum.CONSTRUCTION_UNIT_LEADER.getMemberType()) ||
               type.equals(EngineeringRoleEnum.CONSTRUCTION_WORKER.getMemberType());
    }
}