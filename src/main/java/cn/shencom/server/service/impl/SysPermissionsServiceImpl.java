package cn.shencom.server.service.impl;

import cn.shencom.repos.SysPermissionsRepository;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.base.constant.RespCode;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.scloud.common.server.service.BaseImpl;
import cn.shencom.scloud.security.core.ScContext;
import cn.shencom.scloud.security.core.SecurityUser;
import cn.shencom.server.service.ISysPermissionsService;
import cn.shencom.utils.XsgcContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * SysPermissions的服务接口的实现类
 *
 * <AUTHOR>
@Service
@Slf4j
public class SysPermissionsServiceImpl extends BaseImpl implements ISysPermissionsService {

    @Autowired
    private SysPermissionsRepository sysPermissionsRepository;


    @Override
    public Result allPermission() {
        SecurityUser currentUser = ScContext.getCurrentUser();
        String organizationId = XsgcContext.getOrganizationId();
        if (currentUser == null) {
            throw new ScException(RespCode.CURRENT_USER_NOT_EXIST);
        }
        Set<String> nameByUserId;
        if (organizationId==null){
            nameByUserId = sysPermissionsRepository.findNameByUserId(currentUser.getId());
        }else {
            nameByUserId = sysPermissionsRepository.findNameByUserIdAndOrganizationId(currentUser.getId(),organizationId);
        }

        return success(nameByUserId);
    }
}