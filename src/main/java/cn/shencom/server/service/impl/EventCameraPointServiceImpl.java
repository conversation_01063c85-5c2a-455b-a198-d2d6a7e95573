package cn.shencom.server.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.shencom.constant.CommonConstant;
import cn.shencom.feign.IServiceGuanclassifyRecycle;
import cn.shencom.model.EventCameraPoint;
import cn.shencom.model.EventCameraPointDevice;
import cn.shencom.model.XsgcBusinessMembers;
import cn.shencom.model.dto.CameraTreeRegionDTO;
import cn.shencom.model.dto.ExApiDeviceStatusResp;
import cn.shencom.model.dto.SmartCameraLiveDTO;
import cn.shencom.model.dto.create.EventCameraPointCreateDTO;
import cn.shencom.model.dto.query.EventCameraPointQueryDTO;
import cn.shencom.model.dto.query.SporadicProjectQueryDTO;
import cn.shencom.model.dto.resp.EventCameraPointRespDTO;
import cn.shencom.model.dto.resp.ExApiCameraStatusRespDTO;
import cn.shencom.model.dto.resp.ExApiDevicePlaybackListRespDTO;
import cn.shencom.model.dto.resp.LiveRespDTO;
import cn.shencom.model.dto.resp.SporadicProjectRespDTO;
import cn.shencom.model.dto.update.EventCameraPointUpdateDTO;
import cn.shencom.repos.ComRegionRepository;
import cn.shencom.repos.EventCameraPointDeviceRepository;
import cn.shencom.repos.EventCameraPointRepository;
import cn.shencom.repos.XsgcBusinessMembersRepository;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.base.constant.NumberConstant;
import cn.shencom.scloud.common.base.constant.RespCode;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.scloud.common.base.util.BizAssert;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.jpa.scpage.MyLink;
import cn.shencom.scloud.common.jpa.util.export.ScExport;
import cn.shencom.scloud.common.jpa.util.query.LinkUtil;
import cn.shencom.scloud.common.jpa.util.query.ScQueryUtil;
import cn.shencom.scloud.common.server.service.BaseImpl;
import cn.shencom.scloud.common.util.BeanUtil;
import cn.shencom.scloud.common.util.ScidContext;
import cn.shencom.scloud.security.core.ScContext;
import cn.shencom.server.service.IEventCameraPointService;
import cn.shencom.server.service.ISporadicProjectService;
import cn.shencom.utils.AiotUtil;
import cn.shencom.utils.UserUtil;

import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Table;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 摄像头信息表 的服务实现
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Service
@Slf4j
public class EventCameraPointServiceImpl extends BaseImpl implements IEventCameraPointService {
    @Autowired
    private EventCameraPointRepository eventCameraPointRepository;

    @Autowired
    private EventCameraPointDeviceRepository eventCameraPointDeviceRepository;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private ComRegionRepository comRegionRepository;

    @Autowired
    private IServiceGuanclassifyRecycle serviceGuanclassifyRecycle;

    @Autowired
    private AiotUtil aiotUtil;

    @Autowired
    private ISporadicProjectService sporadicProjectService;

    @Resource
    private Environment environment;

    @Autowired
    private XsgcBusinessMembersRepository xsgcBusinessMembersRepository;


    @Override
    public Page<EventCameraPointRespDTO> query(EventCameraPointQueryDTO bean) {
        SporadicProjectQueryDTO sporadicProjectQueryDTO = new SporadicProjectQueryDTO();

        if (StringUtils.isNotBlank(bean.getProjectName())) {
            sporadicProjectQueryDTO.setName(bean.getProjectName());
        }
        List<SporadicProjectRespDTO> projectList = sporadicProjectService.getUserProjectList(sporadicProjectQueryDTO);
        if (CollectionUtils.isEmpty(projectList)) {
            return Page.empty(PageRequest.of(bean.getPage(), bean.getSize()));
        }

        List<String> projectIds = projectList.stream().map(SporadicProjectRespDTO::getId).collect(Collectors.toList());
        bean.setProjectId(projectIds);

        Map<String, MyLink> linkMap = LinkUtil.convertLink(EventCameraPoint.class);
        Page<EventCameraPoint> res = eventCameraPointRepository.findAll((root, query, builder) -> {
            // 用于拼接条件
            List<Predicate> ps = new ArrayList<>();

            return ScQueryUtil.getPredicate(ps, bean, linkMap, root, query, builder);
        }, PageRequest.of(bean.getPage(), bean.getSize()));
        return ScQueryUtil.handle(res, linkMap, EventCameraPointRespDTO.class);
    }

    @Override
    public EventCameraPointRespDTO show(ScShowDTO bean) {
        Optional<EventCameraPoint> option = eventCameraPointRepository.findById(bean.getId());
        if (!option.isPresent()) {
            return null;
        }
        EventCameraPoint entity = option.get();
        Map<String, MyLink> linkMap = LinkUtil.convertLink(EventCameraPoint.class);
        return ScQueryUtil.handleOne(entity, linkMap, EventCameraPointRespDTO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String create(EventCameraPointDevice device, EventCameraPointCreateDTO bean) {
        Date date = new Date();
        String cameraId = null;
        if (StringUtils.isNotBlank(bean.getChannel())) {
            String[] channels = bean.getChannel().split(",");
            for (String channel : channels) {
                String monitorNo = splitMonitorNo(bean.getSerialNo(), channel);

                // 判断
                EventCameraPoint cameraPoint = eventCameraPointRepository.findFirstByMonitorNo(monitorNo);
                if (cameraPoint != null) {
                    throw new ScException(RespCode.CAMERA_SERIAL_NO_REPEAT);
                }

                if (bean.getType() == 22) {
                    EventCameraPoint firstCameraBySipUserIdAndChannel = eventCameraPointRepository
                            .findFirstBySipUserIdAndChannel(bean.getSipUserId(), channel);
                    if (firstCameraBySipUserIdAndChannel != null) {
                        throw new ScException(
                                "sip+通道号重复!无法添加。重复设备序列号：" + firstCameraBySipUserIdAndChannel.getSerialNo());
                    }
                }

                EventCameraPoint entity = new EventCameraPoint();
                BeanUtil.copyProperties(bean, entity);
                checkCameraExist(null, device, bean.getChannel());
                entity.setIsDeleted(0);
                entity.setCreatedAt(date);
                entity.setUpdatedAt(date);
                entity.setChannel(channel);
                // 设置设备号
                entity.setMonitorNo(monitorNo);
                EventCameraPoint point = eventCameraPointRepository.save(entity);
                cameraId = point.getId();
            }
        } else {
            EventCameraPoint cameraPoint = eventCameraPointRepository.findFirstBySerialNo(bean.getSerialNo());
            if (cameraPoint != null) {
                throw new ScException(RespCode.CAMERA_SERIAL_NO_REPEAT);
            }
            EventCameraPoint entity = new EventCameraPoint();
            BeanUtil.copyProperties(bean, entity);
            checkCameraExist(null, device, "1");
            entity.setIsDeleted(0);
            entity.setCreatedAt(date);
            entity.setUpdatedAt(date);
            EventCameraPoint point = eventCameraPointRepository.save(entity);
            cameraId = point.getId();
        }
        return cameraId;
    }

    private void checkCameraExist(String cameraId, EventCameraPointDevice device, String channel) {
        if (Integer.valueOf(22).equals(device.getType())) {
            // check sip user id
            Integer cnt = eventCameraPointDeviceRepository.countBySipNoAndChannel(device.getSipUserId(), channel,
                    cameraId);
            if (Objects.nonNull(cnt) && cnt > 0) {
                throw new ScException("设备存在，无法新增");
            }
        } else {
            // check other
            // check sip user id
            Integer cnt = eventCameraPointDeviceRepository.countBySerialNoAndChannel(device.getSerialNo(), channel,
                    cameraId);
            if (Objects.nonNull(cnt) && cnt > 0) {
                throw new ScException("设备存在，无法新增");
            }
        }
    }

    @Override
    public List<Map<String, String>> getCameraType(EventCameraPointQueryDTO bean) {
        return eventCameraPointRepository.getCameraType(bean);
    }

    private String splitMonitorNo(String serialNo, String channel) {
        return serialNo + "#" + channel;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public EventCameraPoint update(EventCameraPointDevice device, EventCameraPointUpdateDTO bean) {
        EventCameraPoint entity = eventCameraPointRepository.findById(bean.getId())
                .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));
        BeanUtil.copyProperties(bean, entity);
        entity.setProjectId(device.getProjectId());
        checkCameraExist(bean.getId(), device, bean.getChannel());
        setGb28181MonitorNo(entity);
        entity.setOpenLive(0);
        return eventCameraPointRepository.save(entity);
    }

    private void setGb28181MonitorNo(EventCameraPoint bean) {
        if (Objects.nonNull(bean.getChannel())) {
            // 设置设备号
            bean.setMonitorNo(splitMonitorNo(bean.getSerialNo(), bean.getChannel()));
        } else {
            bean.setChannel("1");
            bean.setMonitorNo(bean.getSerialNo() + "#" + bean.getChannel());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            ids.forEach(id -> eventCameraPointRepository.findById(id).ifPresent(entity -> {
                entity.setIsDeleted(1);
                entity.setDeletedAt(new Date());
                eventCameraPointRepository.save(entity);
            }));
        }
    }

    @Override
    public void export(EventCameraPointQueryDTO bean) {
        List<EventCameraPointRespDTO> dtos = new ArrayList<>(query(bean).getContent());
        ScExport.export(bean.getCfg(), dtos, EventCameraPointRespDTO.class);
    }

    @Override
    public Result<List<CameraTreeRegionDTO>> treeQueryAll(EventCameraPointQueryDTO bean, HttpServletResponse response) {
        List<CameraTreeRegionDTO> treePointDataListRes;
        if (ScContext.getCurrentUser() == null) {
            response.setStatus(401);
            return error("未登录。");
        }

        //判断当前是否是业务人员，如果是就不做限制（此处为了避免后续项目过多导致查询大量工程id）
        XsgcBusinessMembers xsgcBusinessMembers = xsgcBusinessMembersRepository
                .findFirstByUserIdAndStatus(ScContext.getCurrentUser().getId(), 1);

        if (xsgcBusinessMembers==null){
            // 获取用权限的项目id
            List<SporadicProjectRespDTO> projectList = sporadicProjectService.getUserProjectList();
            if (CollectionUtils.isEmpty(projectList)) {
                return success(new ArrayList<>());
            }

            List<String> projectIds = projectList.stream().map(SporadicProjectRespDTO::getId).collect(Collectors.toList());
            bean.setProjectId(projectIds);
        }

        try {
            treePointDataListRes = getTreePointDataList(bean);
        } catch (Exception e) {
            long start = System.currentTimeMillis();
            List<CameraTreeRegionDTO> treePointDataList = eventCameraPointRepository.getBuildTreePointData(bean);
            long end = System.currentTimeMillis();
            log.info("查询sql信息耗时-----" + (end - start) + "ms");
            treePointDataListRes = buildTreeNodes(treePointDataList).getChildren();
        }
        return success(treePointDataListRes);
    }

    @Override
    public List<LiveRespDTO> getLiveUrlByAreaId(EventCameraPointQueryDTO bean) {
        if (bean.getAreaId() == null) {
            throw new ScException("areaId不能为空！");
        }
        List<CameraTreeRegionDTO> cameraList = eventCameraPointRepository.getBuildTreePointData(bean);
        List<LiveRespDTO> resp = new ArrayList<>();
        for (CameraTreeRegionDTO dto : cameraList) {
            EventCameraPointQueryDTO queryDTO = new EventCameraPointQueryDTO();
            String[] split = dto.getMonitorNo().split("#");
            queryDTO.setSerialNo(split[0]);
            queryDTO.setChannelNo(split[1]);
            queryDTO.setType(dto.getType());
            LiveRespDTO live = getLive(queryDTO);
            resp.add(live);
        }
        return resp;
    }

    public List<CameraTreeRegionDTO> getTreePointDataList(EventCameraPointQueryDTO bean) {
        List<CameraTreeRegionDTO> dDataList = eventCameraPointRepository.getBuildTreePointData(bean);
        List<CameraTreeRegionDTO> treePointDataList = buildTreeNodes(dDataList).getChildren();
        AtomicReference<List<CameraTreeRegionDTO>> treePointDataListRes = new AtomicReference<>(new ArrayList<>());

        treePointDataListRes.set(treePointDataList);
        return treePointDataListRes.get();
    }

    private CameraTreeRegionDTO buildTreeNodes(List<CameraTreeRegionDTO> treePointDataList) {
        long start = System.currentTimeMillis();
        Map<String, SmartCameraLiveDTO> smartCameraMap = this.getStringSmartCameraLiveDTOMap();
        long end = System.currentTimeMillis();
        log.info("远程调用获取自建摄像头信息耗时-----" + (end - start) + "ms");
        // 创建一个顶层节点
        CameraTreeRegionDTO root = new CameraTreeRegionDTO();
        root.setChildren(new ArrayList<>());

        // 拿区域映射缓存
        String key = String.format("%s:%s:%s", ScidContext.getScid(),
                environment.getProperty("spring.application.name"), CommonConstant.REGION_SIMPLE_MAP_KEY);
        Map<String, String> regionsMap = new HashMap<>();
        try {
            regionsMap = redisTemplate.opsForHash().entries(key);
        } catch (Exception e) {
            log.debug("service-rms获取区域映射缓存失败！！！！！");
        }
        start = System.currentTimeMillis();
        CameraTreeRegionDTO dto = buildTreeNodes(root, treePointDataList, 1, smartCameraMap, regionsMap);
        end = System.currentTimeMillis();
        log.info("构建监控树树耗时-----" + (end - start) + "ms");
        return dto;
    }

    private CameraTreeRegionDTO buildTreeNodes(CameraTreeRegionDTO root, List<CameraTreeRegionDTO> treePointDataList,
            Integer level,
            Map<String, SmartCameraLiveDTO> smartCameraMap, Map<String, String> regionsMap) {
        // 当前节点下的子节点
        List<CameraTreeRegionDTO> child = Lists.newArrayList();
        // stream 流构建当前区域，及下级的list数据 Map<String, List<CameraTreeRegionDTO>>
        Map<String, List<CameraTreeRegionDTO>> levelMap = getLevelMap(treePointDataList, level);

        for (Map.Entry<String, List<CameraTreeRegionDTO>> map : levelMap.entrySet()) {
            String[] split = map.getKey().split(",");
            for (String id : split) {
                // 下一级节点数据
                List<CameraTreeRegionDTO> nextLevelList = map.getValue();
                if (CollectionUtils.isNotEmpty(nextLevelList)) {
                    if ((level == 5)) {
                        // 摄像头级别终止递归
                        nextLevelList.stream().map(e -> {
                            EventCameraPoint point = new EventCameraPoint();
                            BeanUtil.copyProperties(e, point);
                            CameraTreeRegionDTO dto;
                            dto = getCameraDTO(smartCameraMap, point, e.getStatus());
                            return dto;
                        }).forEach(child::add);
                    } else {
                        // 递归下一步 某一级别为空 将root节点往下递归
                        boolean isNull = "0".equals(id);
                        CameraTreeRegionDTO node = root;
                        int nextLevel = level + 1;
                        if (isNull) {
                            if (level < 3) {
                                nextLevel = 4;
                            }
                        } else {
                            // 把作为root的子节点node往下传递
                            node = new CameraTreeRegionDTO(id,
                                    this.getLevelName(id, level, nextLevelList.get(0), regionsMap));
                            child.add(node);
                        }
                        buildTreeNodes(node, nextLevelList, nextLevel, smartCameraMap, regionsMap);
                    }
                }
            }
        }
        // 将子节点追加到父节点中
        root.getChildren().addAll(child);
        return root;
    }

    private String getLevelName(String id, Integer level, CameraTreeRegionDTO dto, Map<String, String> regionsMap) {
        if (level < 4) {
            String name = "";
            name = regionsMap.get(id);
            if (StringUtils.isBlank(name) || "null".equals(name)) {
                name = comRegionRepository.getTitleById(id);
                try {
                    serviceGuanclassifyRecycle.updateRegionRedis(ScidContext.getScid());
                } catch (Exception e) {
                    log.error("刷新区域映射的redis失败");
                }
            }
            return name;
        } else if (level == 4) {
            return dto.getProjectName();
        } else if (level == 5) {
            return dto.getName();
        }
        return null;
    }

    private CameraTreeRegionDTO getCameraDTO(Map<String, SmartCameraLiveDTO> smartCameraMap, EventCameraPoint e,
            Integer status) {
        if (Objects.nonNull(e.getIsLock()) && e.getIsLock() == 1 && Integer.valueOf("0").equals(status)) {
            e.setFlvAddress(null);
            e.setHdFlvAddress(null);
        }
        return Integer.valueOf(17).equals(e.getType()) ? CameraTreeRegionDTO.builder()
                .monitorNo(e.getMonitorNo())
                .monitorName(e.getMonitorName())
                .openLive(status)
                .type(e.getType())
                .modelNo(e.getModelNo())
                .url(smartCameraMap.get(e.getMonitorNo()) == null ? "" : smartCameraMap.get(e.getMonitorNo()).getUrl())
                .build()
                : CameraTreeRegionDTO.builder()
                        .monitorNo(e.getMonitorNo())
                        .monitorName(e.getMonitorName())
                        .openLive(status)
                        .type(e.getType())
                        .modelNo(e.getModelNo())
                        .flvAddress(e.getFlvAddress())
                        .hdFlvAddress(e.getHdFlvAddress())
                        .build();
    }

    private Map<String, List<CameraTreeRegionDTO>> getLevelMap(List<CameraTreeRegionDTO> treePointDataList,
            Integer level) {
        Map<String, List<CameraTreeRegionDTO>> levelMap;
        if (level == 1) {
            levelMap = treePointDataList.stream()
                    .sorted(Comparator.comparingInt(CameraTreeRegionDTO::getDistrictSort).reversed())
                    .collect(Collectors.groupingBy(CameraTreeRegionDTO::getRegionPid, LinkedHashMap::new,
                            Collectors.toList()));
        } else if (level == 2) {
            levelMap = treePointDataList.stream().collect(Collectors.groupingBy(CameraTreeRegionDTO::getRegionId));
        } else if (level == 3) {
            levelMap = treePointDataList.stream().collect(Collectors.groupingBy(CameraTreeRegionDTO::getRegionCid));
        } else if (level == 4) {
            levelMap = treePointDataList.stream().collect(Collectors.groupingBy(CameraTreeRegionDTO::getProjectId));
        } else {
            levelMap = treePointDataList.stream().collect(Collectors.groupingBy(CameraTreeRegionDTO::getId));
        }
        return levelMap;
    }

    private Map<String, SmartCameraLiveDTO> getStringSmartCameraLiveDTOMap() {
        List<Map<String, Object>> smartCameraLiveData = eventCameraPointRepository.getAllSmartCameraLiveData();

        return smartCameraLiveData
                .stream()
                // .map(s -> new SmartCameraLiveDTO(s.get("webcam") == null ? "" :
                // this.getUrl(sflDomain, s.get("webcam").toString()),
                // s.get("monitorNo").toString()))
                .map(s -> {
                    return new SmartCameraLiveDTO(null, s.get("monitorNo").toString());
                })
                .collect(Collectors.toMap(SmartCameraLiveDTO::getMonitorNo, (o) -> o));
    }

    @Transactional
    @Override
    public Result cameraOpen(EventCameraPointQueryDTO bean) {
        getCameraStatus(bean, 1);
        return success();
    }

    @Transactional
    @Override
    public void syncCameraOnline() {
        EventCameraPointQueryDTO bean = new EventCameraPointQueryDTO();
        getCameraStatus(bean, 1000);
    }

    private void getCameraStatus(EventCameraPointQueryDTO bean, Integer pageSize) {
        Pageable pageable = PageRequest.of(0, pageSize);
        Slice<EventCameraPointRespDTO> page;
        do {
            page = eventCameraPointRepository.findSliceSerialNoChannel(bean, pageable);
            List<EventCameraPointRespDTO> content = page.getContent();
            Table<String, String, String> sipUserIdToSerialNoMap = HashBasedTable.create();
            List<ExApiCameraStatusRespDTO> data = aiotUtil.getCameraStatus(sipUserIdToSerialNoMap, content);
            data.forEach(re -> {
                String deviceCode = sipUserIdToSerialNoMap.get(re.getDeviceCode(), re.getPeripheryCode());
                if (StringUtils.isBlank(deviceCode))
                    deviceCode = re.getDeviceCode();

                if (NumberConstant.ONE_INT.equals(re.getStatus())) {
                    eventCameraPointRepository.updateCameraStatus(deviceCode, re.getPeripheryCode(), re.getStatus());
                } else {
                    eventCameraPointRepository.updateCameraStatus(deviceCode, re.getPeripheryCode(), 0);
                }
            });
            log.debug("已同步 {} 摄像头状态", content.size());
            pageable = page.nextPageable();
        } while (page.hasNext());
    }

    @Override
    public LiveRespDTO getLive(EventCameraPointQueryDTO bean) {
        EventCameraPoint eventCameraPoint = checkExists(bean);
        LiveRespDTO respDTO = new LiveRespDTO();
        respDTO.setFlvAddress(eventCameraPoint.getFlvAddress());
        respDTO.setHdFlvAddress(eventCameraPoint.getHdFlvAddress());
        if (StringUtils.isBlank(respDTO.getFlvAddress()) && StringUtils.isBlank(respDTO.getHdFlvAddress())) {
            String liveUrl = aiotUtil.getLiveUrl(bean.getSerialNo(), bean.getChannelNo());
            respDTO.setFlvAddress(liveUrl);
        }
        return respDTO;
    }

    /**
     * 处理类型和序列号
     *
     * @param bean
     */
    private EventCameraPoint checkExists(EventCameraPointQueryDTO bean) {
        EventCameraPoint eventCameraPoint = eventCameraPointRepository.findFirstBySerialNoAndChannel(bean.getSerialNo(),
                bean.getChannelNo());
        if (eventCameraPoint != null) {
            EventCameraPointDevice pointDevice = eventCameraPointDeviceRepository
                    .findById(eventCameraPoint.getDeviceId()).orElse(null);
            if (pointDevice == null) {
                throw new ScException("找不到该摄像头");
            }
            if (Integer.valueOf(22).equals(pointDevice.getType())
                    && StringUtils.isNotBlank(pointDevice.getSipUserId())) {
                bean.setSerialNo(pointDevice.getSipUserId());
            }
        } else {
            throw new ScException("找不到该摄像头");
        }
        return eventCameraPoint;
    }

    @Override
    public LiveRespDTO getHistory(EventCameraPointQueryDTO bean) {
        checkExists(bean);
        BizAssert.notNull(bean.getStartTime(), "开始时间不能为空");
        // 如果没有结束时间, 开始时间 + 15min
        if (Objects.isNull(bean.getEndTime())) {
            bean.setEndTime(DateUtil.offsetMinute(bean.getStartTime(), 15).toJdkDate());
        }
        LiveRespDTO respDTO = new LiveRespDTO();
        String historyUrl = aiotUtil.getHistoryUrl(bean.getSerialNo(), bean.getChannelNo(), bean.getStartTime(),
                bean.getEndTime());
        respDTO.setFlvAddress(historyUrl);
        return respDTO;
    }

    @Override
    public List<ExApiDevicePlaybackListRespDTO> getHistoryList(EventCameraPointQueryDTO bean) {
        checkExists(bean);
        BizAssert.notNull(bean.getStartTime(), "开始时间不能为空");
        // 如果没有结束时间, 开始时间 + 15min
        if (Objects.isNull(bean.getEndTime())) {
            bean.setEndTime(DateUtil.offsetMinute(bean.getStartTime(), 15).toJdkDate());
        }
        return aiotUtil.getHistoryList(bean.getSerialNo(), bean.getChannelNo(), bean.getStartTime(), bean.getEndTime());
    }

    @Override
    public Result<List<CameraTreeRegionDTO>> treeQuery(EventCameraPointQueryDTO bean) {
        List<CameraTreeRegionDTO> cameraTreeRegionDTOList = eventCameraPointRepository.getLiveData(bean.getId());
        return success(cameraTreeRegionDTOList);
    }

    @Override
    public void updateAiotDeviceStatus() {
        // 获取aiot的设备
        List<ExApiDeviceStatusResp> list = eventCameraPointRepository.getAllDeviceByType(22);
        if (CollUtil.isEmpty(list)) {
            XxlJobHelper.log("没有设备");
            return;
        }
        updateAiotDeviceStatusByDeviceList(list);
    }

    private void updateAiotDeviceStatusByDeviceList(List<ExApiDeviceStatusResp> list) {
        Map<Integer, List<ExApiDeviceStatusResp>> cameraTypeMap = list.stream()
                .collect(Collectors.groupingBy(ExApiDeviceStatusResp::getType));
        cameraTypeMap.forEach((type, typeCameras) -> {
            XxlJobHelper.log("正在更新type = " + type + " 的摄像头 " + typeCameras.size() + " 台");

            List<List<ExApiDeviceStatusResp>> camerasList = splitList(typeCameras, 100);
            XxlJobHelper.log("摄像头分成 {} 批次更新", camerasList.size());

            AtomicInteger i = new AtomicInteger();
            List<String> onlineIds = new ArrayList<>();

            camerasList.forEach(cameras -> {
                List<ExApiDeviceStatusResp> staus = getStaus(cameras);
                if (CollUtil.isEmpty(staus)) {
                    XxlJobHelper.log("查询无结果");
                    return;
                }
                XxlJobHelper.log("更新第{}批, {}台摄像头", i.incrementAndGet(), staus.size());

                Map<Integer, List<ExApiDeviceStatusResp>> map = staus.stream()
                        .collect(Collectors.groupingBy(ExApiDeviceStatusResp::getStatus));

                map.forEach((status, li) -> {
                    if (Objects.nonNull(status)) {
                        List<String> ids = li.stream().map(ExApiDeviceStatusResp::getId).collect(Collectors.toList());
                        XxlJobHelper.log("状态：{}, 摄像头ids: {}", status, ids);
                        if (status == 1) {
                            onlineIds.addAll(ids);
                        }
                    }
                });
            });
            // event_camera_point
            eventCameraPointRepository.setOfflineEventCameraPoint(onlineIds, 22);
            eventCameraPointRepository.setOnlineEventCameraPoint(onlineIds, 22);
            eventCameraPointRepository.setLockEventCameraPoint(onlineIds, 22);
            eventCameraPointRepository.setOnlineLockRealStatusEventCameraPoint(onlineIds, 22);
            eventCameraPointRepository.setOfflineLockRealStatusEventCameraPoint(onlineIds, 22);

        });
    }

    public static <T> List<List<T>> splitList(List<T> list, int chunkSize) {
        List<List<T>> partitions = new ArrayList<>();
        for (int i = 0; i < list.size(); i += chunkSize) {
            partitions.add(new ArrayList<>(list.subList(i, Math.min(i + chunkSize, list.size()))));
        }
        return partitions;
    }

    private List<ExApiDeviceStatusResp> getStaus(List<ExApiDeviceStatusResp> list) {
        return aiotUtil.getStaus(list);
    }

}