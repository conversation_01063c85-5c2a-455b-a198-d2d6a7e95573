package cn.shencom.server.service.impl;

import cn.shencom.model.AimSceneCategoryManagement;
import cn.shencom.model.dto.create.AimSceneCategoryManagementCreateDTO;
import cn.shencom.model.dto.query.AimSceneCategoryManagementQueryDTO;
import cn.shencom.model.dto.resp.AimSceneCategoryDTO;
import cn.shencom.model.dto.resp.AimSceneCategoryManagementRespDTO;
import cn.shencom.model.dto.update.AimSceneCategoryManagementUpdateDTO;
import cn.shencom.repos.AimSceneCategoryManagementRepository;
import cn.shencom.repos.AimSceneManagementRepository;
import cn.shencom.scloud.common.base.constant.RespCode;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.jpa.scpage.MyLink;
import cn.shencom.scloud.common.jpa.util.export.ScExport;
import cn.shencom.scloud.common.jpa.util.query.LinkUtil;
import cn.shencom.scloud.common.jpa.util.query.ScQueryUtil;
import cn.shencom.scloud.common.server.service.BaseImpl;
import cn.shencom.scloud.common.util.BeanUtil;
import cn.shencom.server.service.IAimSceneCategoryManagementService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;


/**
 * 场景类别管理表 的服务实现
 *
 * <AUTHOR>
 * @since 2022-07-26
 */
@Service
@Slf4j
public class AimSceneCategoryManagementServiceImpl extends BaseImpl implements IAimSceneCategoryManagementService {

    @Autowired
    private AimSceneCategoryManagementRepository aimSceneCategoryManagementRepository;

    @Autowired
    private AimSceneManagementRepository aimSceneManagementRepository;

    @Override
    public Page<AimSceneCategoryManagementRespDTO> query(AimSceneCategoryManagementQueryDTO bean) {
        Map<String, MyLink> linkMap = LinkUtil.convertLink(AimSceneCategoryManagement.class);
        Page<AimSceneCategoryManagement> res = aimSceneCategoryManagementRepository.findAll((root, query, builder) -> {
            //用于拼接条件
            List<Predicate> ps = new ArrayList<>();

            return ScQueryUtil.getPredicate(ps, bean, linkMap, root, query, builder);
        }, PageRequest.of(bean.getPage(), bean.getSize()));
        return ScQueryUtil.handle(res, linkMap, AimSceneCategoryManagementRespDTO.class, (re, dto) -> {
            dealData(dto);
        });
    }

    private void dealData(AimSceneCategoryManagementRespDTO dto) {
        //查询该类别关联场景数
        dto.setSceneNum(aimSceneManagementRepository.getSceneNumByCategoryId(dto.getId()));
    }

    @Override
    public AimSceneCategoryManagementRespDTO show(ScShowDTO bean) {
        Optional<AimSceneCategoryManagement> option = aimSceneCategoryManagementRepository.findById(bean.getId());
        if (!option.isPresent()) {
            return null;
        }
        AimSceneCategoryManagement entity = option.get();
        Map<String, MyLink> linkMap = LinkUtil.convertLink(AimSceneCategoryManagement.class);
        AimSceneCategoryManagementRespDTO respDTO = ScQueryUtil.handleOne(entity, linkMap, AimSceneCategoryManagementRespDTO.class);
        dealData(respDTO);
        return respDTO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public AimSceneCategoryManagement create(AimSceneCategoryManagementCreateDTO bean) {
        AimSceneCategoryManagement entity = new AimSceneCategoryManagement();
        BeanUtil.copyProperties(bean, entity);
        return aimSceneCategoryManagementRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public AimSceneCategoryManagement update(AimSceneCategoryManagementUpdateDTO bean) {
        AimSceneCategoryManagement entity = aimSceneCategoryManagementRepository.findById(bean.getId())
                .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));
        BeanUtil.copyProperties(bean, entity);
        return aimSceneCategoryManagementRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            ids.forEach(id -> aimSceneCategoryManagementRepository.findById(id).ifPresent(entity -> {
                entity.setIsDeleted(1);
                aimSceneCategoryManagementRepository.save(entity);
            }));
        }
    }

    @Override
    public void export(AimSceneCategoryManagementQueryDTO bean) {
        List<AimSceneCategoryManagementRespDTO> dtos = new ArrayList<>(query(bean).getContent());
        ScExport.export(bean.getCfg(), dtos, AimSceneCategoryManagementRespDTO.class);
    }

    @Override
    public List<AimSceneCategoryDTO> getAllCategory() {
        return aimSceneCategoryManagementRepository.getAllCategory();
    }
}