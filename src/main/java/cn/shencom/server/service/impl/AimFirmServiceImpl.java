package cn.shencom.server.service.impl;

import cn.shencom.model.AimFirm;
import cn.shencom.model.dto.create.AimFirmCreateDTO;
import cn.shencom.model.dto.query.AimFirmQueryDTO;
import cn.shencom.model.dto.resp.AimFirmRespDTO;
import cn.shencom.model.dto.update.AimFirmUpdateDTO;
import cn.shencom.repos.AimFirmRepository;
import cn.shencom.scloud.common.base.constant.RespCode;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.jpa.scpage.MyLink;
import cn.shencom.scloud.common.jpa.util.export.ScExport;
import cn.shencom.scloud.common.jpa.util.query.LinkUtil;
import cn.shencom.scloud.common.jpa.util.query.ScQueryUtil;
import cn.shencom.scloud.common.server.service.BaseImpl;
import cn.shencom.scloud.common.util.BeanUtil;
import cn.shencom.server.service.IAimFirmService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;


/**
 * 人工智能管理厂商表 的服务实现
 *
 * <AUTHOR>
 * @since 2022-08-02
 */
@Service
@Slf4j
public class AimFirmServiceImpl extends BaseImpl implements IAimFirmService {

    @Autowired
    private AimFirmRepository aimFirmRepository;

    @Override
    public Page<AimFirmRespDTO> query(AimFirmQueryDTO bean) {
        Map<String, MyLink> linkMap = LinkUtil.convertLink(AimFirm.class);
        Page<AimFirm> res = aimFirmRepository.findAll((root, query, builder) -> {
            //用于拼接条件
            List<Predicate> ps = new ArrayList<>();

            return ScQueryUtil.getPredicate(ps, bean, linkMap, root, query, builder);
        }, PageRequest.of(bean.getPage(), bean.getSize()));
        return ScQueryUtil.handle(res, linkMap, AimFirmRespDTO.class);
    }

    @Override
    public AimFirmRespDTO show(ScShowDTO bean) {
        Optional<AimFirm> option = aimFirmRepository.findById(bean.getId());
        if (!option.isPresent()) {
            return null;
        }
        AimFirm entity = option.get();
        Map<String, MyLink> linkMap = LinkUtil.convertLink(AimFirm.class);
        return ScQueryUtil.handleOne(entity, linkMap, AimFirmRespDTO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public AimFirm create(AimFirmCreateDTO bean) {
        AimFirm entity = new AimFirm();
        BeanUtil.copyProperties(bean, entity);
        return aimFirmRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public AimFirm update(AimFirmUpdateDTO bean) {
        AimFirm entity = aimFirmRepository.findById(bean.getId())
                .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));
        BeanUtil.copyProperties(bean, entity);
        return aimFirmRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            ids.forEach(id -> aimFirmRepository.findById(id).ifPresent(entity -> {
                entity.setIsDeleted(1);
                aimFirmRepository.save(entity);
            }));
        }
    }

    @Override
    public void export(AimFirmQueryDTO bean) {
        List<AimFirmRespDTO> dtos = new ArrayList<>(query(bean).getContent());
        ScExport.export(bean.getCfg(), dtos, AimFirmRespDTO.class);
    }

    @Override
    public List<AimFirmRespDTO> getAllFirm() {
        return aimFirmRepository.getAllFirm();
    }
}