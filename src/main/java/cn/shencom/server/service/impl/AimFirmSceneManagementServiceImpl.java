package cn.shencom.server.service.impl;

import cn.shencom.constant.AimSceneRespCode;
import cn.shencom.model.AimFirmSceneManagement;
import cn.shencom.model.AimSceneFirmRelationship;
import cn.shencom.model.AimSceneManagement;
import cn.shencom.model.dto.AimFirmRelevanceSceneDTO;
import cn.shencom.model.dto.create.AimFirmSceneManagementCreateDTO;
import cn.shencom.model.dto.query.AimFirmSceneManagementQueryDTO;
import cn.shencom.model.dto.resp.AimFirmSceneManagementRespDTO;
import cn.shencom.model.dto.resp.AimSceneStatisticsDTO;
import cn.shencom.model.dto.update.AimFirmSceneManagementUpdateDTO;
import cn.shencom.repos.AimFirmSceneManagementRepository;
import cn.shencom.repos.AimSceneFirmRelationshipRepository;
import cn.shencom.repos.AimSceneManagementRepository;
import cn.shencom.scloud.common.base.constant.RespCode;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.jpa.scpage.MyLink;
import cn.shencom.scloud.common.jpa.scpage.ScExp;
import cn.shencom.scloud.common.jpa.scpage.ScQuery;
import cn.shencom.scloud.common.jpa.util.export.ScExport;
import cn.shencom.scloud.common.jpa.util.query.LinkUtil;
import cn.shencom.scloud.common.jpa.util.query.ScQueryUtil;
import cn.shencom.scloud.common.server.service.BaseImpl;
import cn.shencom.scloud.common.util.BeanUtil;
import cn.shencom.server.service.IAimFirmSceneManagementService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 厂商场景管理 的服务实现
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Service
@Slf4j
public class AimFirmSceneManagementServiceImpl extends BaseImpl implements IAimFirmSceneManagementService {

    @Autowired
    private AimFirmSceneManagementRepository aimFirmSceneManagementRepository;

    @Autowired
    private AimSceneFirmRelationshipRepository aimSceneFirmRelationshipRepository;

    @Autowired
    private AimSceneManagementRepository aimSceneManagementRepository;

    @Override
    public Page<AimFirmSceneManagementRespDTO> query(AimFirmSceneManagementQueryDTO bean) {
        Integer isLinked = bean.getIsLinked();
        String sceneId = bean.getSceneId();
        Map<String, MyLink> linkMap = LinkUtil.convertLink(AimFirmSceneManagement.class);
        Page<AimFirmSceneManagement> res = aimFirmSceneManagementRepository.findAll((root, query, builder) -> {
            //用于拼接条件
            List<Predicate> ps = new ArrayList<>();
            //查询关联列表时
            if (isLinked != null) {
                Join<Object, Object> join = root.join("aimSceneFirmRelationships", JoinType.LEFT);
                if (isLinked.equals(1)) {
                    ps.add(builder.and(builder.equal(join.get("sceneId"), sceneId)));
                } else if (isLinked.equals(0)) {
                    join.on(builder.equal(join.get("sceneId"), sceneId));
                    ps.add(builder.isNull(join.get("firmId")));
                }
            }
            return ScQueryUtil.getPredicate(ps, bean, linkMap, root, query, builder);
        }, PageRequest.of(bean.getPage(), bean.getSize()));
        return ScQueryUtil.handle(res, linkMap, AimFirmSceneManagementRespDTO.class);
    }

    @Override
    public AimFirmSceneManagementRespDTO show(ScShowDTO bean) {
        Optional<AimFirmSceneManagement> option = aimFirmSceneManagementRepository.findById(bean.getId());
        if (!option.isPresent()) {
            return null;
        }
        AimFirmSceneManagement entity = option.get();
        Map<String, MyLink> linkMap = LinkUtil.convertLink(AimFirmSceneManagement.class);
        return ScQueryUtil.handleOne(entity, linkMap, AimFirmSceneManagementRespDTO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public AimFirmSceneManagement create(AimFirmSceneManagementCreateDTO bean) {
        AimFirmSceneManagement entity = new AimFirmSceneManagement();
        BeanUtil.copyProperties(bean, entity);
        return aimFirmSceneManagementRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public AimFirmSceneManagement update(AimFirmSceneManagementUpdateDTO bean) {
        AimFirmSceneManagement entity = aimFirmSceneManagementRepository.findById(bean.getId())
                .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));
        BeanUtil.copyProperties(bean, entity);
        return aimFirmSceneManagementRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            ids.forEach(id -> aimFirmSceneManagementRepository.findById(id).ifPresent(entity -> {
                //是否还存在关联场景
                Integer isExist = aimSceneFirmRelationshipRepository.existsByFirmId(id);
                if (isExist != null) {
                    throw new ScException(AimSceneRespCode.EXIST_SCENE);
                }
                entity.setIsDeleted(1);
                aimFirmSceneManagementRepository.save(entity);
            }));
        }
    }

    @Override
    public void export(AimFirmSceneManagementQueryDTO bean) {
        List<AimFirmSceneManagementRespDTO> dtos = new ArrayList<>(query(bean).getContent());
        ScExport.export(bean.getCfg(), dtos, AimFirmSceneManagementRespDTO.class);
    }

    @Transactional
    @Override
    public void sceneRelevance(AimFirmRelevanceSceneDTO bean) {
        String firmId = bean.getFirmId();
        AimFirmSceneManagement aimFirmSceneManagement = aimFirmSceneManagementRepository.findById(firmId)
                .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));
        for (String sceneId : bean.getSceneIds()) {
            AimSceneFirmRelationship aimSceneFirmRelationship = new AimSceneFirmRelationship();
            aimSceneFirmRelationship.setFirmId(firmId);
            aimSceneFirmRelationship.setSceneId(sceneId);
            aimSceneFirmRelationshipRepository.save(aimSceneFirmRelationship);
            //修改厂商关联ai场景数
            aimFirmSceneManagement.setSceneNum(aimFirmSceneManagement.getSceneNum() + 1);
            //修改ai场景关联厂商数
            AimSceneManagement aimSceneManagement = aimSceneManagementRepository.findById(sceneId)
                    .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));
            aimSceneManagement.setFirmNum(aimSceneManagement.getFirmNum() + 1);
        }
    }

    @Transactional
    @Override
    public void sceneDisassociate(AimFirmRelevanceSceneDTO bean) {
        String firmId = bean.getFirmId();
        AimFirmSceneManagement aimFirmSceneManagement = aimFirmSceneManagementRepository.findById(firmId)
                .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));
        for (String sceneId : bean.getSceneIds()) {
            aimSceneFirmRelationshipRepository.deleteBySceneIdAndFirmId(sceneId,firmId);
            //修改厂商关联ai场景数
            aimFirmSceneManagement.setSceneNum(aimFirmSceneManagement.getSceneNum() - 1);
            //修改ai场景关联厂商数
            AimSceneManagement aimSceneManagement = aimSceneManagementRepository.findById(sceneId)
                    .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));
            aimSceneManagement.setFirmNum(aimSceneManagement.getFirmNum() - 1);
        }

    }

    @Override
    public AimSceneStatisticsDTO sceneStatistics(AimFirmSceneManagementQueryDTO bean) {
        dealSimpleQuery(bean);
        if (StringUtils.isBlank(bean.getSimpleUsed())) {
            ScQueryUtil.getValuesRmAndSetBean(bean.getQuery(), Arrays.asList("sceneId" ,"firmId", "firmName", "code", "sceneName"), bean);
            dealBean(bean);
        } else {
            bean.setSimpleUsed("%" + bean.getSimpleUsed() + "%");
        }
        return aimFirmSceneManagementRepository.getSceneStatistics(bean);
    }

    private void dealBean(AimFirmSceneManagementQueryDTO bean) {
        if (StringUtils.isNotBlank(bean.getFirmName())) {
            bean.setFirmName("%" + bean.getFirmName() + "%");
        }
        if (StringUtils.isNotBlank(bean.getCode())) {
            bean.setCode("%" + bean.getCode() + "%");
        }
        if (StringUtils.isNotBlank(bean.getSceneName())) {
            bean.setSceneName("%" + bean.getSceneName() + "%");
        }

    }

    private AimFirmSceneManagementQueryDTO dealSimpleQuery(AimFirmSceneManagementQueryDTO bean) {
        List<ScQuery> query = bean.getQuery();
        if (CollectionUtils.isEmpty(query)) {
            return bean;
        }
        List<ScExp> scExpList = query.get(0).getExps();
        if (CollectionUtils.isEmpty(scExpList)) {
            return bean;
        }
        ScExp scExp = query.get(0).getExps().get(0);
        if ("or".equals(scExp.getLr())) {
            bean.setSimpleUsed(scExp.getValue());
        }
        return bean;
    }


    @Override
    public List<AimFirmSceneManagementRespDTO> list() {
        List<AimFirmSceneManagement> all = aimFirmSceneManagementRepository.findAll();
        return all.stream().map( dto ->  {
            AimFirmSceneManagementRespDTO respDTO = new AimFirmSceneManagementRespDTO();
            BeanUtil.copyProperties(dto,respDTO);
            return respDTO;
        }).collect(Collectors.toList());
    }
}