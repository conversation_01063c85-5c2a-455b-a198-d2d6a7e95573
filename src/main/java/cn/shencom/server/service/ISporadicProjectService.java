package cn.shencom.server.service;

import cn.shencom.model.SporadicProject;
import cn.shencom.model.dto.create.SporadicProjectCreateDTO;
import cn.shencom.model.dto.query.SporadicProjectMobileQueryDTO;
import cn.shencom.model.dto.query.SporadicProjectQueryDTO;
import cn.shencom.model.dto.resp.InviteCodeRespDTO;
import cn.shencom.model.dto.resp.SporadicProjectRespDTO;
import cn.shencom.model.dto.resp.SporadicProjectRegionStatisticsRespDTO;
import cn.shencom.model.dto.update.SporadicProjectUpdateDTO;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.utils.UserUtil.UtilsRegion;

import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 小散工程表 的服务接口
 *
 * <AUTHOR>
 * @since 2025-05-29
 */
public interface ISporadicProjectService {

    /**
     * 查询小散工程表列表
     *
     * @param bean 查询DTO
     * @return 分页列表
     */
    Page<SporadicProjectRespDTO> query(SporadicProjectQueryDTO bean);


    /**
     * 无权限校验，根据id查询
     * @param bean
     * @return
     */
    Page<SporadicProjectRespDTO> queryByIds(SporadicProjectQueryDTO bean);


    /**
     * 根据id查询小散工程表
     *
     * @param bean 查询DTO
     * @return 单个实体
     */
    SporadicProjectRespDTO show(ScShowDTO bean);

    /**
     * 新建小散工程表
     *
     * @param bean 新建DTO
     * @return 创建实体
     */
    SporadicProject create(SporadicProjectCreateDTO bean);

    /**
     * 修改小散工程表
     *
     * @param bean 修改DTO
     * @return 修改实体
     */
    SporadicProject update(SporadicProjectUpdateDTO bean);

    /**
     * 更新施工状态
     *
     * @param bean 修改DTO
     * @return 修改实体
     */
    void updateStatus(SporadicProjectUpdateDTO bean);

    /**
     * 删除小散工程表
     *
     * @param ids id集合
     */
    void delete(List<String> ids);

    /**
     * 导出小散工程表
     *
     * @param bean 导出DTO
     */
    void export(SporadicProjectQueryDTO bean);

    Result<?> importExcel(MultipartFile file);

    /**
     * 查询小散工程表列表- 移动端
     *
     * @param bean 查询DTO
     * @return 分页列表
     */
    Page<SporadicProjectRespDTO> mobileIndex(SporadicProjectMobileQueryDTO bean);

    void autoUpdateStatus();

    /**
     * 查询小散工程表列表- 移动端
     *
     * @param bean 查询DTO
     * @return
     */
    List<SporadicProjectRespDTO> mobileSelect();


    /**
     * 获取用户对应的项目id
     */
    List<SporadicProjectRespDTO> getUserProjectList(UtilsRegion region, SporadicProjectQueryDTO dto);

    /**
     * 获取用户对应的项目id
     */
    List<SporadicProjectRespDTO> getUserProjectList(SporadicProjectQueryDTO dto);

    /**
     * 获取用户对应的项目id
     */
    List<SporadicProjectRespDTO> getUserProjectList();

    /**
     * 获取工程区域分布统计
     *
     * @param bean 查询DTO
     * @return 区域统计列表
     */
    List<SporadicProjectRegionStatisticsRespDTO> getRegionStatistics(SporadicProjectQueryDTO bean);

    /**
     * 生成工程邀请码
     * 只有施工负责人和建设方（业主）有权限生成
     *
     * @param projectId 工程ID
     * @return 邀请码信息
     */
    InviteCodeRespDTO generateInviteCode(String projectId);

    /**
     * 通过邀请码获取工程信息
     *
     * @param inviteCode 邀请码
     * @param projectNumber 备案编码
     * @return 工程信息
     */
    SporadicProjectRespDTO getProjectByInviteCode(String inviteCode, String projectNumber);

    /**
     * 通过邀请码绑定工程
     * 当前用户通过邀请码绑定到工程，创建关联关系
     *
     * @param inviteCode 邀请码
     * @param projectNumber 备案编码
     * @return 绑定结果信息
     */
    SporadicProjectRespDTO bindProjectByInviteCode(String inviteCode, String projectNumber);

}
