package cn.shencom.server.service;

import cn.shencom.model.AimFirmSceneManagement;
import cn.shencom.model.dto.AimFirmRelevanceSceneDTO;
import cn.shencom.model.dto.create.AimFirmSceneManagementCreateDTO;
import cn.shencom.model.dto.query.AimFirmSceneManagementQueryDTO;
import cn.shencom.model.dto.resp.AimFirmSceneManagementRespDTO;
import cn.shencom.model.dto.resp.AimSceneStatisticsDTO;
import cn.shencom.model.dto.update.AimFirmSceneManagementUpdateDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 厂商场景管理 的服务接口
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
public interface IAimFirmSceneManagementService {

    /**
     * 查询厂商场景管理列表
     *
     * @param bean 查询DTO
     * @return 分页列表
     */
    Page<AimFirmSceneManagementRespDTO> query(AimFirmSceneManagementQueryDTO bean);

    /**
     * 根据id查询厂商场景管理
     *
     * @param bean 查询DTO
     * @return 单个实体
     */
    AimFirmSceneManagementRespDTO show(ScShowDTO bean);

    /**
     * 新建厂商场景管理
     *
     * @param bean 新建DTO
     * @return 创建实体
     */
    AimFirmSceneManagement create(AimFirmSceneManagementCreateDTO bean);

    /**
     * 修改厂商场景管理
     *
     * @param bean 修改DTO
     * @return 修改实体
     */
    AimFirmSceneManagement update(AimFirmSceneManagementUpdateDTO bean);

    /**
     * 删除厂商场景管理
     *
     * @param ids id集合
     */
    void delete(List<String> ids);

    /**
     * 导出厂商场景管理
     *
     * @param bean 导出DTO
     */
    void export(AimFirmSceneManagementQueryDTO bean);

    void sceneRelevance(AimFirmRelevanceSceneDTO bean);

    void sceneDisassociate(AimFirmRelevanceSceneDTO bean);

    AimSceneStatisticsDTO sceneStatistics(AimFirmSceneManagementQueryDTO bean);


    List<AimFirmSceneManagementRespDTO>  list();
}
