package cn.shencom.server.service;

import cn.shencom.model.SysRoleUserOrganization;
import cn.shencom.model.dto.create.SysRoleUserOrganizationCreateDTO;
import cn.shencom.model.dto.query.SysRoleUserOrganizationQueryDTO;
import cn.shencom.model.dto.resp.SysRoleUserOrganizationRespDTO;
import cn.shencom.model.dto.resp.XsgcOrganizationRespDTO;
import cn.shencom.model.dto.update.SysRoleUserOrganizationUpdateDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 系统角色，权限，组织关联表 的服务接口
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
public interface ISysRoleUserOrganizationService {

    /**
     * 查询系统角色，权限，组织关联表列表
     *
     * @param bean 查询DTO
     * @return 分页列表
     */
    Page<SysRoleUserOrganizationRespDTO> query(SysRoleUserOrganizationQueryDTO bean);

    /**
     * 根据id查询系统角色，权限，组织关联表
     *
     * @param bean 查询DTO
     * @return 单个实体
     */
    SysRoleUserOrganizationRespDTO show(ScShowDTO bean);

    /**
     * 新建系统角色，权限，组织关联表
     *
     * @param bean 新建DTO
     * @return 创建实体
     */
    SysRoleUserOrganization create(SysRoleUserOrganizationCreateDTO bean);

    /**
     * 修改系统角色，权限，组织关联表
     *
     * @param bean 修改DTO
     * @return 修改实体
     */
    SysRoleUserOrganization update(SysRoleUserOrganizationUpdateDTO bean);

    /**
     * 删除系统角色，权限，组织关联表
     *
     * @param ids id集合
     */
    void delete(List<String> ids);

    /**
     * 导出系统角色，权限，组织关联表
     *
     * @param bean 导出DTO
     */
    void export(SysRoleUserOrganizationQueryDTO bean);


    /**
     * 为组织内的全部人员授权
     */
    void addPermissionForOrganization(String organizationId);

    /**
     * 删除组织内全部人员的授权
     */
    void removePermissionForOrganization(String organizationId);


    /**
     * 为特定用户添加权限
     */
    void addPermissionForUser(SysRoleUserOrganizationUpdateDTO bean);


    /**
     * 移除用户特定权限
     * @param bean
     */
    void removePermissionForUser(SysRoleUserOrganizationUpdateDTO bean);




    /**
     * 切换组织
     */
    XsgcOrganizationRespDTO switchOrganization(SysRoleUserOrganizationQueryDTO bean);


    /**
     * 获取当前用户关联的所有角色
     */
    List<SysRoleUserOrganizationRespDTO> getAllRoles(SysRoleUserOrganizationQueryDTO bean);

}
