package cn.shencom.server.service;

import cn.shencom.model.dto.query.AiotSceneMgrQueryDTO;
import cn.shencom.model.dto.resp.AiotSceneMgrTreeRespDTO;
import cn.shencom.scloud.common.base.Result;

import java.util.List;

/**
 * 场景管理 的服务接口
 *
 * <AUTHOR>
 * @since 2024-08-03
 */
public interface IAiotSceneMgrExtService {


    Result<List<AiotSceneMgrTreeRespDTO>> getMenuTreeByAll(AiotSceneMgrQueryDTO bean);

}
