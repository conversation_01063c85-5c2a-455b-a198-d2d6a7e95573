package cn.shencom.server.service;

import cn.shencom.model.AimSceneCategoryManagement;
import cn.shencom.model.dto.create.AimSceneCategoryManagementCreateDTO;
import cn.shencom.model.dto.query.AimSceneCategoryManagementQueryDTO;
import cn.shencom.model.dto.resp.AimSceneCategoryDTO;
import cn.shencom.model.dto.resp.AimSceneCategoryManagementRespDTO;
import cn.shencom.model.dto.update.AimSceneCategoryManagementUpdateDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 场景类别管理表 的服务接口
 *
 * <AUTHOR>
 * @since 2022-07-26
 */
public interface IAimSceneCategoryManagementService {

    /**
     * 查询场景类别管理表列表
     *
     * @param bean 查询DTO
     * @return 分页列表
     */
    Page<AimSceneCategoryManagementRespDTO> query(AimSceneCategoryManagementQueryDTO bean);

    /**
     * 根据id查询场景类别管理表
     *
     * @param bean 查询DTO
     * @return 单个实体
     */
    AimSceneCategoryManagementRespDTO show(ScShowDTO bean);

    /**
     * 新建场景类别管理表
     *
     * @param bean 新建DTO
     * @return 创建实体
     */
    AimSceneCategoryManagement create(AimSceneCategoryManagementCreateDTO bean);

    /**
     * 修改场景类别管理表
     *
     * @param bean 修改DTO
     * @return 修改实体
     */
    AimSceneCategoryManagement update(AimSceneCategoryManagementUpdateDTO bean);

    /**
     * 删除场景类别管理表
     *
     * @param ids id集合
     */
    void delete(List<String> ids);

    /**
     * 导出场景类别管理表
     *
     * @param bean 导出DTO
     */
    void export(AimSceneCategoryManagementQueryDTO bean);

    List<AimSceneCategoryDTO> getAllCategory();

}
