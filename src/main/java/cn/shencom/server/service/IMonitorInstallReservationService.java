package cn.shencom.server.service;

import cn.shencom.model.MonitorInstallReservation;
import cn.shencom.model.dto.create.MonitorInstallReservationCreateDTO;
import cn.shencom.model.dto.query.MonitorInstallReservationQueryDTO;
import cn.shencom.model.dto.resp.MonitorInstallReservationRespDTO;
import cn.shencom.model.dto.update.MonitorInstallReservationUpdateDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 小散工程-监管工单流程-安装预约详情 的服务接口
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
public interface IMonitorInstallReservationService {

    /**
     * 查询小散工程-监管工单流程-安装预约详情列表
     *
     * @param bean 查询DTO
     * @return 分页列表
     */
    Page<MonitorInstallReservationRespDTO> query(MonitorInstallReservationQueryDTO bean);

    /**
     * 根据id查询小散工程-监管工单流程-安装预约详情
     *
     * @param bean 查询DTO
     * @return 单个实体
     */
    MonitorInstallReservationRespDTO show(ScShowDTO bean);

    /**
     * 新建小散工程-监管工单流程-安装预约详情
     *
     * @param bean 新建DTO
     * @return 创建实体
     */
    MonitorInstallReservation create(MonitorInstallReservationCreateDTO bean);

    /**
     * 修改小散工程-监管工单流程-安装预约详情
     *
     * @param bean 修改DTO
     * @return 修改实体
     */
    MonitorInstallReservation update(MonitorInstallReservationUpdateDTO bean);

    /**
     * 删除小散工程-监管工单流程-安装预约详情
     *
     * @param ids id集合
     */
    void delete(List<String> ids);

    /**
     * 导出小散工程-监管工单流程-安装预约详情
     *
     * @param bean 导出DTO
     */
    void export(MonitorInstallReservationQueryDTO bean);

}
