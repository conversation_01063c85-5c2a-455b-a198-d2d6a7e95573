package cn.shencom.server.service;

import cn.shencom.model.EventModelTypeConfig;
import cn.shencom.model.dto.create.EventModelTypeConfigCreateDTO;
import cn.shencom.model.dto.query.EventModelTypeConfigQueryDTO;
import cn.shencom.model.dto.resp.EventModelTypeConfigRespDTO;
import cn.shencom.model.dto.update.EventModelTypeConfigUpdateDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 设备型号配置表 的服务接口
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
public interface IEventModelTypeConfigService {

    /**
     * 查询设备型号配置表列表
     *
     * @param bean 查询DTO
     * @return 分页列表
     */
    Page<EventModelTypeConfigRespDTO> query(EventModelTypeConfigQueryDTO bean);

    /**
     * 根据id查询设备型号配置表
     *
     * @param bean 查询DTO
     * @return 单个实体
     */
    EventModelTypeConfigRespDTO show(ScShowDTO bean);

    /**
     * 新建设备型号配置表
     *
     * @param bean 新建DTO
     * @return 创建实体
     */
    EventModelTypeConfig create(EventModelTypeConfigCreateDTO bean);

    /**
     * 修改设备型号配置表
     *
     * @param bean 修改DTO
     * @return 修改实体
     */
    EventModelTypeConfig update(EventModelTypeConfigUpdateDTO bean);

    /**
     * 删除设备型号配置表
     *
     * @param ids id集合
     */
    void delete(List<String> ids);

    /**
     * 导出设备型号配置表
     *
     * @param bean 导出DTO
     */
    void export(EventModelTypeConfigQueryDTO bean);

}
