package cn.shencom.server.service;

import cn.shencom.model.EngineeringMembers;
import cn.shencom.model.dto.create.EngineeringMembersCreateDTO;
import cn.shencom.model.dto.query.EngineeringMembersQueryDTO;
import cn.shencom.model.dto.resp.EngineeringMembersRespDTO;
import cn.shencom.model.dto.resp.SporadicProjectRespDTO;
import cn.shencom.model.dto.update.EngineeringMembersUpdateDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 小散工程-工程人员 的服务接口
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
public interface IEngineeringMembersService {

    /**
     * 查询小散工程-工程人员列表
     *
     * @param bean 查询DTO
     * @return 分页列表
     */
    Page<EngineeringMembersRespDTO> query(EngineeringMembersQueryDTO bean);

    /**
     * 根据id查询小散工程-工程人员
     *
     * @param bean 查询DTO
     * @return 单个实体
     */
    EngineeringMembersRespDTO show(ScShowDTO bean);

    /**
     * 新建小散工程-工程人员
     *
     * @param bean 新建DTO
     * @return 创建实体
     */
    EngineeringMembers create(EngineeringMembersCreateDTO bean);

    /**
     * 修改小散工程-工程人员
     *
     * @param bean 修改DTO
     * @return 修改实体
     */
    EngineeringMembers update(EngineeringMembersUpdateDTO bean);

    /**
     * 删除小散工程-工程人员
     *
     * @param ids id集合
     */
    void delete(List<String> ids);

    /**
     * 导出小散工程-工程人员
     *
     * @param bean 导出DTO
     */
    void export(EngineeringMembersQueryDTO bean);

    /**
     * 创建或更新
     */
    EngineeringMembers createOrUpdate(EngineeringMembersUpdateDTO bean);




    /**
     * 查询当前工程成员绑定的项目列表
     * @param bean
     * @return
     */
    Page<SporadicProjectRespDTO> projectPage(EngineeringMembersQueryDTO bean);


    /**
     * 移除某个工程的工程人员
     */
    void removeMemberRelateByProjectId(String projectId);

}
