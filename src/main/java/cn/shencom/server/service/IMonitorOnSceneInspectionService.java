package cn.shencom.server.service;

import cn.shencom.model.MonitorOnSceneInspection;
import cn.shencom.model.dto.create.MonitorOnSceneInspectionCreateDTO;
import cn.shencom.model.dto.query.MonitorOnSceneInspectionQueryDTO;
import cn.shencom.model.dto.resp.MonitorOnSceneInspectionRespDTO;
import cn.shencom.model.dto.update.MonitorOnSceneInspectionUpdateDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 小散工程-监管工单流程-现场勘察详情 的服务接口
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
public interface IMonitorOnSceneInspectionService {

    /**
     * 查询小散工程-监管工单流程-现场勘察详情列表
     *
     * @param bean 查询DTO
     * @return 分页列表
     */
    Page<MonitorOnSceneInspectionRespDTO> query(MonitorOnSceneInspectionQueryDTO bean);

    /**
     * 根据id查询小散工程-监管工单流程-现场勘察详情
     *
     * @param bean 查询DTO
     * @return 单个实体
     */
    MonitorOnSceneInspectionRespDTO show(ScShowDTO bean);

    /**
     * 新建小散工程-监管工单流程-现场勘察详情
     *
     * @param bean 新建DTO
     * @return 创建实体
     */
    MonitorOnSceneInspection create(MonitorOnSceneInspectionCreateDTO bean);

    /**
     * 修改小散工程-监管工单流程-现场勘察详情
     *
     * @param bean 修改DTO
     * @return 修改实体
     */
    MonitorOnSceneInspection update(MonitorOnSceneInspectionUpdateDTO bean);

    /**
     * 删除小散工程-监管工单流程-现场勘察详情
     *
     * @param ids id集合
     */
    void delete(List<String> ids);

    /**
     * 导出小散工程-监管工单流程-现场勘察详情
     *
     * @param bean 导出DTO
     */
    void export(MonitorOnSceneInspectionQueryDTO bean);

}
