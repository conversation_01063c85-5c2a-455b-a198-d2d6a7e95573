package cn.shencom.constant;

/**
 * 邀请码相关常量
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
public interface InviteCodeConstant {

    /**
     * 邀请码Redis key前缀
     */
    String INVITE_CODE_REDIS_PREFIX = "sporadic:project:invite:code:";

    /**
     * 邀请码长度
     */
    int INVITE_CODE_LENGTH = 10;

    /**
     * 邀请码有效期（秒）- 1周
     */
    long INVITE_CODE_EXPIRE_SECONDS = 7 * 24 * 60 * 60L;

    /**
     * 邀请码字符集：大小写字母+数字
     */
    String INVITE_CODE_CHARSET = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
}
