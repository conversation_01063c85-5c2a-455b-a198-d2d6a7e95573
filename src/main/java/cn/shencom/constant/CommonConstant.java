package cn.shencom.constant;

import java.util.Arrays;
import java.util.List;

/**
 * 通用常量
 * <AUTHOR>
 * @date 2023/5/10 9:46
 */
public interface CommonConstant {

    /**
     * 行政区划redis缓存前缀
     */
    String REDIS_PREFIX_REGION = "com:region";

    /**
     * 深圳市id
     */
    String REGION_SHENZHEN = "2";

    //全部区id
    List<String> CITY_REGION_IDS = Arrays.asList(
            "79",
            "80",
            "82",
            "81",
            "83",
            "85",
            "84",
            "86",
            "3",
            "87"
    );

    // 区域redis key 常量
    String REGION_SIMPLE_REDIS_KEY = "com_region_simple_all";

    // 区域映射redis key 常量 hash
    String REGION_SIMPLE_MAP_KEY = "com_region_map";


    Integer PROJECT_STATUS_WAIT = 0;
    Integer PROJECT_STATUS_GOING = 1;
    Integer PROJECT_STATUS_END = 2;

    String AIOT_EVENT_QUEUE  = "aiot:event";


    /**
     * 消息提醒
     */
    String XSGC_MESSAGE_REMIND  = "xsgc:message:remind";

}
