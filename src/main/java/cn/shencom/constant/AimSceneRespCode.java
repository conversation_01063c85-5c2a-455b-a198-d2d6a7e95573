package cn.shencom.constant;

import cn.shencom.scloud.common.base.constant.SuperRespCode;

public enum AimSceneRespCode implements SuperRespCode {

    EXIST_FIRM("649001","存在关联厂商场景，请先解除关联"),
    EXIST_SCENE("649002","存在关联AI场景，请先解除关联");

    private final String code;
    private final String msg;

    @Override
    public String msg() {
        return msg;
    }

    @Override
    public String code() {
        return code;
    }

    AimSceneRespCode(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
