package cn.shencom.repos;

import cn.shencom.model.AiotSceneCategory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;


/**
 * aiot_scene_category 的Repository
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Repository
public interface AiotSceneCategoryRepository extends JpaRepository<AiotSceneCategory, String>, JpaSpecificationExecutor<AiotSceneCategory> {
    AiotSceneCategory findFirstByCateCodeAndSceneCode(String cateCode,String sceneCode);
}
