package cn.shencom.repos;

import cn.shencom.model.AimSceneFirmRelationship;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * AI场景-厂商场景关联表 的Repository
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Repository
public interface AimSceneFirmRelationshipRepository extends JpaRepository<AimSceneFirmRelationship, String>, JpaSpecificationExecutor<AimSceneFirmRelationship> {

    void deleteBySceneIdAndFirmId(String sceneId, String firmId);

    List<AimSceneFirmRelationship> findBySceneId(String sceneId);

    @Query(nativeQuery = true, value = "select scene_id from aim_scene_firm_relationship where firm_id = ?1")
    List<String> getSceneIdsByFirmId(String firmId);

    @Query(nativeQuery = true, value = "select 1 from aim_scene_firm_relationship where scene_id = ?1 limit 1")
    Integer existsBySceneId(String id);

    @Query(nativeQuery = true, value = "select 1 from aim_scene_firm_relationship where firm_id = ?1 limit 1")
    Integer existsByFirmId(String id);

    @Query(nativeQuery = true, value = "select firm_id from aim_scene_firm_relationship where scene_id = ?1")
    List<String> getFirmIdsBySceneId(String sceneId);
}
