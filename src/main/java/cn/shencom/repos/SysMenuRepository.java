package cn.shencom.repos;

import cn.shencom.model.SysMenu;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Map;
import java.util.Set;


/**
 * SysMenu的Repository接口
 *
 * <AUTHOR>
@Repository
public interface SysMenuRepository extends CrudRepository<SysMenu, String>, JpaSpecificationExecutor<SysMenu> {
    @Query(value = "SELECT cast(menu_id as char)  FROM sys_menu_permission  " +
            "            WHERE permission_id  " +
            "            IN ( SELECT permission_id FROM sys_permission_role WHERE role_id  " +
            "            IN (SELECT sru.role_id FROM sys_role_user sru " +
            "      JOIN sys_roles  sr ON sru.role_id = sr.id AND sr.flag = 0      " +
            "         WHERE sru.user_id = :userId)) ", nativeQuery = true)
    Set<String> findMenuIdsByUserId(@Param("userId") String userId);


    @Query(value =
            "SELECT cast(menu_id as char)  FROM sys_menu_permission  " +
            "            WHERE permission_id  " +
            "            IN ( SELECT permission_id FROM sys_permission_role WHERE role_id  " +
            "            IN (SELECT sru.role_id FROM sys_role_user_organization sru " +
            "      JOIN sys_roles  sr ON sru.role_id = sr.id AND sr.flag = 1      " +
            "         WHERE sru.user_id = :userId AND sru.organization_id = :organizationId))  ", nativeQuery = true)
    Set<String> findMenuIdsByUserIdAndOrganization(@Param("userId") String userId,@Param("organizationId") String organizationId);



    @Query(value = "SELECT cast(id as char) FROM sys_menu", nativeQuery = true)
    Set<String> findAllMenuIds();


    @Query(nativeQuery = true, value = "SELECT im.label AS bkName, c.link AS bkUrl " +
            "FROM fn_rmsv3_icon_menu im " +
            "LEFT JOIN fn_rmsv_route_config c ON im.link_id = c.id " +
            "WHERE im.id = ?1 ")
    Map<String, Object> findIconMenu(String id);
}
