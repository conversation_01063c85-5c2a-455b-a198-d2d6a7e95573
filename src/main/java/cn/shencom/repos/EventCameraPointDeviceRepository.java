package cn.shencom.repos;

import cn.shencom.model.EventCameraPointDevice;
import cn.shencom.model.dto.query.EventCameraPointDeviceMobileQueryDTO;
import cn.shencom.model.dto.query.EventCameraPointDeviceQueryDTO;
import cn.shencom.model.dto.resp.EventCameraPointDeviceMobileRespDTO;
import cn.shencom.model.dto.resp.EventCameraPointDeviceRespDTO;
import com.slyak.spring.jpa.TemplateQuery;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * 摄像头表 的Repository
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Repository
public interface EventCameraPointDeviceRepository extends JpaRepository<EventCameraPointDevice, String>, JpaSpecificationExecutor<EventCameraPointDevice> {

    EventCameraPointDevice findFirstBySerialNo(String serialNo);

    EventCameraPointDevice findFirstBySipUserId(String sipUserId);


    List<EventCameraPointDevice> findByIdIn(List<String> ids);


    List<EventCameraPointDevice> findByProjectId(String projectId);


    boolean existsByProjectId(String projectId);


    @Query(nativeQuery = true, value = "select count(1) " +
            "from event_camera_point_device t1  " +
            "left join event_camera_point t2 on t1.id = t2.device_id " +
            "where t1.is_deleted = 0 and t2.is_deleted = 0 and t1.sip_user_id = :serialNo and t2.channel = :channel and t1.type = 22 and t2.id != :cameraId ")
    Integer countBySipNoAndChannel(@Param("serialNo") String serialNo, @Param("channel") String channel, @Param("cameraId") String cameraId);

    @Query(nativeQuery = true, value = "select count(1) " +
            "from event_camera_point_device t1  " +
            "left join event_camera_point t2 on t1.id = t2.device_id " +
            "where t1.is_deleted = 0 and t2.is_deleted = 0 and t1.serial_no = :serialNo and t2.channel = :channel and t1.type != 22 and t2.id != :cameraId ")
    Integer countBySerialNoAndChannel(@Param("serialNo") String serialNo, @Param("channel") String channel, @Param("cameraId") String cameraId);

    /**
     * 移动端查询监控设备列表
     *
     * @param bean 查询条件
     * @param pageable 分页信息
     * @return 分页结果
     */
    @TemplateQuery
    Page<EventCameraPointDeviceMobileRespDTO> mobileIndex(EventCameraPointDeviceMobileQueryDTO bean, Pageable pageable);



    @TemplateQuery
    Page<EventCameraPointDeviceRespDTO> notRelevanceIndex(EventCameraPointDeviceQueryDTO bean, Pageable pageable);
}
