package cn.shencom.repos;

import cn.shencom.model.MonitorRecycle;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;


/**
 * 小散工程-监管工单流程-上门回收详情 的Repository
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@Repository
public interface MonitorRecycleRepository extends JpaRepository<MonitorRecycle, String>, JpaSpecificationExecutor<MonitorRecycle> {

}
