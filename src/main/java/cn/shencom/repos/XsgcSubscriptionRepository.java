package cn.shencom.repos;

import cn.shencom.model.XsgcSubscription;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;


/**
 * 小散工程-套餐 的Repository
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Repository
public interface XsgcSubscriptionRepository extends JpaRepository<XsgcSubscription, String>, JpaSpecificationExecutor<XsgcSubscription> {

    XsgcSubscription findFirstByName(String name);

}
