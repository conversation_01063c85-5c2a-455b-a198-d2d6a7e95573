package cn.shencom.repos;

import cn.shencom.model.GisPoi;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;


/**
 * 区域块/线路 的Repository
 *
 * <AUTHOR>
 * @since 2022-01-11
 */
@Repository
public interface GisPoiRepository extends CrudRepository<GisPoi, String>, JpaSpecificationExecutor<GisPoi> {

}
