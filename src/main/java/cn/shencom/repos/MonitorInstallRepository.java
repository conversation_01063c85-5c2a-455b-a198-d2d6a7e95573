package cn.shencom.repos;

import cn.shencom.model.MonitorInstall;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;


/**
 * 小散工程-监管工单流程-上门安装详情 的Repository
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@Repository
public interface MonitorInstallRepository extends JpaRepository<MonitorInstall, String>, JpaSpecificationExecutor<MonitorInstall> {

}
