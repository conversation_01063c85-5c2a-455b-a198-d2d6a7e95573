package cn.shencom.repos;

import cn.shencom.model.AiotCategory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;


/**
 * aiot场景分类 的Repository
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Repository
public interface AiotCategoryRepository extends JpaRepository<AiotCategory, String>, JpaSpecificationExecutor<AiotCategory> {
    @Query(value = "SELECT * FROM aiot_category WHERE `code` = ?1" ,nativeQuery = true)
    AiotCategory findFirstByCodeWithDeleted(String code);
}
