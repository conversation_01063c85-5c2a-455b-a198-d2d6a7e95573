package cn.shencom.repos;

import cn.shencom.model.ContractingUnitOrganizationRelate;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * 施工单位组织关联表 的Repository
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Repository
public interface ContractingUnitOrganizationRelateRepository
                extends JpaRepository<ContractingUnitOrganizationRelate, String>,
                JpaSpecificationExecutor<ContractingUnitOrganizationRelate> {

        Optional<ContractingUnitOrganizationRelate> findByContractingUnitIdAndOrganizationId(String contractingUnitId,
                        String organizationId);

        /**
         * 获取施工单位组织关联表列表,需要对 contractingUnitId 进行去重并统计数量
         *
         * @param organizationId 组织ID
         * @return 施工单位组织关联表列表
         */
        @Query(value = "SELECT DISTINCT COUNT(contracting_unit_id) FROM contracting_unit_organization_relate WHERE organization_id = :organizationId AND is_deleted = 0", nativeQuery = true)
        Integer findDistinctContractingUnitIdByOrganizationIdCount(@Param("organizationId") String organizationId);
}
