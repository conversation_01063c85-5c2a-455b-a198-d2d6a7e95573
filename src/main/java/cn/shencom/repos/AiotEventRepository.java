package cn.shencom.repos;

import cn.shencom.model.AiotEvent;
import cn.shencom.model.dto.query.AiotEventMobileQueryDTO;
import cn.shencom.model.dto.query.AiotEventQueryDTO;
import cn.shencom.model.dto.resp.AiotEventMobileRespDTO;
import cn.shencom.model.dto.resp.AiotEventRespDTO;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import com.slyak.spring.jpa.TemplateQuery;

/**
 * 监控事件 的Repository
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Repository
public interface AiotEventRepository extends JpaRepository<AiotEvent, String>, JpaSpecificationExecutor<AiotEvent> {
    AiotEvent findFirstByEventNo(String eventNo);

    @TemplateQuery
    List<AiotEventRespDTO> queryEvent(AiotEventQueryDTO bean);

    @TemplateQuery
    Page<AiotEventMobileRespDTO> mobileIndex(AiotEventMobileQueryDTO bean, Pageable pageable);
}
