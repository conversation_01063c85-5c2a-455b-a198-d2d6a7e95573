package cn.shencom.repos;

import cn.shencom.model.XsgcMessageRemind;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;


/**
 * 小散工程-消息提醒表 的Repository
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Repository
public interface XsgcMessageRemindRepository extends JpaRepository<XsgcMessageRemind, String>, JpaSpecificationExecutor<XsgcMessageRemind> {


    /**
     * 修改已办标记
     */
    @Modifying
    @Transactional
    @Query(nativeQuery = true,value = "UPDATE xsgc_message_remind SET resolve_flag = ?1,updated_at = NOW()  WHERE relate_id =?2 AND user_id = ?3 ")
    int updateResolveFlagByUserId(Integer resolveFlag,String relateId,String userId);


    /**
     * 修改已办标记
     */
    @Modifying
    @Transactional
    @Query(nativeQuery = true,value = "UPDATE xsgc_message_remind SET resolve_flag = ?1,updated_at = NOW() WHERE relate_id =?2 AND user_id != ?3 ")
    int updateResolveFlagAndUserIdNotEQ(Integer resolveFlag,String relateId,String userId);

    @Modifying
    @Transactional
    @Query(nativeQuery = true,value = "UPDATE xsgc_message_remind SET status = ?2 ,updated_at = ?3 WHERE id =?1 ")
    int updateStatus(String id, Integer status , Date updatedAt);


}
