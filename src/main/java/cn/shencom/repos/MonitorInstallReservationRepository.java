package cn.shencom.repos;

import cn.shencom.model.MonitorInstallReservation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;


/**
 * 小散工程-监管工单流程-安装预约详情 的Repository
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@Repository
public interface MonitorInstallReservationRepository extends JpaRepository<MonitorInstallReservation, String>, JpaSpecificationExecutor<MonitorInstallReservation> {

}
