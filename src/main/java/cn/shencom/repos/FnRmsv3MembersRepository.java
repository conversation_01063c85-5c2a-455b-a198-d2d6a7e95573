package cn.shencom.repos;

import cn.shencom.model.FnRmsv3Members;
import com.slyak.spring.jpa.TemplateQuery;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * 小散工程-组织团队成员表 的Repository
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Repository
public interface FnRmsv3MembersRepository extends JpaRepository<FnRmsv3Members, String>, JpaSpecificationExecutor<FnRmsv3Members> {

    boolean existsByOrganizationId(String organizationId);

    boolean existsByMobileAndOrganizationId(String mobile,String organizationId);

    FnRmsv3Members findByUserIdAndOrganizationId(String userId,String organizationId);


    @TemplateQuery
    int countByOrganizationIdAndTypeIn(@Param("organizationId") String organizationId, @Param("types") List<String> types);

}
