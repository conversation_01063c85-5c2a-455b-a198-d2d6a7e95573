package cn.shencom.repos;

import cn.shencom.model.AimSceneManagement;
import cn.shencom.model.dto.query.AimSceneManagementQueryDTO;
import cn.shencom.model.dto.resp.AimSceneCategoryDTO;
import cn.shencom.model.dto.resp.AimSceneStatisticsDTO;
import com.slyak.spring.jpa.TemplateQuery;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * AI场景管理 的Repository
 *
 * <AUTHOR>
 * @since 2022-07-26
 */
@Repository
public interface AimSceneManagementRepository extends JpaRepository<AimSceneManagement, String>, JpaSpecificationExecutor<AimSceneManagement> {

    @Query(nativeQuery = true, value = "select max(code) from aim_scene_management where is_deleted = 0")
    String getMaxCode();

    @TemplateQuery
    AimSceneStatisticsDTO getSceneStatisticsData(AimSceneManagementQueryDTO bean);

    @Query(nativeQuery = true, value = "select count(distinct id) from aim_scene_management " +
            "where is_deleted = 0 and scene_category_id = ?1")
    long getSceneNumByCategoryId(String id);

    @TemplateQuery
    List<String> findCodesById(@Param("id") String id);

    @TemplateQuery
    List<AimSceneCategoryDTO> getAiSceneAll();
}
