package cn.shencom.repos;

import cn.shencom.model.EventOrderSource;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;


/**
 * 事件来源表 的Repository
 *
 * <AUTHOR>
 * @since 2023-11-15
 */
@Repository
public interface EventOrderSourceRepository extends JpaRepository<EventOrderSource, String>, JpaSpecificationExecutor<EventOrderSource> {

}
