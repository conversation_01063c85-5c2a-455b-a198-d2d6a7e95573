package cn.shencom.repos;

import cn.shencom.model.EventModelTypeConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;


/**
 * 设备型号配置表 的Repository
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Repository
public interface EventModelTypeConfigRepository extends JpaRepository<EventModelTypeConfig, String>, JpaSpecificationExecutor<EventModelTypeConfig> {

}
