package cn.shencom.repos;

import cn.shencom.model.MonitorFlow;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * 小散工程-监管工单流程 的Repository
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@Repository
public interface MonitorFlowRepository extends JpaRepository<MonitorFlow, String>, JpaSpecificationExecutor<MonitorFlow> {

    MonitorFlow findByOrderIdAndFlow(String orderId,Integer flow);


    List<MonitorFlow>  findByFlow(Integer flow);


    @Query(nativeQuery = true,value = "SELECT DISTINCT order_id FROM monitor_flow WHERE created_user = ?1 AND flow =  ?2 AND state = 1")
    List<String> findOrderIdsByCreatedUserAndFlow(String userId,Integer flow);

}
