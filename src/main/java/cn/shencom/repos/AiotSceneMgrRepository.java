package cn.shencom.repos;

import cn.shencom.model.AiotSceneMgr;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * 场景类别管理 的Repository
 *
 * <AUTHOR>
 * @since 2024-08-03
 */
@Repository
public interface AiotSceneMgrRepository extends CrudRepository<AiotSceneMgr, String>, JpaRepository<AiotSceneMgr, String>, JpaSpecificationExecutor<AiotSceneMgr> {
    AiotSceneMgr findBySceneCode(String sceneCode);
    Long countBypId(String pId);


    List<AiotSceneMgr> findBypIdNot(String pid);
}
