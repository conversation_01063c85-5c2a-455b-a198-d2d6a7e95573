package cn.shencom.repos;

import cn.shencom.model.EventCameraPoint;
import cn.shencom.model.dto.CameraTreeRegionDTO;
import cn.shencom.model.dto.ExApiDeviceStatusResp;
import cn.shencom.model.dto.query.EventCameraPointQueryDTO;
import cn.shencom.model.dto.resp.EventCameraPointRespDTO;
import com.slyak.spring.jpa.TemplateQuery;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;


/**
 * 摄像头信息表 的Repository
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Repository
public interface EventCameraPointRepository extends JpaRepository<EventCameraPoint, String>, JpaSpecificationExecutor<EventCameraPoint> {

    EventCameraPoint findFirstBySerialNo(String serialNo);

    List<EventCameraPoint> findByDeviceIdIn(List<String> deviceIds);
    List<EventCameraPoint> findByDeviceId(String deviceId);

    EventCameraPoint findFirstByMonitorNo(String monitorNo);

    EventCameraPoint findFirstBySipUserIdAndChannel(String sip,String channel);

    // 删除deviceId 没用到的 channelId
    @Modifying
    @Transactional
    @Query(nativeQuery = true, value = "update event_camera_point set is_deleted = 1 where is_deleted = 0 and device_id = :deviceId and id not in :channelIdList ")
    void deleteNoContainCamera(@Param("deviceId") String deviceId, @Param("channelIdList") List<String> channelIdList);

    @Modifying
    @Transactional
    @Query(nativeQuery = true, value = "update event_camera_point set is_deleted = 1 where is_deleted = 0 and device_id = :deviceId ")
    void deleteByDeviceId(@Param("deviceId") String deviceId);

    @TemplateQuery
    List<Map<String, String>> getCameraType(EventCameraPointQueryDTO bean);

    @TemplateQuery
    List<CameraTreeRegionDTO> getBuildTreePointData(EventCameraPointQueryDTO bean);

    @Query(nativeQuery = true, value = "SELECT ecp.monitor_no monitorNo FROM event_camera_point ecp  " +
            "WHERE ecp.monitor_no IS NOT NULL GROUP BY ecp.monitor_no ")
    List<Map<String, Object>> getAllSmartCameraLiveData();

    EventCameraPoint findFirstBySerialNoAndChannel(String serialNo, String channel);

    @TemplateQuery
    Slice<EventCameraPointRespDTO> findSliceSerialNoChannel(EventCameraPointQueryDTO queryDTO, Pageable pageable);

    @Modifying
    @Query(nativeQuery = true, value = "update event_camera_point  " +
            "set status = :status " +
            "where is_deleted = 0 and serial_no = :serialNo and channel = :channel and is_lock = 0 ")
    Integer updateCameraStatus(@Param("serialNo") String serialNo, @Param("channel") String channel, @Param("status") Integer status);

    @TemplateQuery
    List<CameraTreeRegionDTO> getLiveData(@Param("monitorNo") String monitorNo);



    @Modifying
    @Transactional
    @Query(nativeQuery = true, value = "update event_camera_point " +
            "set `status` = 1, real_status = 1, online_at = now() " +
            "where id in :cameraIds and type = :type and is_deleted = 0 and is_lock = 0 ")
    void setOnlineEventCameraPoint(@Param("cameraIds") List<String> cameraIds, @Param("type") Integer type);

    @Modifying
    @Transactional
    @Query(nativeQuery = true, value = "update event_camera_point " +
            "set `status` = 1, online_at = now() " +
            "where id in :cameraIds and type = :type and is_deleted = 0 and is_lock = 1 and status = 1 ")
    void setLockEventCameraPoint(@Param("cameraIds") List<String> cameraIds, @Param("type") Integer type);

    @Modifying
    @Transactional
    @Query(nativeQuery = true, value = "update event_camera_point " +
            "set real_status = 1 " +
            "where id in :cameraIds and type = :type and is_deleted = 0 and is_lock = 1 ")
    void setOnlineLockRealStatusEventCameraPoint(@Param("cameraIds") List<String> cameraIds, @Param("type") Integer type);


    @Modifying
    @Transactional
    @Query(nativeQuery = true, value = "update event_camera_point " +
            "set real_status = 0 " +
            "where id not in :cameraIds and type = :type and is_deleted = 0 and is_lock = 1 ")
    void setOfflineLockRealStatusEventCameraPoint(@Param("cameraIds") List<String> cameraIds, @Param("type") Integer type);

    @Modifying
    @Transactional
    @Query(nativeQuery = true, value = "update event_camera_point " +
            "set `status` = 0, real_status = 0 " +
            "where id not in :cameraIds and type = :type and is_deleted = 0 and is_lock = 0 ")
    void setOfflineEventCameraPoint(@Param("cameraIds") List<String> cameraIds, @Param("type") Integer type);

    @TemplateQuery
    List<ExApiDeviceStatusResp> getAllDeviceByType(@Param("type") Integer type);


}
