package cn.shencom.repos;

import cn.shencom.model.AimSceneCategoryManagement;
import cn.shencom.model.dto.resp.AimSceneCategoryDTO;
import com.slyak.spring.jpa.GenericJpaRepository;
import com.slyak.spring.jpa.TemplateQuery;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * 场景类别管理表 的Repository
 *
 * <AUTHOR>
 * @since 2022-07-26
 */
@Repository
public interface AimSceneCategoryManagementRepository extends GenericJpaRepository<AimSceneCategoryManagement, String>, JpaSpecificationExecutor<AimSceneCategoryManagement> {

    @TemplateQuery
    List<AimSceneCategoryDTO> getAllCategory();

    @Query(nativeQuery = true, value = "select name from aim_scene_category_management where is_deleted = 0 and id = ?1")
    String getNameById(String sceneCategoryId);
}
