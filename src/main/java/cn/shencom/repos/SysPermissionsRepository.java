package cn.shencom.repos;

import cn.shencom.model.SysPermissions;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;


/**
 * SysPermissions的Repository接口
 *
 * <AUTHOR>
@Repository
public interface SysPermissionsRepository extends CrudRepository<SysPermissions, String>, JpaSpecificationExecutor<SysPermissions> {


    @Query(value = "SELECT name FROM sys_permissions WHERE id IN " +
            "            (SELECT permission_id FROM sys_permission_role WHERE role_id  " +
            "            IN (SELECT sru.role_id FROM sys_role_user sru JOIN sys_roles sr ON sru.role_id = sr.id  " +
            "  WHERE sru.user_id = :userId  and  sr.flag = 0  )    " +
            ") and deleted_at is null ", nativeQuery = true)
    Set<String> findNameByUserId(@Param("userId")  String userId );


    @Query(value =
            "SELECT name FROM sys_permissions WHERE id IN " +
            "            (SELECT permission_id FROM sys_permission_role WHERE role_id  " +
            "            IN ( " +
            "SELECT sru.role_id FROM sys_role_user_organization sru JOIN sys_roles sr ON sru.role_id = sr.id  " +
            "WHERE sru.user_id = :userId  AND sru.organization_id = :organizationId AND sr.flag = 1 " +
            " )  " +
            " ) and deleted_at is null ", nativeQuery = true)
    Set<String> findNameByUserIdAndOrganizationId(@Param("userId")  String userId , @Param("organizationId")   String organizationId);

}
