package cn.shencom.repos;

import cn.shencom.model.AimFirm;
import cn.shencom.model.dto.resp.AimFirmRespDTO;
import com.slyak.spring.jpa.GenericJpaRepository;
import com.slyak.spring.jpa.TemplateQuery;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * 人工智能管理厂商表 的Repository
 *
 * <AUTHOR>
 * @since 2022-08-02
 */
@Repository
public interface AimFirmRepository extends GenericJpaRepository<AimFirm, String>, JpaSpecificationExecutor<AimFirm> {

    @TemplateQuery
    List<AimFirmRespDTO> getAllFirm();

}
