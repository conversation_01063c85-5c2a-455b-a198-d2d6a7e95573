package cn.shencom.repos;

import cn.shencom.model.EventOrderType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;


/**
 * 监控事件类型 的Repository
 *
 * <AUTHOR>
 * @since 2023-11-22
 */
@Repository
public interface EventOrderTypeRepository extends JpaRepository<EventOrderType, String>, JpaSpecificationExecutor<EventOrderType> {

}
