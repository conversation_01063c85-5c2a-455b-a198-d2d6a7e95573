package cn.shencom.repos;

import cn.shencom.model.FnRmsv3MembersTypeRelate;
import cn.shencom.model.dto.MemberRelateStatics;
import cn.shencom.model.dto.SimpleMemberDTO;
import cn.shencom.model.dto.create.FnRmsv3MembersTypeRelateCreateDTO;
import cn.shencom.model.dto.resp.XsgcOrganizationRespDTO;
import com.slyak.spring.jpa.TemplateQuery;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 小散工程-组织团队成员关系 的Repository
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Repository
public interface FnRmsv3MembersTypeRelateRepository
        extends JpaRepository<FnRmsv3MembersTypeRelate, String>, JpaSpecificationExecutor<FnRmsv3MembersTypeRelate> {

    @TemplateQuery
    List<SimpleMemberDTO> findAllRelateByOrganizationId(@Param("organizationId") String organizationId);

    @TemplateQuery
    List<SimpleMemberDTO> findAllUserIdByProjectId(@Param("projectId") String projectId);

    List<FnRmsv3MembersTypeRelate> findByMemberId(String memberId);

    boolean existsByMemberIdAndTypeId(String memberId, String typeId);

    FnRmsv3MembersTypeRelate findFirstByMemberIdAndTypeIdIn(String memberId, List<String> typeIdList);

    @TemplateQuery
    List<XsgcOrganizationRespDTO> getAllOrganizationByUserId(@Param("userId") String userId);

    @TemplateQuery
    List<XsgcOrganizationRespDTO> getAllOrganizationByUserId(@Param("userId") String userId, @Param("organizationId") String organizationId);

    @TemplateQuery
    MemberRelateStatics getStatics(@Param("organizationId") String organizationId);

}
