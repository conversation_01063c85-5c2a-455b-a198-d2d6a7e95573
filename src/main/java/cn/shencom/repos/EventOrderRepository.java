package cn.shencom.repos;

import cn.shencom.model.EventOrder;
import cn.shencom.model.dto.resp.AimSceneManagementRespDTO;
import com.slyak.spring.jpa.GenericJpaRepository;
import com.slyak.spring.jpa.TemplateQuery;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 事件工单表 的Repository
 *
 * <AUTHOR>
 * @since 2021-06-29
 */
@Repository
public interface EventOrderRepository extends GenericJpaRepository<EventOrder, String>, JpaSpecificationExecutor<EventOrder> {

    @Query(nativeQuery = true, value = "select type from open_ai_camera_device where is_deleted = 0 and serial_no = ?1 limit 1")
    Integer findOpenAiCameraTypeByCameraNo(String cameraNo);

    EventOrder findFirstByEventCode(String eventCode);

    @TemplateQuery
    AimSceneManagementRespDTO getEventData(@Param("typeCodeList") List<String> typeCodeList);

}
