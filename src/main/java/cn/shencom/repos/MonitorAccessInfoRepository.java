package cn.shencom.repos;

import cn.shencom.model.MonitorAccessInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * 小散工程-监管工单流程-监控接入详情 的Repository
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@Repository
public interface MonitorAccessInfoRepository extends JpaRepository<MonitorAccessInfo, String>, JpaSpecificationExecutor<MonitorAccessInfo> {


    @Transactional
    @Modifying
    int deleteByOrderId(String orderId);


    List<MonitorAccessInfo> findByOrderId(String orderId);

}
