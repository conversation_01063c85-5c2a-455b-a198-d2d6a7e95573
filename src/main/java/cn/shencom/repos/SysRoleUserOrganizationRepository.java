package cn.shencom.repos;

import cn.shencom.model.SysRoleUserOrganization;
import cn.shencom.model.dto.query.SysRoleUserOrganizationQueryDTO;
import com.slyak.spring.jpa.TemplateQuery;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * 系统角色，权限，组织关联表 的Repository
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Repository
public interface SysRoleUserOrganizationRepository extends JpaRepository<SysRoleUserOrganization, String>, JpaSpecificationExecutor<SysRoleUserOrganization> {

    @Transactional
    @Modifying
    int deleteByOrOrganizationId(String organizationId);




    boolean existsByOrganizationIdAndUserIdAndRoleId(String organizationId,String userId,String roleId);

    List<SysRoleUserOrganization> findByOrganizationIdAndUserIdAndRoleId(String organizationId,String userId, String roleId);


}
