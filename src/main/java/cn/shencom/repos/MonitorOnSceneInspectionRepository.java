package cn.shencom.repos;

import cn.shencom.model.MonitorOnSceneInspection;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;


/**
 * 小散工程-监管工单流程-现场勘察详情 的Repository
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@Repository
public interface MonitorOnSceneInspectionRepository extends JpaRepository<MonitorOnSceneInspection, String>, JpaSpecificationExecutor<MonitorOnSceneInspection> {

}
