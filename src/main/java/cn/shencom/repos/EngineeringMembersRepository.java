package cn.shencom.repos;

import cn.shencom.model.EngineeringMembers;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * 小散工程-工程人员 的Repository
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Repository
public interface EngineeringMembersRepository extends JpaRepository<EngineeringMembers, String>, JpaSpecificationExecutor<EngineeringMembers> {

    EngineeringMembers findByTypeAndMobile(Integer type,String mobile);


    EngineeringMembers findFirstByMobile(String mobile);

    EngineeringMembers findFirstByUserId(String userId);

    List<EngineeringMembers> findByIdInAndType(List<String> ids, Integer type);
}
