package cn.shencom.repos;

import cn.shencom.model.XsgcCustomerInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;


/**
 * 小散工程-客户信息表 的Repository
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Repository
public interface XsgcCustomerInfoRepository extends JpaRepository<XsgcCustomerInfo, String>, JpaSpecificationExecutor<XsgcCustomerInfo> {


    boolean existsByOptionIdAndStatus(String optionId,Integer status);

    boolean existsByNumber(String number);


    /**
     * 查询未关闭且已过期的服务
     * @param status
     * @param endDate
     * @return
     */
    List<XsgcCustomerInfo> findByStatusAndEndDateLessThan(Integer status, Date endDate);

    XsgcCustomerInfo findFirstByOrganizationId(String organizationId);

}
