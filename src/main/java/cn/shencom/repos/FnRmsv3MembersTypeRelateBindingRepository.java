package cn.shencom.repos;

import cn.shencom.model.FnRmsv3MembersTypeRelateBinding;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * 小散工程-组织团队成员区域关联关系 的Repository
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Repository
public interface FnRmsv3MembersTypeRelateBindingRepository extends JpaRepository<FnRmsv3MembersTypeRelateBinding, String>, JpaSpecificationExecutor<FnRmsv3MembersTypeRelateBinding> {

    List<FnRmsv3MembersTypeRelateBinding> findByRelateId(String relateId);


    @Modifying
    @Transactional
    int deleteByRelateId(String relateId);

}
