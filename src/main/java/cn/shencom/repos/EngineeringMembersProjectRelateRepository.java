package cn.shencom.repos;

import cn.shencom.model.EngineeringMembersProjectRelate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;


/**
 * 小散工程-工程人员关联项目表 的Repository
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Repository
public interface EngineeringMembersProjectRelateRepository extends JpaRepository<EngineeringMembersProjectRelate, String>, JpaSpecificationExecutor<EngineeringMembersProjectRelate> {

    List<EngineeringMembersProjectRelate> findByRelateId(String relateId);

    EngineeringMembersProjectRelate findByMemberIdAndProjectId(String memberId, String projectId);

    List<EngineeringMembersProjectRelate> findByMemberId(String memberId);

    List<EngineeringMembersProjectRelate> findByProjectId(String projectId);


    /**
     * 查询工程关联的施工负责人 和 建设方（业主）
     * @param projectId
     * @return
     */
    @Query(nativeQuery = true,value = " SELECT DISTINCT m.user_id FROM engineering_members_project_relate  r " +
            "JOIN engineering_members m ON r.member_id = m.id  " +
            "WHERE r.is_deleted = 0 AND m.is_deleted = 0 AND r.project_id = ?1 ")
    Set<String> findUserIdsByProjectId(String projectId);

    List<EngineeringMembersProjectRelate> findByProjectIdIn(List<String> projectIds);
}
