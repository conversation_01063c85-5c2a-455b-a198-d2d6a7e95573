package cn.shencom.repos;


import cn.shencom.model.SysRoles;
import cn.shencom.model.dto.query.SysRoleUserOrganizationQueryDTO;
import cn.shencom.model.dto.resp.SysRoleUserOrganizationRespDTO;
import com.slyak.spring.jpa.TemplateQuery;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * SysRoles的Repository接口
 *
 * <AUTHOR>
@Repository
public interface SysRolesRepository extends CrudRepository<SysRoles, String>, JpaSpecificationExecutor<SysRoles> {


    SysRoles findFirstByName(String name);




    @TemplateQuery
    List<SysRoleUserOrganizationRespDTO>  getAllRoles(SysRoleUserOrganizationQueryDTO bean);


    @TemplateQuery
    List<SysRoleUserOrganizationRespDTO>  getAllOrganizationRoles(SysRoleUserOrganizationQueryDTO bean);

}
