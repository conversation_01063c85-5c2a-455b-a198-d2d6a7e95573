package cn.shencom.repos;

import cn.shencom.model.SysRoleUser;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * SysRoleUser的Repository接口
 *
 * <AUTHOR>
@Repository
public interface SysRoleUserRepository extends CrudRepository<SysRoleUser, String>,
        JpaSpecificationExecutor<SysRoleUser> {

    List<SysRoleUser> findByUserIdAndRoleId(@Param("userId") String userId,
                                            @Param("roleId") String roleId);

    List<SysRoleUser> findByUserId(@Param("userId") String userId);

    List<SysRoleUser> findByRoleIdIn(List<String> ids);

    @Modifying
    void deleteByRoleIdIn(List<String> roleIds);

    @Modifying
    void deleteByUserIdIn(List<String> userIds);

    @Modifying
    void deleteByUserIdAndRoleId(String userId,String roleId);
}
