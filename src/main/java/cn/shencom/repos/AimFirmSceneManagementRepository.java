package cn.shencom.repos;

import cn.shencom.model.AimFirmSceneManagement;
import cn.shencom.model.dto.query.AimFirmSceneManagementQueryDTO;
import cn.shencom.model.dto.resp.AimSceneStatisticsDTO;
import com.slyak.spring.jpa.GenericJpaRepository;
import com.slyak.spring.jpa.TemplateQuery;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;


/**
 * 厂商场景管理 的Repository
 *
 * <AUTHOR>
 * @since 2022-07-27
 */
@Repository
public interface AimFirmSceneManagementRepository extends GenericJpaRepository<AimFirmSceneManagement, String>, JpaSpecificationExecutor<AimFirmSceneManagement> {


    AimFirmSceneManagement findFirstByCode(String code);

    List<AimFirmSceneManagement> findAllByCodeIn(List<String> codeList);

    @TemplateQuery
    Map<String, String> getNameAndCodeById(@Param("firmId") String firmId);

    @TemplateQuery
    AimSceneStatisticsDTO getSceneStatistics(AimFirmSceneManagementQueryDTO bean);

    @Query(nativeQuery = true, value = "select asm.name " +
            "from aim_scene_management asm  " +
            "left join aim_scene_firm_relationship asfr on asm .id = asfr.scene_id  " +
            "left join aim_firm_scene_management afsm on asfr.firm_id = afsm.id and afsm.is_deleted = 0 " +
            "where afsm.code = ?1 ")
    List<String> getSceneName(String typeCode);

    @Query(nativeQuery = true, value = "select asm.id, asm.name, afsm.code as `code` " +
            "from aim_scene_management asm  " +
            "left join aim_scene_firm_relationship asfr on asm .id = asfr.scene_id  " +
            "left join aim_firm_scene_management afsm on asfr.firm_id = afsm.id and afsm.is_deleted = 0 " +
            "where afsm.code in ?1 ")
    List<Map<String, Object>> getSceneNameByCodeIn(List<String> typeCodeList);

    @Query(nativeQuery = true , value = "SELECT  DISTINCT asm.scene_name " +
            "            FROM aim_scene_firm_relationship  f " +
            "            JOIN aim_scene_management am ON f.scene_id = am.id " +
            "            JOIN aim_firm_scene_management  m  ON f.firm_id = m.id  " +
            "            JOIN aiot_scene_mgr asm ON asm.id = am.scene_category_id  " +
            "           WHERE m.is_deleted = 0 AND m.code = ?1 " )
    List<String> getSceneCategoryName(String typeCode);





    @Query(nativeQuery = true,  value = "select distinct asm.scene_category_id " +
            "from aim_scene_management asm  " +
            "left join aim_scene_firm_relationship asfr on asm .id = asfr.scene_id  " +
            "left join aim_firm_scene_management afsm on asfr.firm_id = afsm.id and afsm.is_deleted = 0 " +
            "where afsm.code = ?1 ")
    List<String> getSceneCategoryId(String typeCode);




    @Query(nativeQuery = true , value = "SELECT  m.code " +
            "FROM aim_scene_firm_relationship  f " +
            "JOIN aim_scene_management am ON f.scene_id = am.id " +
            "JOIN aim_firm_scene_management  m  ON f.firm_id = m .id  " +
            "WHERE m.is_deleted = 0 AND am.id = ?1 " )
    List<String> getCodeBySceneId(String sceneId);




    @Query(nativeQuery = true , value = "SELECT  m.code " +
            "FROM aim_scene_firm_relationship  f " +
            "JOIN aim_scene_management am ON f.scene_id = am.id " +
            "JOIN aim_firm_scene_management  m  ON f.firm_id = m.id " +
            "JOIN aiot_scene_mgr asm ON asm.id = am.scene_category_id  " +
            "WHERE m.is_deleted = 0 AND asm.id = ?1 " )
    List<String> getCodeBySceneCategoryId(String sceneCategoryId);


}
