package cn.shencom.repos;

import cn.shencom.model.ConstructionUnitOrganizationRelate;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;


/**
 * 施工单位组织关联表 的Repository
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Repository
public interface ConstructionUnitOrganizationRelateRepository extends JpaRepository<ConstructionUnitOrganizationRelate, String>, JpaSpecificationExecutor<ConstructionUnitOrganizationRelate> {

  List<ConstructionUnitOrganizationRelate> findByConstructionUnitIdInAndOrganizationId(List<String> constructionUnitIds, String organizationId);
}
