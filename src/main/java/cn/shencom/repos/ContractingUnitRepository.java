package cn.shencom.repos;

import cn.shencom.model.ContractingUnit;

import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

/**
 * 施工单位表 的Repository
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Repository
public interface ContractingUnitRepository
    extends JpaRepository<ContractingUnit, String>, JpaSpecificationExecutor<ContractingUnit> {

    Optional<ContractingUnit> findByName(String name);
}
