package cn.shencom.repos;

import cn.shencom.model.SporadicProjectCategory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * 小散工程分类 的Repository
 *
 * <AUTHOR>
 * @since 2025-05-29
 */
@Repository
public interface SporadicProjectCategoryRepository extends JpaRepository<SporadicProjectCategory, String>, JpaSpecificationExecutor<SporadicProjectCategory> {
    SporadicProjectCategory findFirstByName(String name);

    SporadicProjectCategory findFirstBypIdAndName(String pid,String name );

    List<SporadicProjectCategory> findAllBypId(String pid);

    List<SporadicProjectCategory> findAllBypIdIn(List<String> pids);
}
