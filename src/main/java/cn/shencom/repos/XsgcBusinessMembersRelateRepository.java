package cn.shencom.repos;

import cn.shencom.model.XsgcBusinessMembersRelate;
import cn.shencom.model.dto.query.XsgcBusinessMembersRelateQueryDTO;
import cn.shencom.model.dto.resp.XsgcBusinessMembersRelateRespDTO;
import com.slyak.spring.jpa.TemplateQuery;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * 小散工程-业务人员客户关联表 的Repository
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Repository
public interface XsgcBusinessMembersRelateRepository extends JpaRepository<XsgcBusinessMembersRelate, String>, JpaSpecificationExecutor<XsgcBusinessMembersRelate> {

    boolean existsByOrganizationId(String organizationId);

    boolean existsByMemberId(String memberId);

    List<XsgcBusinessMembersRelate> findByOrganizationId(String organizationId);


    @TemplateQuery
    List<XsgcBusinessMembersRelate> findByOrganizationIdAndType(@Param("organizationId") String organizationId,@Param("type") Integer type);


    @TemplateQuery
    Page<XsgcBusinessMembersRelateRespDTO> relevanceIndex(XsgcBusinessMembersRelateQueryDTO bean , Pageable pageable);


    @TemplateQuery
    Page<XsgcBusinessMembersRelateRespDTO> notRelevanceIndex(XsgcBusinessMembersRelateQueryDTO bean , Pageable pageable);


    @Modifying
    @Transactional
    int deleteByOrganizationId(String organizationId);


}
