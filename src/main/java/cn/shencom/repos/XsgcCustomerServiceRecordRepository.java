package cn.shencom.repos;

import cn.shencom.model.XsgcCustomerServiceRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * 小散工程-客户服务开通记录 的Repository
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Repository
public interface XsgcCustomerServiceRecordRepository extends JpaRepository<XsgcCustomerServiceRecord, String>, JpaSpecificationExecutor<XsgcCustomerServiceRecord> {

    XsgcCustomerServiceRecord findFirstByCustomerIdAndActiveOrderByStartDateDesc(String customerId,Integer active);

    XsgcCustomerServiceRecord findFirstByCustomerIdAndActiveOrderByStartDateAsc(String customerId,Integer active);

    XsgcCustomerServiceRecord findFirstByCustomerIdOrderByIdDesc(String customerId);


    boolean existsByCustomerId(String customerId);

    boolean existsByOptionId(String optionId);

    @Query(nativeQuery = true , value = " UPDATE xsgc_customer_service_record SET is_deleted = 1 ,deleted_at = NOW() WHERE customer_id = ?1  ")
    @Modifying
    @Transactional
    int deleteByCustomerId(String customerId);


    /**
     * 查询当日临时续约的客户
     * @return 客户id
     */
    @Query(nativeQuery = true , value =
                    "SELECT DISTINCT customer_id FROM xsgc_customer_service_record " +
                            "WHERE CURRENT_DATE() BETWEEN start_date AND end_date AND renewal_mark = 2 AND is_deleted = 0 ")
    List<String> selectTempRenewal();
}
