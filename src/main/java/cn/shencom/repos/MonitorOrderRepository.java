package cn.shencom.repos;

import cn.shencom.model.MonitorOrder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * 小散工程-监管工单 的Repository
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@Repository
public interface MonitorOrderRepository extends JpaRepository<MonitorOrder, String>, JpaSpecificationExecutor<MonitorOrder> {

    MonitorOrder findByProjectId(String projectId);

    List<MonitorOrder> findByFlow(Integer flow);

    @Modifying
    @Transactional
    int deleteByProjectId(String projectId);
}
