package cn.shencom.repos;

import cn.shencom.model.AiotCategory;
import cn.shencom.model.AiotScene;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;


/**
 * aiot场景分类 的Repository
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Repository
public interface AiotSceneRepository extends JpaRepository<AiotScene, String>, JpaSpecificationExecutor<AiotScene> {
    @Query(value = "SELECT * FROM aiot_scene WHERE `code` = ?1" ,nativeQuery = true)
    AiotScene findFirstByCodeWithDeleted(String code);
}
