package cn.shencom.rabbitmq.sender;

import cn.shencom.constant.CommonConstant;
import cn.shencom.model.dto.XsgcMessageRemindDTO;
import com.alibaba.fastjson.JSON;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class XsgcMessageRemindSender {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    public void send(XsgcMessageRemindDTO bean) {
        rabbitTemplate.convertAndSend(CommonConstant.XSGC_MESSAGE_REMIND, CommonConstant.XSGC_MESSAGE_REMIND, JSON.toJSONString(bean));
    }


}
