package cn.shencom.rabbitmq.listener;


import cn.shencom.constant.CommonConstant;
import cn.shencom.model.dto.AiotEventPushDTO;
import cn.shencom.model.dto.create.AiotEventCreateDTO;
import cn.shencom.scloud.common.util.ScidContext;
import cn.shencom.server.service.IAiotEventService;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class AiotEventListener {
    @Value("${scid}")
    private String scid;

    @Resource
    private IAiotEventService aiotEventService;

    @Resource
    private RabbitTemplate rabbitTemplate;

    @RabbitListener(bindings =
        @QueueBinding(
                value = @Queue(name = CommonConstant.AIOT_EVENT_QUEUE,durable = "true"),
                exchange = @Exchange(value =  CommonConstant.AIOT_EVENT_QUEUE, type = "topic"),
                key = {"aiot:event","aiot:event:failed"}
        )
    )
    public void onMessage(String message) {
        log.info("queue:{},message:{}",  CommonConstant.AIOT_EVENT_QUEUE, message);
        try{
            AiotEventPushDTO bean = JSONObject.parseObject(message, AiotEventPushDTO.class);
            ScidContext.setScid(scid);
            aiotEventService.updateOrCreate(bean);
        }catch (Exception e){
            e.printStackTrace();
            log.error("queue:{},error:{}", CommonConstant.AIOT_EVENT_QUEUE, e.getMessage());
            rabbitTemplate.convertAndSend(CommonConstant.AIOT_EVENT_QUEUE, "aiot:event:failed", message);
        }

    }
}
