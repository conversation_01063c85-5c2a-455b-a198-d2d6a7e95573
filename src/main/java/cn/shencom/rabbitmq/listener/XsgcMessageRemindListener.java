package cn.shencom.rabbitmq.listener;

import cn.shencom.constant.CommonConstant;
import cn.shencom.model.dto.XsgcMessageRemindDTO;
import cn.shencom.scloud.common.util.ScidContext;
import cn.shencom.server.service.IXsgcMessageRemindService;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class XsgcMessageRemindListener {

    @Value("${scid}")
    private String scid;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private IXsgcMessageRemindService xsgcMessageRemindService;


    @RabbitListener(bindings =
    @QueueBinding(
            value = @Queue(name = CommonConstant.XSGC_MESSAGE_REMIND,durable = "true"),
            exchange = @Exchange(value =  CommonConstant.XSGC_MESSAGE_REMIND, type = "topic"),
            key = {"xsgc:message:remind","xsgc:message:remind:failed"}
    )
    )
    public void onMessage(String message) {
        log.info("queue:{},message:{}",  CommonConstant.XSGC_MESSAGE_REMIND, message);
        try{
            XsgcMessageRemindDTO bean = JSONObject.parseObject(message, XsgcMessageRemindDTO.class);
            ScidContext.setScid(scid);
            xsgcMessageRemindService.createRemind(bean);
        }catch (Exception e){
            e.printStackTrace();
            log.error("queue:{},error:{}", CommonConstant.XSGC_MESSAGE_REMIND, e.getMessage());
            rabbitTemplate.convertAndSend(CommonConstant.XSGC_MESSAGE_REMIND, "xsgc:message:remind:failed", message);
        }

    }

}
