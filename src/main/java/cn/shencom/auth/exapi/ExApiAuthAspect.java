package cn.shencom.auth.exapi;


import cn.shencom.common.util.DBContext;
import cn.shencom.config.AiotConfig;
import cn.shencom.scloud.common.base.constant.RespCode;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.scloud.common.util.CommonUtil;
import cn.shencom.scloud.common.util.ScidContext;
import cn.shencom.utils.AuthSignUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 处理带有@ExApi的方法
 *
 * <AUTHOR>
 */
@Aspect
@Order(-1)
@Component
@Slf4j
public class ExApiAuthAspect {

    private static String APPID = "appid";

    private static String SECRET = "secret";

    private static String TIMESTAMP = "timestamp";

    private static String SIGN = "signature";

    private static String NONCE = "nonce";

    @Value("${scid}")
    private String scid;

    @Resource
    private AiotConfig aiotConfig;

    @Before("@annotation(exApiAuth)")
    public void before(JoinPoint joinPoint, ExApiAuth exApiAuth) {
        ScidContext.setScid(scid);
        DBContext.setDbType(scid);
        Map<String, String> map = getHeaderParamMap(APPID, TIMESTAMP, SIGN, NONCE);
        checkSign(map, aiotConfig.getSecret());
    }

    /**
     * 获取请求头
     *
     * @param fields
     * @return
     */
    private Map<String, String> getHeaderParamMap(String... fields) {
        HttpServletRequest request = CommonUtil.getRequest();
        Map<String, String> map = new HashMap<>();
        for (String field : fields) {
            String param = request.getHeader(field);
            if (StringUtils.isBlank(param)) {
                throw new ScException(RespCode.PARAMETER_ILLEGAL, "缺少必要参数: " + field);
            }
            map.put(field, param);
        }
        long timestamp = Long.parseLong(map.get(TIMESTAMP));
        if (((System.currentTimeMillis() - timestamp) > 24 * 60 * 60 * 1000)) {
            //超过24小时
            throw new ScException("登录链接过期");
        }
        return map;
    }

    private void checkSign(Map<String, String> map, String secret) {
        String sign = AuthSignUtil.sign(map.get(APPID), map.get(NONCE), map.get(TIMESTAMP), secret);
        if (!map.get(SIGN).equals(sign)) {
            throw new ScException("错误签名");
        }
    }
}