package cn.shencom;

import com.slyak.spring.jpa.FreemarkerSqlTemplates;
import com.slyak.spring.jpa.GenericJpaRepositoryFactoryBean;
import com.slyak.spring.jpa.GenericJpaRepositoryImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@Slf4j
@SpringBootApplication(scanBasePackages = {"cn.shencom"})
@EnableJpaRepositories(value = {"cn.shencom"},
        repositoryBaseClass = GenericJpaRepositoryImpl.class,
        repositoryFactoryBeanClass = GenericJpaRepositoryFactoryBean.class)
@EnableTransactionManagement
@EnableFeignClients
@EnableJpaAuditing
@EnableScheduling
@EnableAsync
@EnableAspectJAutoProxy(exposeProxy = true)
public class SporadicProjectApplication {


    public static void main(String[] args) {
        SpringApplication.run(SporadicProjectApplication.class, args);
    }

    @Bean
    protected FreemarkerSqlTemplates freemarkerSqlTemplates() {
        FreemarkerSqlTemplates sqlTemplates = new FreemarkerSqlTemplates();
        sqlTemplates.setSuffix(".sftl");
        sqlTemplates.setTemplateBasePackage("");
        return sqlTemplates;
    }
}
