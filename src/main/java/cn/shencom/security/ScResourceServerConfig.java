package cn.shencom.security;

import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableResourceServer;
import org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfigurerAdapter;

/**
 * <AUTHOR>
 * @date 2018/8/20
 */
@Configuration
@EnableResourceServer
public class ScResourceServerConfig extends ResourceServerConfigurerAdapter {

    @Override
    public void configure(HttpSecurity http) throws Exception {
        http
                .csrf().disable()
                .cors().and()
                .authorizeRequests()
                .antMatchers(
                        "/com/region/tree",
                        "/aiot/event/push"
                )
                .permitAll()
                .antMatchers("/**")
                .authenticated();
    }

}
