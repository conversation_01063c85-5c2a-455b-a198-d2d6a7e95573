package cn.shencom.feign;

import cn.shencom.scloud.common.base.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;


@FeignClient(name = "service-guanclassify-recycle")
public interface IServiceGuanclassifyRecycle {
    @PostMapping(value = {"/common/basic/data/region/redis/update"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    Result updateRegionRedis(@RequestHeader(value = "scid") String scid);
}
