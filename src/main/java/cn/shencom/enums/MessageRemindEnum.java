package cn.shencom.enums;

public enum MessageRemindEnum {

    //消息类型，1-预约安装提醒，2-预约回收提醒，3-现场勘察提醒，4-上门安装提醒，5-上门回收提醒，6-已接入监管的提醒，7-结束监管的提醒，8-违规告警的提醒
    TYPE1(1, "预约安装提醒"),
    TYPE2(2, "预约回收提醒"),
    TYPE3(3, "现场勘察提醒"),
    TYPE4(4, "上门安装提醒"),
    TYPE5(5, "上门回收提醒"),
    TYPE6(6, "已接入监管的提醒"),
    TYPE7(7, "结束监管的提醒"),
    TYPE8(8, "违规告警的提醒"),
    ;

    private final Integer type;
    private final String name;

    MessageRemindEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }


    public Integer getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public static String getNameByType(Integer type) {
        for (MonitorFlowEnum flow : MonitorFlowEnum.values()) {
            if (flow.getType().equals(type)) {
                return flow.getName();
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return this.name;
    }

}
