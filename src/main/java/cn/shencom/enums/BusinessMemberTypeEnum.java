package cn.shencom.enums;

import lombok.Getter;

import java.util.List;

/**
 * 业务人员类型枚举
 *
 * <AUTHOR>
 */
@Getter
public enum BusinessMemberTypeEnum {

    /**
     * 1.技术人员
     * 2.商务人员
     * 3.销售人员
     * 4.安装人员
     */
    TECHNICIAN(1, "技术人员"),
    BUSINESS_PEOPLE(2, "商务人员"),
    SALESPERSON(3, "销售人员"),
    INSTALLER(4, "安装人员"),
    ;

    private Integer code;
    private String msg;

    BusinessMemberTypeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static String getMsgByCode(Integer code) {
        BusinessMemberTypeEnum[] values = BusinessMemberTypeEnum.values();
        for (BusinessMemberTypeEnum value : values) {
            if (value.getCode().equals(code)) {
                return value.getMsg();
            }
        }
        return null;
    }


    public static BusinessMemberTypeEnum[] getAllTypeEnum(){
        return values();
    }
}
