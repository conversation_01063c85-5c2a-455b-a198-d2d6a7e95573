package cn.shencom.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * 团队人员类型枚举
 *
 * <AUTHOR>
 */
@Getter
public enum OrganizationMemberTypeEnum {

    /**
     * 1.管理员
     * 2.市人员
     * 3.区人员
     * 4.街道人员
     * 5.社区人员
     * 6.巡查人员
     */
    ADMINISTRATORS(1, "管理员"),
    CITY_PERSONNEL(2, "市人员"),
    DISTRICT_PERSONNEL(3, "区人员"),
    STREET_PERSONNEL(4, "街道人员"),
    COMMUNITY_PERSONNEL(5, "社区人员"),
    INSPECTOR(6, "巡查人员"),
    ;

    private Integer code;
    private String msg;

    /**
     * 除了管理员以外的类型
     */
    public static final List<String> ORDINARY_MEMBER_TYPE = Arrays.asList("2", "3", "4", "5", "6");

    OrganizationMemberTypeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static String getMsgByCode(Integer code) {
        OrganizationMemberTypeEnum[] values = OrganizationMemberTypeEnum.values();
        for (OrganizationMemberTypeEnum value : values) {
            if (value.getCode().equals(code)) {
                return value.getMsg();
            }
        }
        return null;
    }
}
