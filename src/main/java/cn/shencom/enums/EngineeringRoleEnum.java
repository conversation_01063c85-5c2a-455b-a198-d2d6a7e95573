package cn.shencom.enums;

import java.util.ArrayList;
import java.util.List;


/**
 *  工程管理人员角色枚举
 */
public enum EngineeringRoleEnum {
    CONSTRUCTION_PARTY(1,"Construction:party", "建设方（业主）"),
    CONSTRUCTION_UNIT_LEADER(2,"Construction:unitleader", "施工单位负责人"),
    ;


    //角色标识
    private final String role;
    private final String name;
    //工程成员类型
    private final Integer memberType;

    EngineeringRoleEnum(Integer memberType, String role, String name) {
        this.memberType =memberType;
        this.role = role;
        this.name = name;
    }


    public static String getRoleByType(Integer type){
        for (EngineeringRoleEnum businessRoleEnum : values()) {
            if (type.equals(businessRoleEnum.getMemberType())){
                return businessRoleEnum.getRole();
            }
        }
        return null;
    }


    public static String getNameByType(Integer type){
        for (EngineeringRoleEnum businessRoleEnum : values()) {
            if (type.equals(businessRoleEnum.getMemberType())){
                return businessRoleEnum.getName();
            }
        }
        return null;
    }


    public String getRole() {
        return role;
    }

    public Integer getMemberType() {
        return memberType;
    }


    public String getName() {
        return name;
    }

    public static String getNameByRole(String role) {
        for (EngineeringRoleEnum status : EngineeringRoleEnum.values()) {
            if (status.getRole().equals(role)) {
                return status.getName();
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return this.name;
    }


    public static List<String> getAllRoleName(){
        List<String> result = new ArrayList<>();
        for (EngineeringRoleEnum businessRoleEnum : EngineeringRoleEnum.values()) {
            result.add(businessRoleEnum.getRole());
        }
        return result;
    }
}
