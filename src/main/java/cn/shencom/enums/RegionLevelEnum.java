package cn.shencom.enums;
/**
 * 区域级别枚举
 */
public enum RegionLevelEnum {
    /**
     * 市级
     */
    CITY("city", 0),
    /**
     * 区级
     */
    DISTRICT("district", 1),
    /**
     * 街道级
     */
    STREET("street", 2),
    /**
     * 社区级
     */
    COMMUNITY("community", 3),
    ;

    // 区域级别
    private final Integer level;
    private final String code;

    RegionLevelEnum(String code, Integer level) {
        this.code = code;
        this.level = level;
    }

    public Integer level() {
        return level;
    }

    public String code() {
        return code;
    }

}
