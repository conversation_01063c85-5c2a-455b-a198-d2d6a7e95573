package cn.shencom.enums;

/**
 * 监管流程
 */
public enum MonitorFlowEnum {
    //当前流程, 0-工程创建，1-安装预约，2-现场勘察，3-上门安装，4-接入监管， 5-施工完成，6-回收预约，7-上门回收， 9-监管结束
    TYPE0(0, "工程创建"),
    TYPE1(1, "安装预约"),
    TYPE2(2, "现场勘察"),
    TYPE3(3, "上门安装"),
    TYPE4(4, "接入监管"),
    TYPE5(5, "施工完成"),
    TYPE6(6, "回收预约"),
    TYPE7(7, "上门回收"),
    TYPE9(9, "监管结束"),
    ;

    private final Integer type;
    private final String name;

    MonitorFlowEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }


    public Integer getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public static String getNameByType(Integer type) {
        for (MonitorFlowEnum flow : MonitorFlowEnum.values()) {
            if (flow.getType().equals(type)) {
                return flow.getName();
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return this.name;
    }
}
