package cn.shencom.enums;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public enum BusinessRoleEnum {
    ORDINARY_INSPECTOR("6","Ordinary:patrol", "普通巡查员"),
    ORDINARY_CITY_ADMINISTRATOR("2","Ordinary:citylevel", "普通市级管理员"),
    ORDINARY_DISTRICT_ADMINISTRATOR("3","Ordinary:districtlevel", "普通区级管理员"),
    ORDINARY_STREET_ADMINISTRATOR("4","Ordinary:street", "普通街道管理员"),
    ORDINARY_COMMUNITY_ADMINISTRATOR("5","Ordinary:community", "普通社区管理员"),

    SENIOR_INSPECTOR("6","Senior:patrol", "高级巡查员"),
    SENIOR_CITY_ADMINISTRATOR("2","Senior:citylevel", "高级市级管理员"),
    SENIOR_DISTRICT_ADMINISTRATOR("3","Senior:districtlevel", "高级区级管理员"),
    SENIOR_STREET_ADMINISTRATOR("4","Senior:street", "高级街道管理员"),
    SENIOR_COMMUNITY_ADMINISTRATOR("5","Senior:community", "高级社区管理员"),

    ADMINISTRATOR("1" , "Organization:administrator", "管理员")
    ;



    public static final List<BusinessRoleEnum> ORDINARY_ROLES = Arrays.asList( ADMINISTRATOR, ORDINARY_INSPECTOR ,ORDINARY_CITY_ADMINISTRATOR,
            ORDINARY_DISTRICT_ADMINISTRATOR,ORDINARY_STREET_ADMINISTRATOR,ORDINARY_COMMUNITY_ADMINISTRATOR);

    public static final List<BusinessRoleEnum> SENIOR_ROLES = Arrays.asList(ADMINISTRATOR,SENIOR_INSPECTOR ,SENIOR_CITY_ADMINISTRATOR,
            SENIOR_DISTRICT_ADMINISTRATOR,SENIOR_STREET_ADMINISTRATOR,SENIOR_COMMUNITY_ADMINISTRATOR );




    //角色标识
    private final String role;
    private final String name;
    //团队成员类型
    private final String memberType;

    BusinessRoleEnum(String memberType, String role, String name) {
        this.memberType =memberType;
        this.role = role;
        this.name = name;
    }


    public static String getRoleByType(String type, boolean isSenior){
        List<BusinessRoleEnum> businessRoleEnumList =  isSenior? SENIOR_ROLES : ORDINARY_ROLES;
        for (BusinessRoleEnum businessRoleEnum : businessRoleEnumList) {
            if (type.equals(businessRoleEnum.getMemberType())){
                return businessRoleEnum.getRole();
            }
        }
        return null;
    }


    public static List<String> getRoleByType(String type){

        List<String> roles =new ArrayList<>();
        for (BusinessRoleEnum businessRoleEnum : values()) {
            if (type.equals(businessRoleEnum.getMemberType())){
                roles.add( businessRoleEnum.getRole());
            }
        }
        return roles;
    }



    public String getRole() {
        return role;
    }

    public String getMemberType() {
        return memberType;
    }


    public String getName() {
        return name;
    }

    public static String getNameByRole(String role) {
        for (BusinessRoleEnum status : BusinessRoleEnum.values()) {
            if (status.getRole().equals(role)) {
                return status.getName();
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return this.name;
    }


    public static List<String> getAllRoleName(){
        List<String> result = new ArrayList<>();
        for (BusinessRoleEnum businessRoleEnum : BusinessRoleEnum.values()) {
            result.add(businessRoleEnum.getRole());
        }
        return result;
    }
}
