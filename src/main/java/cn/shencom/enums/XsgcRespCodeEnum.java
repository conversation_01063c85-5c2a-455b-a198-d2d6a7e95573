package cn.shencom.enums;

import lombok.Getter;

/**
 * 响应编码
 *
 * <AUTHOR>
 */
@Getter
public enum XsgcRespCodeEnum {


    CAN_NOT_CLOSE_SUBSCRIPTION("6001", "该套餐当前使用中，无法关闭"),
    SUBSCRIPTION_NAME_REPEAT("6002", "套餐名称重复，无法创建/更新"),
    ;

    private String errorCode;
    private String msg;

    XsgcRespCodeEnum(String errorCode, String msg) {
        this.errorCode = errorCode;
        this.msg = msg;
    }

    public static String getMsgByCode(String code) {
        XsgcRespCodeEnum[] values = XsgcRespCodeEnum.values();
        for (XsgcRespCodeEnum value : values) {
            if (value.getErrorCode().equals(code)) {
                return value.getMsg();
            }
        }
        return null;
    }
}
