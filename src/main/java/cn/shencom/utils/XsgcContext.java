package cn.shencom.utils;

/**
 * 深传的租户编号上下文
 */
public class XsgcContext {

    private static final InheritableThreadLocal<String> contextHolder = new InheritableThreadLocal<>();

    public static void setOrganizationId(String organizationId) {
        contextHolder.set(organizationId);
    }

    public static String getOrganizationId() {
        return contextHolder.get();
    }

    public static void clearOrganizationId() {
        contextHolder.remove();
    }
}
