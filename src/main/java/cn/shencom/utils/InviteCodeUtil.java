package cn.shencom.utils;

import cn.shencom.constant.InviteCodeConstant;
import org.springframework.stereotype.Component;

import java.security.SecureRandom;

/**
 * 邀请码工具类
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Component
public class InviteCodeUtil {

    private static final SecureRandom RANDOM = new SecureRandom();

    /**
     * 生成随机邀请码
     * 大小写字母+数字混合，10位长度
     * 
     * @return 邀请码
     */
    public static String generateInviteCode() {
        StringBuilder sb = new StringBuilder(InviteCodeConstant.INVITE_CODE_LENGTH);
        String charset = InviteCodeConstant.INVITE_CODE_CHARSET;
        
        for (int i = 0; i < InviteCodeConstant.INVITE_CODE_LENGTH; i++) {
            int index = RANDOM.nextInt(charset.length());
            sb.append(charset.charAt(index));
        }
        
        return sb.toString();
    }

    /**
     * 生成Redis存储的key
     * 
     * @param inviteCode 邀请码
     * @return Redis key
     */
    public static String getRedisKey(String inviteCode) {
        return InviteCodeConstant.INVITE_CODE_REDIS_PREFIX + inviteCode;
    }
}
