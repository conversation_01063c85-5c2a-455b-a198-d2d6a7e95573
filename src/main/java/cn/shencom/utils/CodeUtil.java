package cn.shencom.utils;

import cn.shencom.constant.AimSceneManagementConstant;
import cn.shencom.scloud.common.util.DateUtil;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Component
public class CodeUtil {
    public static String getCodeBySc(String old) {
        String year = DateUtil.getYear(new Date());
        String[] split = old.split(AimSceneManagementConstant.SCAI_PREFIX + year);
        if (split.length == 1) {
            //代表是新的一年
            old = AimSceneManagementConstant.SCAI_PREFIX + year + "000";
        }
        String[] newSplit = old.split(AimSceneManagementConstant.SCAI_PREFIX + year);
        return getCode(old, newSplit[1]);
    }

    public static String getCode(String old, String num) {
        int n = num.length();
        int nums = Integer.parseInt(num) + 1;
        String newNum = String.valueOf(nums);
        n = Math.min(n, newNum.length());
        return old.subSequence(0, old.length() - n) + newNum;
    }

}
