package cn.shencom.utils;

import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.scloud.common.core.config.UploadUtilV2Config;
import cn.shencom.scloud.common.core.utils.UploadUtilV2;
import cn.shencom.scloud.common.util.BeanUtil;
import cn.shencom.scloud.common.util.export.ExportWordUtil2007;
import cn.shencom.scloud.common.util.export.ScExcelExportUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 通用上传导入excel
 *
 * <AUTHOR>
 * @date 2023/9/28 16:39
 */
@Slf4j
@Component
public class ImportExcelUtil {


    private static UploadUtilV2 uploadUtilV2;



    /**
     * 不检验参数
     */
    public static <T, C, A> Result<String> importExcel(MultipartFile file, Class<T> excelCls, Class<C> crateCls, Function<C, A> saveFunc) {
        return importExcel(file, 0, 1, Arrays.stream(excelCls.getDeclaredFields()).map(Field::getName).collect(Collectors.toList()), excelCls, crateCls, d -> false, saveFunc);
    }

    /**
     * 通用导入
     *
     * @param file:           文件
     * @param excelCls:       接收excel文件的dto
     * @param crateCls:       用于创建实体类的crateDto
     * @param checkParamFunc: 检查参数的断言
     * @param saveFunc:       保存的方法
     * @return cn.shencom.scloud.common.base.Result<java.lang.String>
     * <AUTHOR>
     * @date 2023/9/28 16:48
     */
    public static <T, C, A> Result<String> importExcel(MultipartFile file, Class<T> excelCls, Class<C> crateCls, Predicate<T> checkParamFunc, Function<C, A> saveFunc) {
        return importExcel(file, 0, 1, Arrays.stream(excelCls.getDeclaredFields()).map(Field::getName).collect(Collectors.toList()), excelCls, crateCls, checkParamFunc, saveFunc);
    }

    /**
     * @param file:           文件
     * @param exportCols:     导出的时候需要导出的字段
     * @param excelCls:       接收excel文件的dto
     * @param crateCls:       用于创建实体类的crateDto
     * @param checkParamFunc: 检查参数的断言
     * @param saveFunc:       保存的方法
     * @return cn.shencom.scloud.common.base.Result<java.lang.String>
     * <AUTHOR>
     * @date 2023/9/28 17:47
     */
    public static <T, C, A> Result<String> importExcel(MultipartFile file, List<String> exportCols,
                                                       Class<T> excelCls, Class<C> crateCls,
                                                       Predicate<T> checkParamFunc,
                                                       Function<C, A> saveFunc) {
        return importExcel(file, 0, 1, exportCols, excelCls, crateCls, checkParamFunc, saveFunc);
    }

    /**
     * @param file:                  文件
     * @param titleRows:             表格标题行数,默认0
     * @param headerRows:            表头行数,默认1
     * @param exportCols:            导出的时候需要导出的字段
     * @param excelCls:              接收excel文件的dto
     * @param crateCls:              用于创建实体类的crateDto
     * @param checkParamFunc:检查参数的断言
     * @param saveFunc:保存的方法
     * @return cn.shencom.scloud.common.base.Result<java.lang.String>
     * <AUTHOR>
     * @date 2023/9/28 17:10
     */
    public static <T, C, A> Result<String> importExcel(MultipartFile file,
                                                       Integer titleRows,
                                                       Integer headerRows, List<String> exportCols,
                                                       Class<T> excelCls, Class<C> crateCls,
                                                       Predicate<T> checkParamFunc,
                                                       Function<C, A> saveFunc) {
        try {
            List<T> failList = new ArrayList<>();
            List<A> crateList = new ArrayList<>();
            List<T> list = ExportWordUtil2007.importExcel(file, titleRows, headerRows, excelCls);
            if (CollUtil.isEmpty(list)) {
                return new Result<>("0000", "0条数据导入成功");
            }
            for (T dto : list) {
                if (checkParamFunc.test(dto)) {
                    failList.add(dto);
                    continue;
                }
                C instance = ReflectUtil.newInstance(crateCls);
                BeanUtil.copyProperties(dto, instance);
                try {
                    crateList.add(saveFunc.apply(instance));
                } catch (Exception e) {
                    log.warn("新增失败：msg:{},case:{}", e.getMessage(), e.getCause());
                }
            }
            int fail = failList.size();
            int saveNum = crateList.size();
            if (fail > 0) {
                String title = "导入失败记录";
                Workbook workbook = ScExcelExportUtil.exportExcel(new ExportParams(title, title), excelCls, failList, exportCols);
                try {
                    String url = uploadWorkbook(workbook, title);
                    return new Result<>("4004", saveNum > 0 ? saveNum + "条数据导入成功," + fail + "条数据导入失败" : fail + "条数据导入失败", url);
                } catch (IOException e) {
                    log.error(e.getMessage());
                    throw new ScException(e.getMessage());
                }
            }
            return new Result<>("0000", saveNum + "条数据导入成功");
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(ex.getMessage());
            throw new ScException("导入失败:msg:{}", ex.getMessage());
        }
    }

    private static String uploadWorkbook(Workbook workbook, String title) throws IOException {
        ByteArrayOutputStream stream = uploadUtilV2.transform(workbook);
        Map<String, String> upload = uploadUtilV2.uploadWorkbook(stream, title, "xls");
        return upload.get(UploadUtilV2Config.URL);
    }

}
