package cn.shencom.utils;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import cn.shencom.config.AiotConfig;
import cn.shencom.model.dto.ExApiDeviceStatusResp;
import cn.shencom.model.dto.resp.EventCameraPointRespDTO;
import cn.shencom.model.dto.resp.ExApiCameraStatusRespDTO;
import cn.shencom.model.dto.resp.ExApiDeviceLiveUrlRespDTO;
import cn.shencom.model.dto.resp.ExApiDevicePlaybackListRespDTO;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.scloud.common.base.snowflake.SnowflakeGenerator;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Table;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2025/5/18 15:45
 */
@Slf4j
@Component
public class AiotUtil {

    // 定义目标时间的格式
    SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");

    @Resource
    private AiotConfig aiotConfig;


    public String getLiveUrl(String serialNo, String channelNo) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("deviceCode", serialNo);
        jsonObject.put("peripheryCode", channelNo);
        String body = HttpUtil.createPost(aiotConfig.getHost() + "/service-iot/exapi/camera/play").body(JSONObject.toJSONString(jsonObject)).addHeaders(headers(aiotConfig.getAppid(), aiotConfig.getSecret())).execute().body();
        Result<ExApiDeviceLiveUrlRespDTO> result = JSONObject.parseObject(body, new TypeReference<Result<ExApiDeviceLiveUrlRespDTO>>() {
        });
        if (!result.getErrcode().equals("0000")) {
            throw new ScException("直播链接获取失败: " + result.getErrmsg());
        }
        return result.getData().getFlvs();
    }

    public String getHistoryUrl(String serialNo, String channelNo, Date startTime, Date endTime) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("deviceCode", serialNo);
        jsonObject.put("peripheryCode", channelNo);
        jsonObject.put("startAt", formatter.format(startTime));
        jsonObject.put("endAt", formatter.format(endTime));
        String body = HttpUtil.createPost(aiotConfig.getHost() + "/service-iot/exapi/camera/playback").body(JSONObject.toJSONString(jsonObject)).addHeaders(headers(aiotConfig.getAppid(), aiotConfig.getSecret())).execute().body();
        Result<ExApiDeviceLiveUrlRespDTO> result = JSONObject.parseObject(body, new TypeReference<Result<ExApiDeviceLiveUrlRespDTO>>() {
        });
        if (!result.getErrcode().equals("0000")) {
            throw new ScException("回放链接获取失败: " + result.getErrmsg());
        }
        return result.getData().getFlvs();
    }

    public List<ExApiDevicePlaybackListRespDTO> getHistoryList(String serialNo, String channelNo, Date startTime, Date endTime) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("deviceCode", serialNo);
        jsonObject.put("peripheryCode", channelNo);
        jsonObject.put("startAt", formatter.format(startTime));
        jsonObject.put("endAt", formatter.format(endTime));
        String body = HttpUtil.createPost(aiotConfig.getHost() + "/service-iot/exapi/camera/playback/list").body(JSONObject.toJSONString(jsonObject)).addHeaders(headers(aiotConfig.getAppid(), aiotConfig.getSecret())).execute().body();
        Result<List<ExApiDevicePlaybackListRespDTO>> result = JSONObject.parseObject(body, new TypeReference<Result<List<ExApiDevicePlaybackListRespDTO>>>() {
        });
        if (!result.getErrcode().equals("0000")) {
            throw new ScException("回放列表获取失败: " + result.getErrmsg());
        }
        return result.getData();
    }

    public List<ExApiCameraStatusRespDTO> getCameraStatus(Table<String, String, String> sipUserIdToSerialNoMap, List<EventCameraPointRespDTO> dtos) {
        if (CollectionUtils.isEmpty(dtos)) {
            return new ArrayList<>();
        }
        JSONArray jsonArray = new JSONArray();
        dtos.forEach(re -> {
            String deviceCode = ObjectUtils.compare(re.getType(), 22) == 0 ? re.getSipUserId() : re.getSerialNo();
            String peripheryCode = re.getChannel();

            if (StringUtils.isBlank(deviceCode) || StringUtils.isBlank(peripheryCode)) {
                return;
            }

            JSONObject jsonObject = new JSONObject();
            if (ObjectUtils.compare(re.getType(), 22) == 0) {
                sipUserIdToSerialNoMap.put(re.getSipUserId(), re.getChannel(), re.getSerialNo());
                jsonObject.put("deviceCode", re.getSipUserId());
            } else {
                jsonObject.put("deviceCode", deviceCode);
            }
            jsonObject.put("peripheryCode", peripheryCode);
            jsonArray.add(jsonObject);
        });
        log.debug("调用aiot接口获取摄像头参数：{}", jsonArray);
        String body = HttpUtil.createPost(aiotConfig.getHost() + "/service-iot/exapi/camera/status").body(JSONObject.toJSONString(jsonArray)).addHeaders(headers(aiotConfig.getAppid(), aiotConfig.getSecret())).execute().body();
        log.debug("调用aiot接口获取摄像头状态返回：" + body.replaceAll("\\s+", ""));
        Result<List<ExApiCameraStatusRespDTO>> result = JSONObject.parseObject(body, new TypeReference<Result<List<ExApiCameraStatusRespDTO>>>() {
        });
        if (!result.getErrcode().equals("0000")) {
            throw new ScException("摄像头状态获取失败: " + result.getErrmsg());
        }
        return result.getData();
    }


    public List<ExApiDeviceStatusResp> getStaus(List<ExApiDeviceStatusResp> list) {
        String res = HttpUtil.createPost(aiotConfig.getHost() + "/service-iot/exapi/camera/status")
                .body(JSONObject.toJSONString(list))
                .addHeaders(headers(aiotConfig.getAppid(), aiotConfig.getSecret()))
                .execute()
                .body();

        if (JSONUtil.isJsonObj(res)) {
            Result<List<ExApiDeviceStatusResp>> result = JSONObject.parseObject(res, new TypeReference<Result<List<ExApiDeviceStatusResp>>>() {
            });

            if (result.success()) {
                return result.getData();
            }
        }
        XxlJobHelper.log("结果：" + res);
        return null;
    }


    /**
     * 根据appid secret 获取加密 header
     */
    public static Map<String, String> headers(String appid, String secret) {
        Map<String, String> headers = new HashMap<>();
        AuthSignUtil authSignUtil = new AuthSignUtil();
        long time = new Date().getTime();
        String generate = SnowflakeGenerator.generate();
        String sign = authSignUtil.sign(appid, generate, String.valueOf(time), secret);
        headers.put("appid", appid);
        headers.put("nonce", generate);
        headers.put("timestamp", String.valueOf(time));
        headers.put("signature", sign);
        headers.put("Content-Type", "application/json");
        return headers;
    }
}
