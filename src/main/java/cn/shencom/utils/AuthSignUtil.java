package cn.shencom.utils;

import cn.shencom.scloud.common.base.snowflake.SnowflakeGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.codec.Hex;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Map.Entry;
import java.util.TreeMap;

@Slf4j
public class AuthSignUtil {

    public static String sign(String appid, String nonce, String timestamp, String secret) {
        TreeMap<String, String> map = new TreeMap<>();
        map.put("appid", appid);
        map.put("nonce", nonce);
        map.put("timestamp", timestamp);
        map.put("secret", secret);
        String content = "";
        for (Entry<String, String> entry : map.entrySet()) {
            content = String.format("%s&%s=%s", content, urlencode(entry.getKey()), urlencode(entry.getValue()));
        }
        // 需要去掉第一个&
        return md5(content.substring(1));
    }

    public static String md5(String text) {
        try {
            log.debug("sign: {}", text);
            MessageDigest inst = MessageDigest.getInstance("MD5");
            inst.update(text.getBytes("UTF-8"));
            byte[] bytes = inst.digest();
            return new String(Hex.encode(bytes));
        } catch (NoSuchAlgorithmException e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        } catch (UnsupportedEncodingException e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    public static String urlencode(String text) {
        try {
            return URLEncoder.encode(text, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    public static void main(String[] args) {
//		appid: sfl8cf1f925eefe4aa3
//		secret: 8cf1f925eefe4aa3
//		aeskey: e2e0a3618cf1f925eefe4aa36e2f0e34
		String sign = sign("sfl8cf1f925eefe4aa3", SnowflakeGenerator.generate(), "1710333935000", "8cf1f925eefe4aa3");
//        String sign = sign("sfl8cf1f925eefe4aa3", "false", "111", "1710233289000", "8cf1f925eefe4aa3");
        System.out.println(sign);
    }
}
