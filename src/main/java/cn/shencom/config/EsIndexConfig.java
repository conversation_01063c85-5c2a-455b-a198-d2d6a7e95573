package cn.shencom.config;

import cn.shencom.scloud.common.util.ScidContext;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * User: wuxiaohu
 * Date: 2022-06-01
 * Time: 11:34
 *
 * <AUTHOR>
 */
@Component
public class EsIndexConfig {
    @Value("${scid}")
    private String scid;

    public final static String EVENT_CAMERA_VOICE_LOG_INDEX = "event_camera_voice_log_v2";
    public String getEventCameraVoiceLogIndex() {
        String scid = ScidContext.getScid();
        if(StringUtils.isEmpty(scid)){
            scid = this.scid;
        }
        return scid.toLowerCase() + "_" + EVENT_CAMERA_VOICE_LOG_INDEX;
    }


    public final static String EVENT_ORDER_INDEX = "event_order";

    public String getEventOrderIndex() {
        String scid = ScidContext.getScid();
        if(StringUtils.isEmpty(scid)){
            scid = this.scid;
        }
        return scid.toLowerCase() + "_" + EVENT_ORDER_INDEX;
    }

    public String getEventCameraVoiceLogIndex(Date date) {
        String index = getEventCameraVoiceLogIndex();
        DateFormat format = new SimpleDateFormat("yyyy");
        return index + "_" + format.format(date);
    }
}
