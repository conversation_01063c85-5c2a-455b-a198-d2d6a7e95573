#!/bin/bash -x

################################################
# Define constants
################################################

# 支持参数
# 必须指定 --env/-e 部署环境 可选值: dev prod
# --version/-v 指定今日第几个版本 例如 1
# --builder/-b 指定用户名, 没有默认选择git用户名
# --platform/-p 没有默认为只打包amd64 / 可选值: all(全部打包) arm64(arm64) amd64(amd64)
#
help() {
  echo "  支持参数
  必须指定 --env/-e 部署环境 可选值: dev prod
  --version/-v 指定今日第几个版本 例如 1
  --builder/-b 指定用户名, 没有则默认选择git配置的用户名
  --platform/-p 没有默认为只打包amd64 / 可选值: all(全部打包) arm64(arm64) amd64(amd64)
  --only-push 只推送,适用于在推送失败之后使用,避免重复build
  "
}

while [[ $# -gt 0 ]]; do
  key="$1"

  case $key in
  -v | --version)
    VERSION="$2"
    shift 2
    ;;
  -e | --env)
    ENV="$2"
    shift 2
    ;;
  -p | --platform)
    PLATFORM="$2"
    shift 2
    ;;
  -b | --builder)
    BUILDER="$2"
    shift 2
    ;;
  --only-push)
    ONLY_PUSH=1
    shift 1
    ;;
  -h | --help)
    help
    exit 1
    ;;
  *)
    echo "Unknown option $1"
    exit 1
    ;;
  esac
done

if [ -z "$VERSION" ]; then
  VERSION=1
fi

if [ -z "$BUILDER" ]; then
  BUILDER=$(git config user.name)
fi

if [ -z "$PLATFORM" ]; then
  PLATFORM=amd64
fi

if [ -z "$ENV" ]; then
  echo "缺少部署环境参数'-e/--env'"
  exit 1
fi

echo "VERSION=$VERSION"
echo "BUILDER=$BUILDER"
echo "PLATFORM=$PLATFORM"
echo "ENV=$ENV"
echo "ONLY_PUSH=$ONLY_PUSH"

OS_TYPE=$(uname)
NAMESPACE="scloud"
DOCKER_FILE_PATH="."
SERVICE_NAME=$(pwd | awk -F '/' '{print $NF}')
REGISTRY_URL="hb.shencom.cn"
REGISTRY_URL_PROD="registry.cn-shenzhen.aliyuncs.com"

CURRENT_DATE=$(date +'%Y%m%d')
TAG="$BUILDER"-"$CURRENT_DATE"-"$VERSION"
IMAGE="$REGISTRY_URL"/"$NAMESPACE"/"$SERVICE_NAME":"$TAG"

##docker swarm 相关

#部署主机信息
SWARM_MASTER_HOST="*************"
SWARM_MASTER_USERNAME="root"
SWARM_MASTER_PORT="22"

##远程
#部署的stack
SWARM_STACK="hoxton"
#部署主机存放的目录路径
TARGET_YAML_PATH="/home/<USER>/docker/scloud/${SERVICE_NAME}"
#目标存放文件名
TARGET_FILE_NAME="${SERVICE_NAME}-${TAG}.yml"
TARGET_FILE_ABSOLUTE_PATH="${TARGET_YAML_PATH}/${TARGET_FILE_NAME}"

##本地
#模板目录的路径
SWARM_YML_PATH="."
#模板名
SWARM_YML_TMP_FILE_NAME="template.yml"
#本地模板文件的路径
SWARM_YML_TMP_FILE_PATH="${SWARM_YML_PATH}/${SWARM_YML_TMP_FILE_NAME}"

#替换模板数量
SWARM_YML_TMP_REPLICAS="1"
#替换模板网络名
SWARM_YML_TMP_NETWORK="beta-overlay"
SWARM_YML_TMP_MEMORY_LIMIT="1.2G"
SWARM_YML_TMP_MEMORY_RSV="0.8G"

#存放替换后的目录路径
SWARM_YML_SERVICE_PATH="./swarm"
#存放替换后文件路径
SWARM_YML_SERVICE_FILE_PATH="${SWARM_YML_SERVICE_PATH}/${TARGET_FILE_NAME}"

################################################
# Define functions
################################################
package() {
  mvnd clean package -Dmaven.test.skip=true
  # mvnd命令不存在使用mvn
  if [[ $? -eq 127 ]]; then
    mvn clean package -Dmaven.test.skip=true
  fi
}

does_fail() {
  if [[ $? -eq 0 ]]; then
    echo -e "\033[93mSuccess! \033[0m"
  else
    echo -e "\033[93mError! \033[0m"
    exit 1
  fi
}

build() {
  docker -v
  if [ "$(uname)" = "Darwin" ]; then
    docker build -t "$IMAGE" $DOCKER_FILE_PATH --platform=linux/amd64
  else
    docker build -t "$IMAGE" $DOCKER_FILE_PATH
  fi
}

push() {
  docker push "$IMAGE"
}

print_image() {
  echo -e "\033[93m$IMAGE\033[0m"
}

exist_template_file() {
  if [ ! -f "${SWARM_YML_TMP_FILE_PATH}" ]; then
    echo -e "请创建 \033[31m${SWARM_YML_TMP_FILE_PATH}\033[0m 服务模板!"
    exit 1
  fi
}
copy_template() {

  if [ ! -d "${SWARM_YML_SERVICE_PATH}" ]; then
    mkdir -p ${SWARM_YML_SERVICE_PATH}
  fi
  cp ${SWARM_YML_TMP_FILE_PATH} ${SWARM_YML_SERVICE_FILE_PATH}
  if [ "$OS_TYPE" = "Darwin" ]; then
    sed -i '' -e "s,{{name}},${SERVICE_NAME},g" ${SWARM_YML_SERVICE_FILE_PATH}
    sed -i '' -e "s,{{image}},${IMAGE},g" ${SWARM_YML_SERVICE_FILE_PATH}
    sed -i '' -e "s,{{replicas}},${SWARM_YML_TMP_REPLICAS},g" ${SWARM_YML_SERVICE_FILE_PATH}
    sed -i '' -e "s,{{network}},${SWARM_YML_TMP_NETWORK},g" ${SWARM_YML_SERVICE_FILE_PATH}
    sed -i '' -e "s,{{memory_limit}},${SWARM_YML_TMP_MEMORY_LIMIT},g" ${SWARM_YML_SERVICE_FILE_PATH}
    sed -i '' -e "s,{{memory_rsv}},${SWARM_YML_TMP_MEMORY_RSV},g" ${SWARM_YML_SERVICE_FILE_PATH}
  else
    sed -i "s,{{name}},${SERVICE_NAME},g" ${SWARM_YML_SERVICE_FILE_PATH}
    sed -i "s,{{image}},${IMAGE},g" ${SWARM_YML_SERVICE_FILE_PATH}
    sed -i "s,{{replicas}},${SWARM_YML_TMP_REPLICAS},g" ${SWARM_YML_SERVICE_FILE_PATH}
    sed -i "s,{{network}},${SWARM_YML_TMP_NETWORK},g" ${SWARM_YML_SERVICE_FILE_PATH}
    sed -i "s,{{memory_limit}},${SWARM_YML_TMP_MEMORY_LIMIT},g" ${SWARM_YML_SERVICE_FILE_PATH}
    sed -i "s,{{memory_rsv}},${SWARM_YML_TMP_MEMORY_RSV},g" ${SWARM_YML_SERVICE_FILE_PATH}
  fi
}

push_swarm_file() {
  #Remote file must exist
  ssh -p ${SWARM_MASTER_PORT} ${SWARM_MASTER_USERNAME}@${SWARM_MASTER_HOST} "mkdir -p ${TARGET_YAML_PATH}"
  scp -P ${SWARM_MASTER_PORT} ${SWARM_YML_SERVICE_FILE_PATH} ${SWARM_MASTER_USERNAME}@${SWARM_MASTER_HOST}:${TARGET_FILE_ABSOLUTE_PATH}
}

delete_template() {
  #delete template ./swarm
  rm -rf ${SWARM_YML_SERVICE_PATH}
}

swarm_cp() {

  exist_template_file

  copy_template

  push_swarm_file

  delete_template
}

swarm_apply() {
  #apply
  ssh -p ${SWARM_MASTER_PORT} ${SWARM_MASTER_USERNAME}@${SWARM_MASTER_HOST} "docker stack deploy -c ${TARGET_FILE_ABSOLUTE_PATH} ${SWARM_STACK} --with-registry-auth"
}

buildx_push() {
  docker -v
  docker buildx version
  if [ "$PLATFORM" = "arm64" ]; then
    docker buildx build -t "$IMAGE" $DOCKER_FILE_PATH --platform=linux/arm64 --push
  elif [ "$PLATFORM" = "all" ]; then
    docker buildx build -t "$IMAGE" $DOCKER_FILE_PATH --platform=linux/arm64,linux/amd64 --push
  fi
}

build_and_push() {
  if [ "$ONLY_PUSH" ]; then
    push
    does_fail
  elif [ "$PLATFORM" = "amd64" ]; then
    package
    does_fail
    build
    does_fail
    push
    does_fail
  else
    package
    does_fail
    buildx_push
    does_fail
  fi
  docker manifest inspect -v "$IMAGE" | grep architecture
}

################################################
# The Main function
################################################
main() {
  build_and_push
  swarm_cp
  does_fail
  swarm_apply
  does_fail
  print_image
}

main_prod() {
  build_and_push
  print_image
  does_fail
}

if [ -n "$ENV" ]; then
  case $ENV in
  dev)
    IMAGE="$REGISTRY_URL"/"$NAMESPACE"/"$SERVICE_NAME":"$TAG"
    echo -e "\033[93m即将发布到测试环境！\033[0m"
    main
    ;;
  prod)
    IMAGE="$REGISTRY_URL_PROD"/"$NAMESPACE"/"$SERVICE_NAME":"$TAG"
    echo -e "\033[93m即将发布到生产环境！\033[0m"
    main_prod
    ;;
  default)
    echo -e "\033[93m即将发布到默认环境！\033[0m"
    main_prod
    ;;
  help)
    help
    ;;
  *)
    help
    ;;
  esac
else
  echo -e "\033[93m即将发布到默认环境！\033[0m"
  main
fi
