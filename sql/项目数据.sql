-- 获取用户权限的项目数量

SELECT m.realname, r.level, b.region_pid, b.region_id, b.region_cid, t.title
FROM
    fn_rmsv3_members_type_relate r
    left join fn_rmsv3_members_type t on t.id = r.type_id
    left JOIN fn_rmsv3_members_type_relate_binding b on b.relate_id = r.id
    left join fn_rmsv3_members m on m.id = r.member_id
WHERE
    r.is_deleted = 0
    AND b.is_deleted = 0
    AND m.is_deleted = 0
    AND t.id != 1
    AND m.user_id = "1713759728019324928"
    AND m.organization_id = "1711957904168636416";

-- 1. 有组织的情况
-- 获取上面用户的区域权限,在值复制到对应的添加上
select *
from sporadic_project
where
    is_deleted = 0
    and organization_id = "1711957904168636416"
    -- and region_pid in (1711957904168636416)
    and region_id in (
        832789087816818864,
        832789087816818860
    )
    -- and region_cid in (1711957904168636416)

-- 获取工程事件
select *
FROM aiot_event
WHERE
    is_deleted = 0
    AND project_id in (1712694790678814720)

-- 事件
SELECT e.*, p.*
FROM
    aiot_event e
    LEFT JOIN sporadic_project p ON p.id = e.project_id
WHERE
    e.is_deleted = 0
    AND p.organization_id = "1708599276551712768"
    AND p.is_deleted = 0
    -- and p.region_cid in (
    --     832789087816819449,
    --     832789087816819448
    -- )

SELECT * FROM com_region WHERE p_id = 107