CREATE TABLE `com_region` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `p_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '父级ID',
  `type` varchar(64) CHARACTER SET utf8 DEFAULT '' COMMENT '类型， province, city, district, subdistrict, community, neighborhood, residential community',
  `title` varchar(256) CHARACTER SET utf8 DEFAULT NULL COMMENT '名称',
  `spell` varchar(64) CHARACTER SET utf8 DEFAULT NULL COMMENT '名称拼音',
  `spell_short` varchar(16) CHARACTER SET utf8 DEFAULT NULL COMMENT '名称拼音首字母',
  `brief` varchar(512) CHARACTER SET utf8 DEFAULT NULL COMMENT '简述',
  `fence_id` text COMMENT '区域块点位id,多个用逗号分隔',
  `cover` varchar(1024) CHARACTER SET utf8 DEFAULT NULL COMMENT '图片',
  `content` text CHARACTER SET utf8 COMMENT '内容',
  `resource_id` bigint(20) DEFAULT NULL COMMENT '图片资源编号',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `status` tinyint(4) DEFAULT NULL COMMENT '是否开启',
  `pid` varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '父id',
  `sort` int(20) DEFAULT '0' COMMENT '排序',
  `poi_id` bigint(20) DEFAULT NULL COMMENT '经纬度表id (gis_poi)',
  `longitude` decimal(10,7) DEFAULT NULL COMMENT '经度',
  `latitude` decimal(10,7) DEFAULT NULL COMMENT '纬度',
  `gd_id` varchar(16) CHARACTER SET utf8 DEFAULT NULL COMMENT '行政区划在高德的id',
  `gd_adcode` varchar(16) CHARACTER SET utf8 DEFAULT NULL COMMENT '行政区划在高德的adcode',
  `district_id` varchar(20) CHARACTER SET utf8 DEFAULT NULL COMMENT 'districtId',
  `region_code` bigint(16) DEFAULT NULL COMMENT '行政区划编码',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `deleted_at` (`deleted_at`) USING BTREE,
  KEY `pid_title` (`p_id`,`title`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1656394300069187585 DEFAULT CHARSET=utf8mb4 COMMENT='通用地区';

CREATE TABLE `sporadic_project` (
  `id` bigint(20) NOT NULL COMMENT '工程ID',
  `name` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '工程名称',
  `organization_id` bigint(20) DEFAULT NULL COMMENT '组织id，关联xsgc_organization',
  `cate_pid` bigint(20) DEFAULT NULL COMMENT '工程分类ID',
  `cate_id` bigint(20) DEFAULT NULL COMMENT '工程类别ID',
  `amount` decimal(12,2) DEFAULT NULL COMMENT '工程金额(元)',
  `area` decimal(8,2) DEFAULT NULL COMMENT '实际施工面积',
  `start_at` date DEFAULT NULL COMMENT '工程开始时间',
  `end_at` date DEFAULT NULL COMMENT '工程结束时间',
  `region_pid` bigint(20) DEFAULT NULL COMMENT '所在区',
  `region_id` bigint(20) DEFAULT NULL COMMENT '所在街道',
  `region_cid` bigint(20) DEFAULT NULL COMMENT '所在社区',
  `address` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '详细地址',
  `lng` decimal(13,10) DEFAULT NULL COMMENT '经度',
  `lat` decimal(13,10) DEFAULT NULL COMMENT '纬度',
  `constructor_id` bigint(20) DEFAULT NULL COMMENT '建设单位ID',
  `constructor_name` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '建设单位名称',
  `constructor_charger_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '建设单位负责人ID',
  `constructor_charger` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '建设单位负责人',
  `owner_mobile` varchar(16) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '业主电话',
  `contractor_id` bigint(20) DEFAULT NULL COMMENT '施工单位ID',
  `contractor_name` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '施工单位名称',
  `contractor_charger_id` bigint(20) DEFAULT NULL COMMENT '施工单位负责人ID',
  `contractor_charger` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '施工单位负责人',
  `contractor_charger_mobile` varchar(16) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '施工单位负责人电话',
  `project_number` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备案编号',
  `status` tinyint(4) DEFAULT '0' COMMENT '施工状态，0-未开始施工，1-施工中，2-施工已结束',
  `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
  `monitor_flag` tinyint(4) DEFAULT '0' COMMENT '是否已接入监管，0-否，1-是',
  `poi_id` bigint(20) DEFAULT NULL COMMENT '点位id，对应gis_poi 表的id',
  `is_deleted` tinyint(4) DEFAULT '0' COMMENT '是否删除:0-否,1-是',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='小散工程表';


-- ================================
-- 问题分析和解决方案
-- ================================
/*
问题描述：
用户有特定社区权限（如：832789087816818953, 832789087816819449）
当传入 regionPid=84 时，当前查询会返回所有该用户有权限的社区
但实际应该只返回属于该 regionPid 下且用户有权限的社区

解决方案：
1. 在统计项目数量时，同时过滤层级和权限
2. 在显示区域列表时，确保只显示用户有权限且属于指定层级的区域
3. 添加 userRegionCidSet 参数来传递用户的社区权限
*/

SELECT
    c.id,
    c.title,
    c.type,
    IFNULL(t.cnt, 0) projectCount
FROM
    com_region c
    LEFT JOIN (
        SELECT
            COUNT(p.id) cnt,
        <#if regionCid?? || regionCidSet??>
            p.region_cid region
        <#elseif regionId?? || regionIdSet??>
            p.region_cid region
        <#elseif regionPid?? || regionPidSet??>
            p.region_cid region
        <#else>
            p.region_pid region
        </#if>
        FROM
            sporadic_project p
        WHERE p.is_deleted = 0
            AND p.organization_id = :organizationId
        <#if regionPid??>
            AND p.region_pid = :regionPid
            <#if userRegionCidSet??>
            AND p.region_cid IN (:userRegionCidSet)
            </#if>
        <#elseif regionPidSet??>
            AND p.region_pid IN (:regionPidSet)
            <#if userRegionCidSet??>
            AND p.region_cid IN (:userRegionCidSet)
            </#if>
        </#if>
        <#if regionId??>
            AND p.region_id = :regionId
            <#if userRegionCidSet??>
            AND p.region_cid IN (:userRegionCidSet)
            </#if>
        <#elseif regionIdSet??>
            AND p.region_id IN (:regionIdSet)
            <#if userRegionCidSet??>
            AND p.region_cid IN (:userRegionCidSet)
            </#if>
        </#if>
        <#if regionCid??>
            AND p.region_cid = :regionCid
        <#elseif regionCidSet??>
            AND p.region_cid IN (:regionCidSet)
        </#if>
        GROUP BY region
    ) t ON t.region = c.id
WHERE c.deleted_at IS NULL
<#if regionCid??>
    AND c.type = 'community'
    AND c.id = :regionCid
<#elseif regionCidSet??>
    AND c.type = 'community'
    AND c.id IN (:regionCidSet)
<#elseif regionId??>
    AND c.type = 'community'
    AND c.p_id = :regionId
    <#if userRegionCidSet??>
    AND c.id IN (:userRegionCidSet)
    </#if>
<#elseif regionIdSet??>
    AND c.type = 'community'
    AND c.p_id IN (:regionIdSet)
    <#if userRegionCidSet??>
    AND c.id IN (:userRegionCidSet)
    </#if>
<#elseif regionPid??>
    AND c.type = 'community'
    AND c.p_id IN (
        SELECT id FROM com_region 
        WHERE p_id = :regionPid 
        AND type = 'subdistrict' 
        AND deleted_at IS NULL
    )
    <#if userRegionCidSet??>
    AND c.id IN (:userRegionCidSet)
    </#if>
<#elseif regionPidSet??>
    AND c.type = 'community'
    AND c.p_id IN (
        SELECT id FROM com_region 
        WHERE p_id IN (:regionPidSet) 
        AND type = 'subdistrict' 
        AND deleted_at IS NULL
    )
    <#if userRegionCidSet??>
    AND c.id IN (:userRegionCidSet)
    </#if>
<#else>
    AND c.type = 'district'
    AND c.p_id = '2'
</#if>
ORDER BY c.sort DESC


-- ================================
-- 原需求说明和问题分析
-- ================================

现在我有个需求,需要统计某个用户下面的区域的工程数量

如果用户只有社区的权限,那么统计社区的工程数量
数据会传过来一个region_pid,需要过滤出来,对应的社区的工程数量

我上面的的查询存在问题,
用户有2个社区的权限,当用户选择一个社区上层的pid的时候,会查出来跨区域的数据,这个是错误的
如果传一个region_pid,需要过滤出来,对应的社区的工程数量

问题示例：
用户有2个社区的权限 `regionCid = 832789087816818953,832789087816819449`
传的参数为 `regionPid=84`

原来错误的sql会得到：
```
id,title,type,projectCount
832789087816818953	三联社区	community	0
832789087816819449	九围社区	community	0
```

但正确的结果应该是（假设只有九围社区属于区域84）：
```
832789087816819449	九围社区	community	0
```

-- ================================
-- 解决方案说明
-- ================================

修正后的SQL通过以下方式解决问题：

1. 添加 userRegionCidSet 参数传递用户的社区权限
2. 在统计项目时，同时过滤层级关系(region_pid)和用户权限(region_cid)
3. 在显示社区时，通过子查询确保社区属于指定区域下的街道
4. 确保统计逻辑和显示逻辑的一致性

使用示例：
```java
// Java代码中需要传递的参数
Map<String, Object> params = new HashMap<>();
params.put("organizationId", 1711957904168636416L);
params.put("regionPid", 84L);
params.put("userRegionCidSet", Arrays.asList(832789087816818953L, 832789087816819449L));
```




