-- 施工单位表
DROP TABLE IF EXISTS `contracting_unit`;
CREATE TABLE `contracting_unit` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(255) NOT NULL COMMENT '施工单位名称',
  `admin_user_id` varchar(32) DEFAULT NULL COMMENT '管理员用户ID，关联移动端认证的管理员',
  `admin_name` varchar(100) DEFAULT NULL COMMENT '管理员姓名',
  `admin_mobile` varchar(20) DEFAULT NULL COMMENT '管理员联系方式',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：0-无效，1-有效',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_user` varchar(32) DEFAULT NULL COMMENT '创建人',
  `updated_user` varchar(32) DEFAULT NULL COMMENT '更新人',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`),
  KEY `idx_admin_user_id` (`admin_user_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_is_deleted` (`is_deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='施工单位表';

-- 施工单位组织关联表
DROP TABLE IF EXISTS `contracting_unit_organization_relate`;
CREATE TABLE `contracting_unit_organization_relate` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `contracting_unit_id` bigint(20) NOT NULL COMMENT '施工单位ID',
  `organization_id` bigint(20) NOT NULL COMMENT '组织ID',
  `credit_score` decimal(5,2) DEFAULT '100.00' COMMENT '信用分数，每个组织独立计算',
  `is_blacklist` tinyint(1) DEFAULT '0' COMMENT '是否黑名单：0-否，1-是',
  `blacklist_reason` varchar(500) DEFAULT NULL COMMENT '黑名单原因',
  `blacklist_time` datetime DEFAULT NULL COMMENT '加入黑名单时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_user` varchar(32) DEFAULT NULL COMMENT '创建人',
  `updated_user` varchar(32) DEFAULT NULL COMMENT '更新人',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_unit_org` (`contracting_unit_id`, `organization_id`),
  KEY `idx_contracting_unit_id` (`contracting_unit_id`),
  KEY `idx_organization_id` (`organization_id`),
  KEY `idx_is_blacklist` (`is_blacklist`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_is_deleted` (`is_deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='施工单位组织关联表';

-- 施工单位统计视图（用于列表展示）
DROP VIEW IF EXISTS `v_construction_unit_statistics`;
CREATE VIEW `v_construction_unit_statistics` AS
SELECT 
    cu.id,
    cu.name,
    cu.admin_name,
    cu.admin_mobile,
    cu.status,
    cu.created_at,
    cuor.organization_id,
    cuor.credit_score,
    cuor.is_blacklist,
    cuor.blacklist_reason,
    cuor.blacklist_time,
    -- 施工工人数量（通过施工单位名称关联工程，再通过关联表关联工程人员）
    COALESCE(worker_stats.worker_count, 0) AS worker_count,
    -- 施工负责人数量（通过施工单位名称关联工程，再通过关联表关联工程人员）
    COALESCE(leader_stats.leader_count, 0) AS leader_count,
    -- 工程总数（按组织权限过滤）
    COALESCE(project_stats.total_project_count, 0) AS total_project_count,
    -- 施工中工程数量
    COALESCE(project_stats.ongoing_project_count, 0) AS ongoing_project_count,
    -- 告警次数（通过工程关联监控事件）
    COALESCE(alert_stats.alert_count, 0) AS alert_count,
    -- 违规问题数量（预留字段）
    0 AS violation_count,
    -- 违规整改数量（预留字段）
    0 AS rectification_count
FROM construction_unit cu
INNER JOIN construction_unit_organization_relate cuor ON cu.id = cuor.construction_unit_id
-- 施工工人统计
LEFT JOIN (
    SELECT 
        sp.contractor,
        sp.organization_id,
        COUNT(DISTINCT em.id) AS worker_count
    FROM sporadic_project sp
    LEFT JOIN engineering_members_project_relate empr ON CAST(sp.id AS CHAR) = CAST(empr.project_id AS CHAR)
        AND empr.is_deleted = 0
    LEFT JOIN engineering_members em ON empr.member_id = em.id
        AND em.type = 3 -- 假设3为施工工人类型
        AND em.is_deleted = 0
    WHERE sp.is_deleted = 0
    GROUP BY sp.contractor, sp.organization_id
) worker_stats ON cu.name COLLATE utf8mb4_unicode_ci = worker_stats.contractor COLLATE utf8mb4_unicode_ci
    AND cuor.organization_id = worker_stats.organization_id
-- 施工负责人统计
LEFT JOIN (
    SELECT 
        sp.contractor,
        sp.organization_id,
        COUNT(DISTINCT em.id) AS leader_count
    FROM sporadic_project sp
    LEFT JOIN engineering_members_project_relate empr ON CAST(sp.id AS CHAR) = CAST(empr.project_id AS CHAR)
        AND empr.is_deleted = 0
    LEFT JOIN engineering_members em ON empr.member_id = em.id
        AND em.type = 2 -- 假设2为施工负责人类型
        AND em.is_deleted = 0
    WHERE sp.is_deleted = 0
    GROUP BY sp.contractor, sp.organization_id
) leader_stats ON cu.name COLLATE utf8mb4_unicode_ci = leader_stats.contractor COLLATE utf8mb4_unicode_ci
    AND cuor.organization_id = leader_stats.organization_id
-- 工程统计
LEFT JOIN (
    SELECT 
        contractor,
        organization_id,
        COUNT(*) AS total_project_count,
        SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) AS ongoing_project_count
    FROM sporadic_project
    WHERE is_deleted = 0
    GROUP BY contractor, organization_id
) project_stats ON cu.name COLLATE utf8mb4_unicode_ci = project_stats.contractor COLLATE utf8mb4_unicode_ci
    AND cuor.organization_id = project_stats.organization_id
-- 告警统计
LEFT JOIN (
    SELECT 
        sp.contractor,
        sp.organization_id,
        COUNT(ae.id) AS alert_count
    FROM sporadic_project sp
    LEFT JOIN aiot_event ae ON CAST(sp.id AS CHAR) = CAST(ae.project_id AS CHAR)
        AND ae.is_deleted = 0
    WHERE sp.is_deleted = 0
    GROUP BY sp.contractor, sp.organization_id
) alert_stats ON cu.name COLLATE utf8mb4_unicode_ci = alert_stats.contractor COLLATE utf8mb4_unicode_ci
    AND cuor.organization_id = alert_stats.organization_id
WHERE cu.is_deleted = 0 
    AND cuor.is_deleted = 0;

-- 初始化数据：从现有工程中提取施工单位信息
INSERT IGNORE INTO construction_unit (name, created_at, created_user)
SELECT 
    contractor AS name,
    MIN(created_at) AS created_at,
    CAST(MIN(create_user) AS CHAR) AS created_user
FROM sporadic_project 
WHERE is_deleted = 0 
    AND contractor IS NOT NULL 
    AND contractor != ''
GROUP BY contractor;

-- 初始化组织关联数据
INSERT IGNORE INTO construction_unit_organization_relate (construction_unit_id, organization_id, created_at, created_user)
SELECT 
    cu.id AS construction_unit_id,
    sp.organization_id AS organization_id,
    MIN(sp.created_at) AS created_at,
    CAST(MIN(sp.create_user) AS CHAR) AS created_user
FROM construction_unit cu
INNER JOIN sporadic_project sp ON cu.name COLLATE utf8mb4_unicode_ci = sp.contractor COLLATE utf8mb4_unicode_ci
WHERE sp.is_deleted = 0 
    AND sp.organization_id IS NOT NULL
GROUP BY cu.id, sp.organization_id; 