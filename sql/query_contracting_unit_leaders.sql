-- 查询施工单位对应的负责人数量
-- 方案1：基于 engineering_members 表中 type=2 的记录统计
SELECT
    cu.id as contracting_unit_id,
    cu.name as contracting_unit_name,
    COUNT(DISTINCT em.id) as leader_count
FROM
    contracting_unit cu
    LEFT JOIN contracting_unit_organization_relate cuor ON cu.id = cuor.contracting_unit_id
    AND cuor.is_deleted = 0
    LEFT JOIN engineering_members_project_relate empr ON cuor.organization_id = empr.organization_id
    AND empr.is_deleted = 0
    LEFT JOIN engineering_members em ON empr.member_id = em.id
    AND em.is_deleted = 0
    AND em.status = 1
    AND em.type = 2 -- 2表示施工单位负责人
WHERE
    cu.is_deleted = 0
    AND cu.status = 1
GROUP BY
    cu.id,
    cu.name
ORDER BY cu.name;

-- 方案2：如果只统计在组织关联表中有负责人信息的施工单位
SELECT
    cu.id as contracting_unit_id,
    cu.name as contracting_unit_name,
    COUNT(DISTINCT cuor.leader) as leader_count
FROM
    contracting_unit cu
    LEFT JOIN contracting_unit_organization_relate cuor ON cu.id = cuor.contracting_unit_id
    AND cuor.is_deleted = 0
    AND cuor.leader IS NOT NULL
    AND cuor.leader != ''
WHERE
    cu.is_deleted = 0
    AND cu.status = 1
GROUP BY
    cu.id,
    cu.name
ORDER BY cu.name;

-- 方案3：综合统计（包含管理员和组织负责人）
SELECT
    cu.id as contracting_unit_id,
    cu.name as contracting_unit_name,
    CASE
        WHEN cu.admin_name IS NOT NULL
        AND cu.admin_name != '' THEN 1
        ELSE 0
    END as has_admin,
    COUNT(DISTINCT cuor.leader) as org_leader_count,
    COUNT(DISTINCT em.id) as engineering_leader_count,
    (
        CASE
            WHEN cu.admin_name IS NOT NULL
            AND cu.admin_name != '' THEN 1
            ELSE 0
        END + COUNT(DISTINCT cuor.leader) + COUNT(DISTINCT em.id)
    ) as total_leader_count
FROM
    contracting_unit cu
    LEFT JOIN contracting_unit_organization_relate cuor ON cu.id = cuor.contracting_unit_id
    AND cuor.is_deleted = 0
    AND cuor.leader IS NOT NULL
    AND cuor.leader != ''
    LEFT JOIN engineering_members_project_relate empr ON cuor.organization_id = empr.organization_id
    AND empr.is_deleted = 0
    LEFT JOIN engineering_members em ON empr.member_id = em.id
    AND em.is_deleted = 0
    AND em.status = 1
    AND em.type = 2 -- 2表示施工单位负责人
WHERE
    cu.is_deleted = 0
    AND cu.status = 1
GROUP BY
    cu.id,
    cu.name,
    cu.admin_name
ORDER BY total_leader_count DESC, cu.name;

-- SELECT *
-- FROM sporadic_project p
-- WHERE
--     1 = 1
--     AND id is not NULL;

-- update sporadic_project set constructor_name = constructor 

-- 我想要把 sporadic_project_copy 中的 constructor 复制 给 sporadic_project.constructor

