-- UPDATE sporadic_project sp
-- JOIN construction_unit cu ON sp.contractor_name = cu.name
-- SET
--     sp.contractor_id = cu.id,
--     sp.contractor_name = cu.name
-- WHERE
--      cu.is_deleted = 0;


-- SELECT * FROM sporadic_project sp
-- JOIN construction_unit cu ON sp.contractor_name = cu.name
-- -- WHERE sp.contractor IS NOT NULL
-- --     AND sp.contractor != ''
-- --     AND cu.is_deleted = 0;

-- SELECT * FROM sporadic_project

-- 我想把 construction_unit 的 id 移除自增
update construction_unit set id = null;