-- @SET @user_id = '1711958416497926144';

SELECT * FROM fn_rmsv3_members WHERE is_deleted = 0

SELECT *
FROM
    fn_rmsv3_members_type_relate_binding
WHERE
    is_deleted = 0;

SELECT * FROM fn_rmsv3_members_type;

SELECT * FROM fn_rmsv3_members_type_relate WHERE is_deleted = 0;

SELECT *
FROM
    fn_rmsv3_members_type_relate_binding
WHERE
    is_deleted = 0;

-- 用户所属组织
SELECT *
FROM fn_rmsv3_members
WHERE
    is_deleted = 0
    AND user_id = "1711958416497926144";

-- 用户所属区域
SELECT m.realname, r.level, b.region_pid, b.region_id, b.region_cid, t.title,o.id,o.name
FROM
    fn_rmsv3_members_type_relate r
    left join fn_rmsv3_members_type t on t.id = r.type_id
    left JOIN fn_rmsv3_members_type_relate_binding b on b.relate_id = r.id
    left join fn_rmsv3_members m on m.id = r.member_id
    left join xsgc_organization o on o.id = m.organization_id
WHERE
    r.is_deleted = 0
    AND b.is_deleted = 0
    AND m.is_deleted = 0
    AND o.is_deleted = 0
    AND m.user_id = "1711958416497926144"
    AND m.organization_id in (
        SELECT organization_id
        FROM fn_rmsv3_members
        WHERE
            is_deleted = 0
            AND user_id = m.user_id
    )

-- 获取用户角色

-- 1710876805475950592

SELECT *
FROM sys_role_user_organization
WHERE
    user_id = "1705411683269828608"


-- 组织成员
SELECT * FROM fn_rmsv3_members WHERE is_deleted = 0 AND user_id = "1705411683269828608"


-- 工程角色

-- SELECT * FROM engineering_members;
-- SELECT * FROM engineering_members_type;
-- SELECT * FROM engineering_members_project_relate;
SELECT type.name, member.id, member.realname, member.mobile
FROM
    engineering_members member
    left join engineering_members_type type on type.id = member.`type`
    -- left join engineering_members_project_relate rel on rel.member_id = member.id
WHERE
    member.is_deleted = 0
    AND member.user_id = "1705411683269828608"

-- 业务角色
SELECT * FROM xsgc_business_members_relate;

SELECT member.id, member.realname, member.mobile, type.name
FROM
    xsgc_business_members member
    LEFT JOIN xsgc_business_members_type type on type.id = member.`type`
    LEFT JOIN xsgc_business_members_relate rel on rel.member_id = member.id
WHERE
    member.is_deleted = 0
    AND member.user_id = "1705411683269828608"