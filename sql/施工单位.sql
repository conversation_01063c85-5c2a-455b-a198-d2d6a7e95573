CREATE TABLE `engineering_members` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `user_id` bigint(20) DEFAULT '0' COMMENT '用户id',
    `realname` varchar(64) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '成员姓名',
    `mobile` varchar(16) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '手机号码',
    `type` int(8) DEFAULT NULL COMMENT '职位类型',
    `id_card` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '身份证号',
    `work_type_name` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '工种名称',
    `certificate_number` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '证书编号',
    `certificate_start_date` date DEFAULT NULL COMMENT '证书有效日期-开始时间',
    `certificate_end_date` date DEFAULT NULL COMMENT '证书有效日期-结束时间',
    `certificate_pic` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '证书图片',
    `desc` varchar(1024) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '描述',
    `status` tinyint(4) DEFAULT '1' COMMENT '有效状态，0-无效，1-有效',
    `created_at` datetime DEFAULT NULL COMMENT '创建时间',
    `updated_at` datetime DEFAULT NULL COMMENT '最后更新时间',
    `is_deleted` tinyint(4) DEFAULT '0' COMMENT '是否删除',
    `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `user_id` (`user_id`) USING BTREE,
    KEY `mobile` (`mobile`) USING BTREE,
    KEY `idx_deletedat_realname_userid` (
        `is_deleted`,
        `realname`,
        `user_id`
    ) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1712232369500459009 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '小散工程-工程人员';

CREATE TABLE `engineering_members_type` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `name` varchar(64) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '职位名称 1.建设方（业主）2.施工单位负责人 3.施工工人',
    `desc` varchar(1024) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '描述',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB AUTO_INCREMENT = 4 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '小散工程-工程人员职位';

CREATE TABLE `engineering_members_project_relate` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `member_id` bigint(20) DEFAULT NULL COMMENT '成员id，关联engineering_members',
    `relate_id` bigint(20) DEFAULT NULL COMMENT 'relateId，engineering_members_relate',
    `organization_id` bigint(20) DEFAULT NULL COMMENT '组织id,关联xsgc_organization',
    `project_id` bigint(20) DEFAULT NULL COMMENT '项目id,关联sporadic_project',
    `created_at` datetime DEFAULT NULL COMMENT '创建时间',
    `updated_at` datetime DEFAULT NULL COMMENT '最后更新时间',
    `is_deleted` tinyint(4) DEFAULT '0' COMMENT '是否删除',
    `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1712348436614676481 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '小散工程-工程人员关联项目表';

CREATE TABLE `contracting_unit` (
    `id` bigint(20) NOT NULL COMMENT '主键ID',
    `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '施工单位名称',
    `admin_user_id` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '管理员用户ID，关联移动端认证的管理员',
    `admin_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '管理员姓名',
    `admin_mobile` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '管理员联系方式',
    `status` tinyint(1) DEFAULT '1' COMMENT '状态：0-无效，1-有效',
    `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `created_user` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
    `updated_user` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新人',
    `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-否，1-是',
    `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_name` (`name`),
    KEY `idx_admin_user_id` (`admin_user_id`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_is_deleted` (`is_deleted`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '施工单位表';

CREATE TABLE `contracting_unit_organization_relate` (
    `id` bigint(20) NOT NULL COMMENT '主键ID',
    `contracting_unit_id` bigint(20) NOT NULL COMMENT '施工单位ID',
    `organization_id` bigint(20) NOT NULL COMMENT '组织ID',
    `credit_score` decimal(5, 2) DEFAULT '100.00' COMMENT '信用分数，每个组织独立计算',
    `is_blacklist` tinyint(1) DEFAULT '0' COMMENT '是否黑名单：0-否，1-是',
    `blacklist_reason` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '黑名单原因',
    `blacklist_time` datetime DEFAULT NULL COMMENT '加入黑名单时间',
    `leader` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '施工单位负责人',
    `leader_mobile` varchar(16) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '施工单位负责人电话',
    `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `created_user` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
    `updated_user` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新人',
    `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-否，1-是',
    `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_unit_org` (
        `contracting_unit_id`,
        `organization_id`
    ),
    KEY `idx_contracting_unit_id` (`contracting_unit_id`),
    KEY `idx_organization_id` (`organization_id`),
    KEY `idx_is_blacklist` (`is_blacklist`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_is_deleted` (`is_deleted`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '施工单位组织关联表';

-- 上面基础表结构

SELECT * FROM engineering_members_type WHERE is_deleted = 0;

SELECT * FROM engineering_members WHERE is_deleted = 0;

SELECT *
FROM
    engineering_members_project_relate
WHERE
    is_deleted = 0;

-- SELECT id FROM sporadic_project WHERE is_deleted = 0;

SELECT members.id, members.realname, project.contractor_charger
FROM
    engineering_members members
    JOIN sporadic_project project on members.mobile = project.contractor_charger_mobile
WHERE
    members.type = 2

UPDATE sporadic_project project
SET
    project.contractor_charger_id = (
        SELECT members.id
        FROM engineering_members members
        WHERE
            members.mobile = project.contractor_charger_mobile
            AND members.type = 2
        LIMIT 1 -- 确保只返回一条记录
    )
WHERE
    EXISTS (
        SELECT 1
        FROM engineering_members members
        WHERE
            members.mobile = project.contractor_charger_mobile
            AND members.type = 2
    );