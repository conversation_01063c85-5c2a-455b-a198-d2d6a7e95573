# 工程邀请码功能说明

## 功能概述

工程邀请码功能允许有权限的用户为工程生成邀请码，其他用户可以通过邀请码查看工程信息。

## 权限说明

只有以下角色的用户可以生成邀请码：
- 施工负责人（施工单位负责人）
- 建设方（业主）

## 邀请码规则

- **格式**: 大小写字母+数字混合
- **长度**: 10位
- **有效期**: 1周（7天）
- **唯一性**: 每次生成都是新的唯一码
- **存储**: 使用Redis存储，过期自动删除

## API接口

### 1. 生成邀请码

**接口地址**: `POST /sporadic/project/invite-code/generate`

**请求参数**:
```json
{
  "id": "工程ID"
}
```

**响应示例**:
```json
{
  "code": "0000",
  "message": "操作成功",
  "data": {
    "inviteCode": "A1b2C3d4E5",
    "projectId": "1234567890",
    "projectName": "示例工程",
    "generatedAt": "2025-08-21T10:00:00",
    "expireAt": "2025-08-28T10:00:00",
    "expireSeconds": 604800
  }
}
```

### 2. 通过邀请码获取工程信息

**接口地址**: `POST /sporadic/project/invite-code/project`

**请求参数**:
```json
{
  "inviteCode": "A1b2C3d4E5"
}
```

**响应示例**:
```json
{
  "code": "0000",
  "message": "操作成功",
  "data": {
    "id": "1234567890",
    "name": "示例工程",
    "amount": 100000.00,
    "area": 500.00,
    "startAt": "2025-08-01",
    "endAt": "2025-12-31",
    "address": "深圳市南山区科技园",
    "constructorName": "建设单位名称",
    "contractorName": "施工单位名称",
    "status": 1,
    "statusName": "施工中"
  }
}
```

### 3. 绑定邀请码

**接口地址**: `POST /sporadic/project/invite-code/bind`

**请求参数**:
```json
{
  "inviteCode": "A1b2C3d4E5"
}
```

**响应示例**:
```json
{
  "code": "0000",
  "message": "操作成功",
  "data": {
    "id": "1234567890",
    "name": "示例工程",
    "amount": 100000.00,
    "area": 500.00,
    "startAt": "2025-08-01",
    "endAt": "2025-12-31",
    "address": "深圳市南山区科技园",
    "constructorName": "建设单位名称",
    "contractorName": "施工单位名称",
    "status": 1,
    "statusName": "施工中"
  }
}
```

## 错误码说明

- `工程不存在`: 指定的工程ID不存在
- `您没有权限生成该工程的邀请码`: 当前用户不是该工程的施工负责人或建设方业主
- `邀请码无效或已过期`: 邀请码不存在或已超过有效期
- `您不是工程成员，无法绑定工程`: 当前用户不是系统中的工程成员
- `您已经绑定过该工程`: 当前用户已经绑定过该工程，不能重复绑定

## 使用流程

### 生成邀请码流程
1. **权限验证**: 系统验证当前用户是否为该工程的施工负责人或建设方业主
2. **生成邀请码**: 生成10位随机邀请码
3. **存储到Redis**: 以邀请码为key，工程ID为value存储到Redis，设置7天过期时间
4. **分享邀请码**: 用户可以将邀请码分享给其他人

### 绑定邀请码流程
1. **邀请码验证**: 验证邀请码是否有效且未过期
2. **用户验证**: 验证当前用户是否为系统中的工程成员
3. **重复绑定检查**: 检查用户是否已经绑定过该工程
4. **建立关联关系**: 在工程成员关联表中创建绑定关系

### 查看工程信息流程
1. **邀请码验证**: 验证邀请码是否有效且未过期
2. **查询工程信息**: 根据邀请码获取对应的工程详细信息

## 技术实现

### 核心类

- `InviteCodeConstant`: 邀请码相关常量定义
- `InviteCodeUtil`: 邀请码生成工具类
- `InviteCodeRespDTO`: 邀请码生成响应DTO
- `InviteCodeQueryDTO`: 邀请码查询请求DTO

### 存储方案

使用Redis存储邀请码映射关系：
- **Key**: `sporadic:project:invite:code:{邀请码}`
- **Value**: 工程ID
- **过期时间**: 7天（604800秒）

### 权限验证

#### 生成邀请码权限
通过查询 `engineering_members_project_relate` 表和 `engineering_members` 表，验证当前用户是否为该工程的：
- 建设方（业主）: `type = 1`
- 施工单位负责人: `type = 2`

#### 绑定邀请码权限
只要是系统中的工程成员（`engineering_members` 表中存在记录）都可以绑定工程

## 注意事项

1. 邀请码生成后立即生效，有效期为7天
2. 每次生成都是新的邀请码，旧的邀请码仍然有效（直到过期）
3. 邀请码过期后自动失效，无法查看工程信息或进行绑定
4. 只有有权限的用户才能生成邀请码（施工负责人和建设方业主）
5. 只有系统中的工程成员才能绑定工程
6. 用户不能重复绑定同一个工程
7. 绑定功能不会自动创建工程成员或分配权限，需要预先存在
8. 通过邀请码查看的工程信息是只读的，不包含敏感信息
9. 绑定后的用户可以在"我的工程"中查看已绑定的工程列表
