# 小散工程区域分布统计功能

## 功能概述

新增小散工程区域分布统计功能，用于统计各区域的工程数量分布情况。数据来源为"我的工程"，即当前用户权限范围内可访问的工程项目。

## 数据维度说明

根据选择的区域级别，展示不同维度的统计数据：

- **选择市时**：展示各区数据
- **选择某区时**：展示该区下各街道数据
- **选择某街道时**：展示该街道下各社区数据
- **选择社区时**：只展示该社区数据

## API接口

### 请求地址
```
GET /sporadic-project/region/statistics
```

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| regionId | String | 否 | 区域ID，用于指定统计的父级区域 |
| regionLevel | Integer | 否 | 区域级别：1-市，2-区，3-街道，4-社区 |

### 请求示例

```json
{
  "regionId": "310104",
  "regionLevel": 2
}
```

### 响应参数

| 参数名 | 类型 | 说明 |
|--------|------|------|
| regionId | String | 区域ID |
| regionName | String | 区域名称 |
| projectCount | Long | 工程数量 |

### 响应示例

```json
[
  {
    "regionId": "310104001",
    "regionName": "新桥街道",
    "projectCount": 450
  },
  {
    "regionId": "310104002",
    "regionName": "华漕街道",
    "projectCount": 320
  }
]
```

## 数据库设计

### 新增DTO类

1. **SporadicProjectRegionStatisticsQueryDTO** - 查询参数DTO
   - regionId: 区域ID（街道）
   - regionPid: 区域ID（区）
   - regionCid: 区域ID（社区）
   - regionLevel: 区域级别

2. **SporadicProjectRegionStatisticsRespDTO** - 响应结果DTO
   - regionId: 区域ID
   - title: 区域名称
   - projectCount: 工程数量

### Repository方法

新增以下查询方法：

- `getRegionStatisticsByDistrict()` - 按区统计
- `getRegionStatisticsByStreet()` - 按街道统计
- `getRegionStatisticsByCommunity()` - 按社区统计
- `getRegionStatisticsBySingleCommunity()` - 单个社区统计
- `findProjectIdsByOrganization()` - 根据组织获取项目ID
- `findAllProjectIds()` - 获取所有项目ID

## 权限控制

统计数据基于当前用户的权限范围：

1. **组织用户**：只能查看所属组织的工程数据
2. **个人用户**：只能查看自己创建或参与的工程数据
3. **业务人员**：可查看负责区域内的工程数据
4. **施工人员**：可查看参与施工的工程数据

## 使用场景

1. **管理驾驶舱**：展示区域工程分布概览
2. **统计报表**：生成各级区域工程统计报告
3. **数据分析**：分析工程在各区域的分布情况
4. **决策支持**：为资源配置和管理决策提供数据支撑

## 注意事项

1. 统计结果按工程数量降序排列
2. 只统计用户权限范围内的工程数据
3. 区域级别参数决定统计的粒度
4. 支持多级区域钻取查询