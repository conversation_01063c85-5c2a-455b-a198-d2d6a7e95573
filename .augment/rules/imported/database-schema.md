---
type: "agent_requested"
---

# 数据库架构理解指南

本项目数据库文件：[db_tn_sc047f71a5d4ef1134.sql](mdc:sql/db_tn_sc047f71a5d4ef1134.sql)

## 核心业务模块

### 1. 小散工程管理 (Sporadic Project)
- **sporadic_project**: 小散工程主表，包含工程基本信息、地理位置、施工单位等
- **sporadic_project_category**: 小散工程分类表，支持树形结构
- **engineering_members**: 工程人员表
- **engineering_members_project_relate**: 工程人员与项目关联表
- **engineering_members_type**: 工程人员职位类型

### 2. 监管工单流程 (Monitor)
- **monitor_order**: 监管工单主表
- **monitor_flow**: 监管工单流程表
- **monitor_install**: 上门安装详情
- **monitor_install_reservation**: 安装预约详情
- **monitor_on_scene_inspection**: 现场勘察详情
- **monitor_recycle**: 上门回收详情
- **monitor_recycle_reservation**: 回收预约详情
- **monitor_access_info**: 监控接入详情

### 3. 人工智能管理 (AIM)
- **aim_firm**: 人工智能管理厂商表
- **aim_firm_scene_management**: 厂商场景管理
- **aim_scene_management**: AI场景管理
- **aim_scene_category_management**: 场景类别管理表
- **aim_scene_firm_relationship**: 场景-厂商场景关联表

### 4. 事件监控 (Event)
- **event_order**: 事件工单表
- **event_order_source**: 事件来源表
- **event_order_type**: 监控事件类型
- **event_camera_point**: 摄像头信息表
- **event_camera_point_device**: 摄像头设备关联表
- **event_camera_point_type**: 摄像头类型表
- **event_model_type_config**: 设备型号配置表

### 5. 设备管理 (Device)
- **device_camera_config**: 摄像头配置表
- **device_camera_type**: 摄像头类型表
- **open_ai_camera**: 开放AI摄像头表
- **aiot_event**: 监控事件表
- **aiot_scene**: aiot违规类型场景
- **aiot_category**: aiot场景分类

### 6. 组织架构 (Organization)
- **xsgc_organization**: 小散工程组织表
- **xsgc_business_members**: 业务人员表
- **xsgc_business_members_relate**: 业务人员客户关联表
- **xsgc_business_members_type**: 业务人员职位
- **xsgc_customer_info**: 客户信息表
- **xsgc_customer_service_record**: 客户服务开通记录
- **xsgc_subscription**: 套餐表

### 7. 系统管理 (System)
- **sys_users**: 用户表
- **sys_roles**: 用户组角色表
- **sys_permissions**: 权限信息表
- **sys_menu**: 菜单表
- **sys_app**: 应用表
- **sys_operate_log**: 操作日志表
- **sys_usr_login_log**: 用户登录日志

### 8. 通用模块 (Common)
- **com_org**: 组织机构表
- **com_region**: 通用地区表
- **com_dictionary**: 数据字典表
- **com_upload_resource**: 文件上传资源表
- **file_upload_resource**: 文件上传资源表
- **gis_poi**: GIS点位表
- **gis_fence**: GIS围栏表

### 9. 内容管理 (CMS)
- **ncms_articles**: 文章表
- **ncms_category**: 文章分类表
- **ncms_tags**: 标签表
- **ncms_user_account**: 用户账户表
- **ncms_interaction**: 互动表
- **ncms_attachment**: 附件表

### 10. 微信公众号 (WeChat)
- **wx_userinfo**: 微信用户信息表
- **wx_cfg**: 微信配置表
- **wx_article**: 微信文章表
- **wx_menu**: 微信菜单表
- **wx_msg**: 微信消息表
- **wx_media**: 微信媒体资源表
- **wx_push_***: 微信推送相关表

### 11. 资源管理 (Resource)
- **resource_item**: 资源项目表
- **resource_category**: 资源分类表
- **resource_datasource**: 资源数据源表
- **resource_tag**: 资源标签表

### 12. 公共分享 (Public Share)
- **pubshare_***: 公共分享相关表群

## 数据库设计特点

1. **统一字段规范**：
   - `id`: 主键，使用bigint(20)
   - `is_deleted`: 软删除标记，0-否，1-是
   - `created_at`: 创建时间
   - `updated_at`: 更新时间
   - `deleted_at`: 删除时间

2. **编码规范**：
   - 使用utf8mb4字符集
   - 表名使用下划线分隔
   - 字段注释详细，便于理解业务含义

3. **业务模块化**：
   - 按业务模块划分表前缀
   - 核心业务围绕"小散工程"展开
   - 支持完整的工程生命周期管理

4. **关联关系**：
   - 大量使用关联表处理多对多关系
   - 支持树形结构（如分类、组织架构）
   - 地理位置信息集成（经纬度、地区关联）

## 开发建议

1. **实体类命名**：建议Java实体类按表名转换为驼峰命名
2. **业务逻辑**：围绕sporadic_project表构建核心业务逻辑
3. **权限控制**：利用sys_*表群实现完整的RBAC权限体系
4. **数据完整性**：注意关联表的数据一致性维护
5. **性能优化**：大表查询时注意索引使用和分页处理
