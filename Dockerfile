FROM hb.shencom.cn/scloud/centos:jdk8-skywalking-arthas

<NAME_EMAIL>

COPY target/*.jar /app.jar

ENV JAVA_OPTS="\
-XX:+UseG1GC \
-XX:+UseContainerSupport \
-XX:InitialRAMPercentage=40.0 \
-XX:MinRAMPercentage=40.0 \
-XX:MaxRAMPercentage=40.0 \
-XX:MaxGCPauseMillis=100 \
-XX:MaxMetaspaceSize=256m \
-XX:CompressedClassSpaceSize=128m \
-XX:MaxDirectMemorySize=64m \
-XX:InitialCodeCacheSize=64m \
-XX:MetaspaceSize=128m \
-XX:ReservedCodeCacheSize=64m \
-XX:+HeapDumpOnOutOfMemoryError \
-XX:HeapDumpPath=./ \
-XX:+PrintGCDetails \
-XX:+PrintGCTimeStamps \
-XX:+PrintGCDateStamps \
-Xloggc:./gc.log \
-XX:+UseGCLogFileRotation \
-XX:NumberOfGCLogFiles=5 \
-XX:GCLogFileSize=3M \
-XX:+PrintHeapAtGC \
-XX:+PrintTenuringDistribution"

ENV JAVA_AGENT="-javaagent:/usr/local/agent/skywalking-agent.jar"

ENTRYPOINT ["sh", "-c" ]
CMD ["java ${JAVA_AGENT} ${JAVA_OPTS} -jar /app.jar && 1"]